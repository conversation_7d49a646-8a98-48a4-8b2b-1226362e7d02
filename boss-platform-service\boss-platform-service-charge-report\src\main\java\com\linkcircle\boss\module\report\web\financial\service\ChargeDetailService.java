package com.linkcircle.boss.module.report.web.financial.service;

import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.module.report.web.dynamic.model.dto.FieldMetaDTO;
import com.linkcircle.boss.module.report.web.dynamic.model.vo.ShowVo;
import com.linkcircle.boss.module.report.web.financial.model.dto.ChargeColumnReqDTO;
import com.linkcircle.boss.module.report.web.financial.model.dto.ChargeDetailReqDTO;
import com.linkcircle.boss.module.report.web.financial.model.dto.ChargeDynamicReqDTO;
import com.linkcircle.boss.module.report.web.financial.model.dto.ChargeFinancialReqDTO;
import com.linkcircle.boss.module.report.web.financial.model.vo.ChargeDetailTemplateColumnVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/20 11:27
 * @description 财务明细查询服务
 */
public interface ChargeDetailService {

    PageResult<Map<String,Object>> pageQuery(ChargeDynamicReqDTO detailReqDTO, boolean isExport);


    //ChargeDetailDynamicHeadDataVO financialExcelHead(ChargeDetailReqDTO detailReqDTO);

    void submitTask(ChargeDynamicReqDTO detailReqDTO);

    List<ShowVo> commonQuery(ChargeColumnReqDTO detailReqDTO);


    List<FieldMetaDTO> exportFields(ChargeDynamicReqDTO reqDTO);

    List<ChargeDetailTemplateColumnVO> exportHeader(ChargeDynamicReqDTO reqDTO);

    PageResult<Map<String, Object>> pageFinancialQuery(ChargeFinancialReqDTO detailReqDTO, boolean b);

    void submitFinancialTask(ChargeFinancialReqDTO detailReqDTO);
}
