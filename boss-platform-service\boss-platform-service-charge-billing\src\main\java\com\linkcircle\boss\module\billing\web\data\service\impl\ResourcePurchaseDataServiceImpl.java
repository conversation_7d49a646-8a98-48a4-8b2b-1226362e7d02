package com.linkcircle.boss.module.billing.web.data.service.impl;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.util.cache.ChargeCacheUtils;
import com.linkcircle.boss.framework.common.util.cache.FunctionUtil;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.module.billing.api.cache.dto.CacheUpdateMessageDTO;
import com.linkcircle.boss.module.billing.web.cache.manager.LocalCacheManager;
import com.linkcircle.boss.module.crm.api.productservice.vo.ServiceCodeInfo;
import com.linkcircle.boss.module.billing.web.data.service.ResourcePurchaseDataService;
import com.linkcircle.boss.module.crm.api.supplier.purchase.ResourcePurchaseApi;
import com.linkcircle.boss.module.crm.api.supplier.purchase.vo.ResourcePurchaseVO;
import io.github.kk01001.redis.RedissonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-06-24 15:28
 * @description 资源采购数据服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ResourcePurchaseDataServiceImpl implements ResourcePurchaseDataService {

    private final RedissonUtil redissonUtil;
    private final ResourcePurchaseApi resourcePurchaseApi;
    private final LocalCacheManager localCacheManager;

    /**
     * 缓存过期时间 - 8小时
     */
    private static final Duration CACHE_EXPIRE_TIME = Duration.ofHours(8);

    @Override
    public List<ResourcePurchaseVO> getAccountResourcePurchases(Long accountId) {
        String accountIdStr = String.valueOf(accountId);

        // 1. 先从本地缓存查询
        Optional<List<ResourcePurchaseVO>> localCached = localCacheManager.getList(
                CacheUpdateMessageDTO.Type.ACCOUNT_RESOURCE_PURCHASE,
                accountIdStr
        );
        if (localCached.isPresent()) {
            log.debug("从本地缓存获取账户资源采购列表: accountId={}", accountId);
            return localCached.get();
        }

        // 2. 本地缓存未命中，从Redis缓存查询
        String redisCacheKey = ChargeCacheUtils.getAccountResourcePurchasesKey(accountId);
        Optional<List<ResourcePurchaseVO>> optional = FunctionUtil.getCachedOrLoadDb(redisCacheKey,
                () -> {
                    String data = redissonUtil.get(redisCacheKey);
                    if (StrUtil.isEmpty(data)) {
                        return Optional.empty();
                    }
                    return Optional.ofNullable(JsonUtils.parseObjectQuietly(data, new TypeReference<>() {
                    }));
                },
                () -> {
                    CommonResult<List<ResourcePurchaseVO>> result = resourcePurchaseApi.getAccountSubscriptionsList(accountId);
                    return Optional.of(result.getCheckedData());
                },
                data -> {
                    redissonUtil.set(redisCacheKey, JsonUtils.toJsonString(data), CACHE_EXPIRE_TIME);
                });

        List<ResourcePurchaseVO> purchases = optional.orElse(Collections.emptyList());

        // 3. 更新本地缓存
        localCacheManager.put(CacheUpdateMessageDTO.Type.ACCOUNT_RESOURCE_PURCHASE, accountIdStr, purchases);
        log.debug("更新本地缓存账户资源采购列表: accountId={}", accountId);

        return purchases;
    }

    @Override
    public Optional<ResourcePurchaseVO> getPurchaseDetail(Long purchaseId) {
        String cacheKey = ChargeCacheUtils.getResourcePurchaseDetailKey(purchaseId);
        return FunctionUtil.getCachedOrLoadDb(cacheKey,
                () -> Optional.ofNullable(redissonUtil.get(cacheKey, ResourcePurchaseVO.class)),
                () -> {
                    CommonResult<ResourcePurchaseVO> result = resourcePurchaseApi.getPurchaseDetail(purchaseId);
                    if (Objects.isNull(result)) {
                        return Optional.empty();
                    }
                    return Optional.of(result.getCheckedData());
                },
                data -> {
                    redissonUtil.set(cacheKey, JsonUtils.toJsonString(data), CACHE_EXPIRE_TIME);
                });
    }

    @Override
    public List<Long> getAllPurchaseAccountIds(Integer paymentType) {
        try {
            CommonResult<List<Long>> result = resourcePurchaseApi.getAllPurchaseAccountIds(paymentType);
            if (Objects.isNull(result) || Objects.isNull(result.getData())) {
                log.warn("获取全部采购账户ID列表失败，支付类型: {}", paymentType);
                return List.of();
            }
            List<Long> accountIds = result.getCheckedData();
            log.debug("获取全部采购账户ID列表成功，支付类型: {}, 数量: {}", paymentType, accountIds.size());
            return accountIds;
        } catch (Exception e) {
            log.error("获取全部采购账户ID列表异常，支付类型: {}", paymentType, e);
            return List.of();
        }
    }

    @Override
    public List<ResourcePurchaseVO> getPurchasesListByBillingType(Integer billingType) {
        try {
            CommonResult<List<ResourcePurchaseVO>> result = resourcePurchaseApi.getPurchasesListByBillingType(billingType);
            if (Objects.isNull(result)) {
                log.warn("根据计费类型获取采购列表失败，计费类型: {}", billingType);
                return List.of();
            }
            List<ResourcePurchaseVO> purchases = result.getCheckedData();
            log.debug("根据计费类型获取采购列表成功，计费类型: {}, 数量: {}", billingType, purchases.size());
            return purchases;
        } catch (Exception e) {
            log.error("根据计费类型获取采购列表异常，计费类型: {}", billingType, e);
            return List.of();
        }
    }

    @Override
    public List<ServiceCodeInfo> getAllActiveServiceCodes() {
        log.debug("获取所有活跃的供应商服务编码信息");

        try {
            // 直接调用远程API获取
            CommonResult<List<ServiceCodeInfo>> result = resourcePurchaseApi.getAllActiveServiceCodes();

            if (result.isSuccess() && result.getData() != null) {
                List<ServiceCodeInfo> serviceCodeInfos = result.getData();
                log.debug("从远程API获取到{}个供应商服务编码信息", serviceCodeInfos.size());
                return serviceCodeInfos;
            }

            log.warn("获取供应商服务编码信息失败或数据为空: {}", result.getMsg());
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("获取所有活跃的供应商服务编码信息异常", e);
            return new ArrayList<>();
        }
    }

}
