package com.linkcircle.boss.module.billing.web.data.service.impl;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.util.cache.ChargeCacheUtils;
import com.linkcircle.boss.framework.common.util.cache.FunctionUtil;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.module.billing.api.cache.dto.CacheUpdateMessageDTO;
import com.linkcircle.boss.module.billing.web.cache.manager.LocalCacheManager;
import com.linkcircle.boss.module.billing.web.data.model.dto.ServiceCodeInfo;
import com.linkcircle.boss.module.billing.web.data.service.ResourcePurchaseDataService;
import com.linkcircle.boss.module.crm.api.supplier.purchase.ResourcePurchaseApi;
import com.linkcircle.boss.module.crm.api.supplier.purchase.vo.ResourcePurchaseVO;
import io.github.kk01001.redis.RedissonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-06-24 15:28
 * @description 资源采购数据服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ResourcePurchaseDataServiceImpl implements ResourcePurchaseDataService {

    private final RedissonUtil redissonUtil;
    private final ResourcePurchaseApi resourcePurchaseApi;
    private final LocalCacheManager localCacheManager;

    /**
     * 缓存过期时间 - 8小时
     */
    private static final Duration CACHE_EXPIRE_TIME = Duration.ofHours(8);

    @Override
    public List<ResourcePurchaseVO> getAccountResourcePurchases(Long accountId) {
        String accountIdStr = String.valueOf(accountId);

        // 1. 先从本地缓存查询
        Optional<List<ResourcePurchaseVO>> localCached = localCacheManager.getList(
                CacheUpdateMessageDTO.Type.ACCOUNT_RESOURCE_PURCHASE,
                accountIdStr
        );
        if (localCached.isPresent()) {
            log.debug("从本地缓存获取账户资源采购列表: accountId={}", accountId);
            return localCached.get();
        }

        // 2. 本地缓存未命中，从Redis缓存查询
        String redisCacheKey = ChargeCacheUtils.getAccountResourcePurchasesKey(accountId);
        Optional<List<ResourcePurchaseVO>> optional = FunctionUtil.getCachedOrLoadDb(redisCacheKey,
                () -> {
                    String data = redissonUtil.get(redisCacheKey);
                    if (StrUtil.isEmpty(data)) {
                        return Optional.empty();
                    }
                    return Optional.ofNullable(JsonUtils.parseObjectQuietly(data, new TypeReference<>() {
                    }));
                },
                () -> {
                    CommonResult<List<ResourcePurchaseVO>> result = resourcePurchaseApi.getAccountSubscriptionsList(accountId);
                    return Optional.of(result.getCheckedData());
                },
                data -> {
                    redissonUtil.set(redisCacheKey, JsonUtils.toJsonString(data), CACHE_EXPIRE_TIME);
                });

        List<ResourcePurchaseVO> purchases = optional.orElse(Collections.emptyList());

        // 3. 更新本地缓存
        localCacheManager.put(CacheUpdateMessageDTO.Type.ACCOUNT_RESOURCE_PURCHASE, accountIdStr, purchases);
        log.debug("更新本地缓存账户资源采购列表: accountId={}", accountId);

        return purchases;
    }

    @Override
    public Optional<ResourcePurchaseVO> getPurchaseDetail(Long purchaseId) {
        String cacheKey = ChargeCacheUtils.getResourcePurchaseDetailKey(purchaseId);
        return FunctionUtil.getCachedOrLoadDb(cacheKey,
                () -> Optional.ofNullable(redissonUtil.get(cacheKey, ResourcePurchaseVO.class)),
                () -> {
                    CommonResult<ResourcePurchaseVO> result = resourcePurchaseApi.getPurchaseDetail(purchaseId);
                    if (Objects.isNull(result)) {
                        return Optional.empty();
                    }
                    return Optional.of(result.getCheckedData());
                },
                data -> {
                    redissonUtil.set(cacheKey, JsonUtils.toJsonString(data), CACHE_EXPIRE_TIME);
                });
    }

    @Override
    public List<Long> getAllPurchaseAccountIds(Integer paymentType) {
        try {
            CommonResult<List<Long>> result = resourcePurchaseApi.getAllPurchaseAccountIds(paymentType);
            if (Objects.isNull(result) || Objects.isNull(result.getData())) {
                log.warn("获取全部采购账户ID列表失败，支付类型: {}", paymentType);
                return List.of();
            }
            List<Long> accountIds = result.getCheckedData();
            log.debug("获取全部采购账户ID列表成功，支付类型: {}, 数量: {}", paymentType, accountIds.size());
            return accountIds;
        } catch (Exception e) {
            log.error("获取全部采购账户ID列表异常，支付类型: {}", paymentType, e);
            return List.of();
        }
    }

    @Override
    public List<ResourcePurchaseVO> getPurchasesListByBillingType(Integer billingType) {
        try {
            CommonResult<List<ResourcePurchaseVO>> result = resourcePurchaseApi.getPurchasesListByBillingType(billingType);
            if (Objects.isNull(result)) {
                log.warn("根据计费类型获取采购列表失败，计费类型: {}", billingType);
                return List.of();
            }
            List<ResourcePurchaseVO> purchases = result.getCheckedData();
            log.debug("根据计费类型获取采购列表成功，计费类型: {}, 数量: {}", billingType, purchases.size());
            return purchases;
        } catch (Exception e) {
            log.error("根据计费类型获取采购列表异常，计费类型: {}", billingType, e);
            return List.of();
        }
    }

    @Override
    public List<ServiceCodeInfo> getAllActiveServiceCodes() {
        String cacheKey = ChargeCacheUtils.buildServiceCodeCacheKey() + ":supplier";
        log.debug("获取所有活跃的供应商服务编码信息，缓存键: {}", cacheKey);

        try {
            // 先从Redis缓存查询
            String cachedData = redissonUtil.get(cacheKey);
            if (StrUtil.isNotBlank(cachedData)) {
                List<ServiceCodeInfo> serviceCodeInfos = JsonUtils.parseObject(cachedData, new TypeReference<List<ServiceCodeInfo>>() {});
                if (serviceCodeInfos != null && !serviceCodeInfos.isEmpty()) {
                    log.debug("从缓存获取到{}个供应商服务编码信息", serviceCodeInfos.size());
                    return serviceCodeInfos;
                }
            }

            // 缓存未命中，从远程API获取
            List<ServiceCodeInfo> serviceCodeInfos = fetchAllActiveServiceCodesFromApi();

            // 缓存结果
            if (!serviceCodeInfos.isEmpty()) {
                redissonUtil.set(cacheKey, JsonUtils.toJsonString(serviceCodeInfos), Duration.ofHours(1));
                log.debug("已缓存{}个供应商服务编码信息", serviceCodeInfos.size());
            }

            return serviceCodeInfos;
        } catch (Exception e) {
            log.error("获取所有活跃的供应商服务编码信息异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 从远程API获取所有活跃的服务编码信息
     */
    private List<ServiceCodeInfo> fetchAllActiveServiceCodesFromApi() {
        log.debug("从远程API获取所有活跃的供应商服务编码信息");

        try {
            // 获取所有活跃的资源采购信息（预付费和后付费）
            List<ServiceCodeInfo> allServiceCodes = new ArrayList<>();

            // 获取资源采购的服务编码
            List<ResourcePurchaseVO> purchases = getPurchasesListByBillingType(null);
            List<ServiceCodeInfo> purchaseServiceCodes = extractServiceCodesFromPurchases(purchases);
            allServiceCodes.addAll(purchaseServiceCodes);

            log.debug("从资源采购信息中提取到{}个服务编码", allServiceCodes.size());

            // 去重并返回
            return allServiceCodes.stream()
                    .filter(Objects::nonNull)
                    .filter(info -> StrUtil.isNotBlank(info.getServiceCode()))
                    .collect(Collectors.toMap(
                            info -> info.getServiceCode() + "_" + info.getPaymentType(),
                            info -> info,
                            (existing, replacement) -> existing))
                    .values()
                    .stream()
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("从远程API获取供应商服务编码信息异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 从资源采购信息中提取服务编码
     */
    private List<ServiceCodeInfo> extractServiceCodesFromPurchases(List<ResourcePurchaseVO> purchases) {
        List<ServiceCodeInfo> serviceCodeInfos = new ArrayList<>();

        for (ResourcePurchaseVO purchase : purchases) {
            if (purchase.getDetails() == null) {
                continue;
            }

            for (ResourcePurchaseVO.Detail detail : purchase.getDetails()) {
                if (StrUtil.isNotBlank(detail.getResourceServiceCode())) {
                    ServiceCodeInfo serviceCodeInfo = ServiceCodeInfo.builder()
                            .serviceCode(detail.getResourceServiceCode())
                            .paymentType(purchase.getPaymentType())
                            .build();
                    serviceCodeInfos.add(serviceCodeInfo);
                }
            }
        }

        return serviceCodeInfos;
    }

}
