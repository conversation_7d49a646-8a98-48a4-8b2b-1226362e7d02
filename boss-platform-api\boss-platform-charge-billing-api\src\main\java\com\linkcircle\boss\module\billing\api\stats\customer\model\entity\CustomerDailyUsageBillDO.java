package com.linkcircle.boss.module.billing.api.stats.customer.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-18 08:58
 * @description 客户每日使用量账单表
 */
@TableName("customer_daily_usage_bill")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerDailyUsageBillDO {

    /**
     * 主键 ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建时间戳
     */
    private Long createTime;

    /**
     * 账单日期(yyyyMMddhh)
     */
    private String billDate;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 客户 ID
     */
    private Long customerId;

    /**
     * 账户 ID
     */
    private Long accountId;

    /**
     * 产品 ID
     */
    private Long productId;

    /**
     * 服务 ID
     */
    private Long serviceId;

    /**
     * 使用量
     */
    @TableField("usage_count")
    private BigDecimal usage;

    /**
     * 使用量单位
     */
    private String usageUnit;

    /**
     * 现金消费金额
     */
    private BigDecimal cashAmount;

    /**
     * 现金消费金额货币单位
     */
    private String cashAmountCurrency;

    /**
     * 积分消费金额
     */
    private BigDecimal pointAmount;
}
