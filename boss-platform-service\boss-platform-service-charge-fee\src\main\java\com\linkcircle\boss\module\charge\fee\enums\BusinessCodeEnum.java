package com.linkcircle.boss.module.charge.fee.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/7/23 17:20
 */
@Getter
@AllArgsConstructor
public enum BusinessCodeEnum {
    BILL_INVOICE_ID_GENERATE("BILL_INVOICE_ID_GENERATE",2, "账单-发票号码生成"),
    BILL_ID_GENERATE("BILL_ID_GENERATE",1, "账单号码生成");
    private final String code;
    private final Integer id;
    private final String description;
}
