# 计费系统统计模块

## 概述

本模块实现了计费系统中客户和供应商的每日使用量统计功能，通过定时任务自动统计和汇总账单数据。

## 功能特性

### 1. 客户每日使用量统计
- **定时任务**: `CustomerDailyUsageStatsScheduledTask`
- **执行时间**: 每小时10分执行（`0 10 * * * ?`）
- **统计范围**: 前一小时的客户收入账单数据
- **数据源**: 预付费和后付费收入账单详情表
- **输出表**: `customer_daily_usage_bill_{year}`

### 2. 供应商每日使用量统计
- **定时任务**: `SupplierDailyUsageStatsScheduledTask`
- **执行时间**: 每小时20分执行（`0 20 * * * ?`）
- **统计范围**: 前一小时的供应商成本账单数据
- **数据源**: 预付费和后付费成本账单详情表
- **输出表**: `supplier_daily_usage_bill_{year}`

## 技术实现

### 分表查询
使用ShardingSphere的HintManager进行分表查询：
```java
HitBusinessTimeDTO businessTimeDTO = HitBusinessTimeDTO.build(List.of(startTime, endTime), List.of(serviceCode));
try (HintManager hintManager = HintManager.getInstance()) {
    hintManager.addTableShardingValue(LogicTableConstant.PREPAID_INCOME_BILL_DETAIL, businessTimeDTO);
    // 执行查询
}
```

### 统计逻辑
1. **查询serviceCode**: 首先查询指定时间范围内所有存在的serviceCode
2. **分serviceCode统计**: 遍历每个serviceCode，分别统计预付费和后付费数据
3. **按小时聚合**: 使用`DATE_FORMAT(FROM_UNIXTIME(business_time / 1000), '%Y%m%d%H')`按小时聚合
4. **重复数据检查**: 插入前检查是否已统计过，避免重复统计

### 重复数据检查规则
- **客户统计**: 根据 `billDate + accountId + serviceId` 判断
- **供应商统计**: 根据 `billDate + accountId + resourceServiceId` 判断

## 数据库表结构

### 客户每日使用量表 (customer_daily_usage_bill_{year})
```sql
CREATE TABLE `customer_daily_usage_bill_${year}` (
    `id` BIGINT(20) NOT NULL COMMENT '主键 ID',
    `create_time` BIGINT(20) NOT NULL COMMENT '创建时间戳',
    `bill_date` VARCHAR(16) NOT NULL COMMENT '账单日期(yyyyMMddhh)',
    `timezone` VARCHAR(32) NOT NULL COMMENT '时区',
    `customer_id` BIGINT(20) NOT NULL COMMENT '客户 ID',
    `account_id` BIGINT(20) NOT NULL COMMENT '账户 ID',
    `product_id` BIGINT(20) NOT NULL COMMENT '产品 ID',
    `service_id` BIGINT(20) NOT NULL COMMENT '服务 ID',
    `usage_count` DECIMAL(18, 6) DEFAULT 0 COMMENT '使用量',
    `usage_unit` VARCHAR(16) DEFAULT NULL COMMENT '使用量单位',
    `cash_amount` DECIMAL(18, 6) DEFAULT 0 COMMENT '现金消费金额',
    `cash_amount_currency` VARCHAR(3) DEFAULT 0 COMMENT '现金消费金额货币单位',
    `point_amount` DECIMAL(18, 0) DEFAULT 0 COMMENT '积分消费金额'
);
```

### 供应商每日使用量表 (supplier_daily_usage_bill_{year})
```sql
CREATE TABLE `supplier_daily_usage_bill_${year}` (
    `id` BIGINT(20) NOT NULL COMMENT '主键 ID',
    `create_time` BIGINT(20) NOT NULL COMMENT '创建时间戳',
    `bill_date` VARCHAR(16) NOT NULL COMMENT '账单日期(yyyyMMddhh)',
    `supplier_id` BIGINT(20) NOT NULL COMMENT '供应商 ID',
    `account_id` BIGINT(20) NOT NULL COMMENT '账户 ID',
    `resource_service_id` BIGINT(20) NOT NULL COMMENT '资源服务 ID',
    `usage_count` DECIMAL(18, 6) DEFAULT 0 COMMENT '使用量',
    `usage_unit` VARCHAR(16) DEFAULT NULL COMMENT '使用量单位',
    `cash_amount` DECIMAL(18, 6) DEFAULT 0 COMMENT '现金消费金额',
    `cash_amount_currency` VARCHAR(3) DEFAULT 0 COMMENT '现金消费金额货币单位'
);
```

## 文件结构

```
stats/
├── scheduled/                          # 定时任务
│   ├── CustomerDailyUsageStatsScheduledTask.java
│   └── SupplierDailyUsageStatsScheduledTask.java
├── service/                            # 服务接口
│   ├── CustomerDailyUsageStatsService.java
│   ├── SupplierDailyUsageStatsService.java
│   └── impl/                          # 服务实现
│       ├── CustomerDailyUsageStatsServiceImpl.java
│       └── SupplierDailyUsageStatsServiceImpl.java
├── mapper/                            # 数据访问层
│   ├── CustomerDailyUsageBillMapper.java
│   └── SupplierDailyUsageBillMapper.java
└── README.md                          # 说明文档
```

## 配置说明

### XXL-Job配置
- **客户统计任务**: `customerDailyUsageStatsHandler`
- **供应商统计任务**: `supplierDailyUsageStatsHandler`
- **执行策略**: `LEAST_FREQUENTLY_USED`
- **失败策略**: `DO_NOTHING`

### 分表配置
分表逻辑表名常量定义在 `LogicTableConstant` 中：
- `PREPAID_INCOME_BILL_DETAIL`: 预付费收入账单详情表
- `POSTPAID_INCOME_BILL_DETAIL`: 后付费收入账单详情表
- `PREPAID_COST_BILL_DETAIL`: 预付费成本账单详情表
- `POSTPAID_COST_BILL_DETAIL`: 后付费成本账单详情表

## 监控和日志

### 日志记录
- 任务开始和结束时间
- 处理的serviceCode数量和列表
- 每个serviceCode的处理结果
- 异常信息和错误详情

### 性能监控
- 统计处理的总记录数
- 成功和失败的serviceCode数量
- 任务执行时长

## 注意事项

1. **时间范围**: 统计前一小时的数据，避免数据延迟问题
2. **分表查询**: 必须使用HintManager指定分表策略
3. **重复检查**: 插入前检查重复数据，确保数据一致性
4. **事务管理**: 使用`@Transactional`确保数据一致性
5. **异常处理**: 单个serviceCode失败不影响其他serviceCode的处理
