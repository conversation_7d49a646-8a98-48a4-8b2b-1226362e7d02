# 鉴权

## 签名生成规则 (sign)

签名用于验证请求的完整性和合法性。签名生成步骤如下：

1. **去除值为空或 null 的参数**: 只有参数有值的才参与sign生成

2. **参数排序**: 将请求体中的所有字段（除 `sign` 外）按字段名 ASCII 码从小到大排序。

3. **拼接字符串**: 将排序后的字段名和值拼接成字符串，格式为 `key1=value1&key2=value2`。对于嵌套对象（如 `data`），将 data 内的值取出继续排序。

4. **添加密钥**: 在拼接字符串末尾追加密钥（密钥由服务端提供，例如 `secret_key`）。

5. **生成签名**: 对拼接后的字符串进行 sha256  加密（小写）。

## sign 生成步骤

密钥为：`secret_key=abc123`

0. header参数

    ```http
    X-AppId: your-app-id
    X-Sign: request-signature
    X-Timestamp: *************
    X-Tenant: 1  
    ```

    参数 

    ```json 
    {
        "account_id": "acc123456",
        "product_id": "prod789",
        "service_id": "svc456",
        "business_time": "**********",
        "usage": "100",
        "callback_url": "https://example.com/callback",
        "data": {
            "caller_number": "***********"
        },
        "request_id": "req123456789",
        "business_id": "biz987654",
    }
    ```

    

1. 按字段名排序：

   > 将 data 数据取出, Header参数 X-Timestamp 要参数参与签名, key为timestamp

   ```
   account_id=acc123456
   business_id=biz987654
   business_time=**********
   callback_url=https://example.com/callback
   caller_number=***********
   product_id=prod789
   request_id=req123456789
   service_id=svc456
   usage=100
   timestamp=**********123
   ```

2. 拼接字符串：

   ```
   
   account_id=acc123456&business_id=biz987654&business_time=**********&callback_url=https://example.com/callback&caller_number=***********&product_id=prod789&request_id=req123456789&service_id=svc456&timestamp=**********123&usage=100
   ```

3. 追加密钥：

   ```
   
   account_id=acc123456&business_id=biz987654&business_time=**********&callback_url=https://example.com/callback&caller_number=***********&product_id=prod789&request_id=req123456789&service_id=svc456&timestamp=**********123&usage=100abc123&secret_key=abc123
   ```

4. sha256 加密：

   ```bash
   
   sign = sha256(account_id=acc123456&business_id=biz987654&business_time=**********&callback_url=https://example.com/callback&caller_number=***********&product_id=prod789&request_id=req123456789&service_id=svc456&timestamp=**********123&usage=100abc123&secret_key=abc123)
   ```

## Java代码demo

> 基于hutool

```java
public static void main(String[] args) {
       long timestamp = System.currentTimeMillis();
        // 创建主参数 HashMap
        HashMap<String, Object> requestParams = new HashMap<>();
        requestParams.put("account_id", "acc123456"); // 账户ID
        requestParams.put("product_id", "prod789");   // 产品ID
        requestParams.put("service_id", "svc456");    // 服务ID
        requestParams.put("business_time", "**********"); // 业务时间戳
        requestParams.put("usage", "100");            // 消耗量
        requestParams.put("callback_url", "https://example.com/callback"); // 回调URL
        requestParams.put("request_id", "req123456789"); // 请求ID
        requestParams.put("business_id", "biz987654");   // 业务唯一ID

        // 创建 data 业务参数 HashMap
        HashMap<String, Object> data = new HashMap<>();
        data.put("caller_number", "***********"); // 主叫号码

        // 将 data 添加到 requestParams 中
        requestParams.put("data", data);


        HashMap<String, Object> signParams = new HashMap<>(requestParams);
        signParams.putAll(data);
        signParams.remove("data");
        requestParams.put("timestamp", timestamp); // header请求时间戳

        //  秘钥, 从计费平台申请
        String secretKey= "1231412";
        String sign = SignUtil.signParams(DigestAlgorithm.SHA256, signParams, "&", "=", true, secretKey);


        String url = "计费平台提供的url";
        // 应用id, 从计费平台申请
        String appId = "your-app-id";

        try (HttpResponse response = HttpRequest.post(url)
                .body(JacksonUtil.toJson(requestParams))
                .header("X-AppId", appId)
                .header("X-Sign", sign)
                .header("X-Timestamp", timestamp)
                .header("X-Tenant", 1)
                .timeout(3000)
                .execute()) {
            System.out.println(response.body());
        }
    }
```

   

# 错误码

| 错误码 | 描述           |
| ------ | -------------- |
| 0      | 成功           |
| 1001   | 参数缺失       |
| 1002   | 签名验证失败   |
| 1003   | 请求时间戳过期 |
| 1004   | 业务 ID 重复     |
| 1005   | 请求 ID 重复     |
| 5000   | 服务器内部错误 |

---

# 注意事项

1. 请求时间戳（`timestamp`）与服务器时间差不得超过 5 分钟。
2. 签名（`sign`）需严格按照签名生成规则计算，否则会导致验证失败。
3. `business_id` 必须全局唯一，重复提交将返回错误码 1004。
4. 回调 URL（`callback_url`）需确保可访问，服务端会在扣费完成后异步通知。

# 计费平台结果的数据

| 字段名              | 类型   | 必填   | 描述                                        |
| ------------------- | ------ | ------ | ------------------------------------------- |
| X-AppId             | String | String | 应用Id, 放在header, 计费平台提供            |
| secret_key          | String | String | 应用秘钥, 计费平台提供 用于生成参数签名sign |
| account_id          | String | 是     | 账户 ID , 计费平台提供                      |
| product_id          | String | 是     | 产品 ID, 计费平台提供                       |
| service_id          | String | 是     | 服务 ID, 计费平台提供                       |
| resource_id         | String | 是     | 资源 ID, 计费平台提供                       |
| resource_service_id | String | 是     | 资源服务 ID, 计费平台提供                   |



# Header参数

> 每个接口header参数都要传

| 字段名      | 类型   | 必填 | 描述                            | 示例值                                                       |
| ----------- | ------ | ---- | ------------------------------- | ------------------------------------------------------------ |
| X-AppId     | String | 是   | 应用Id, 计费平台提供            | "acc123456"                                                  |
| X-Tenant    | String | 是   | 租户id, 固定1                   | 1                                                            |
| X-Timestamp | String | 是   | 当前毫秒时间戳, 前后不超过5分钟 | **********123                                                |
| X-Sign      | String | 是   | 参数签名                        | f5be9e57772b299f17bdaff35ada501b439a16d63c77fb7b3e5808c218950d2c |

# 收入账单明细接口文档

## 接口概述

**接口名称**: 收入账单明细接口  
**接口路径**: `/charge-income-bill-detail`  
**请求方法**: POST  
**内容类型**: application/json  
**描述**: 用于提交收入账单明细并返回处理结果。接口需要对请求参数进行签名验证。

---

## 请求参数

### 请求体 (JSON)

| 字段名          | 类型   | 必填 | 描述                           | 示例值                     |
|-----------------|--------|------|--------------------------------|----------------------------|
| account_id      | String | 是   | 账户 ID                        | "acc123456"               |
| product_id      | String | 是   | 产品 ID                        | "prod789"                 |
| service_id      | String | 是   | 服务 ID                        | "svc456"                  |
| business_time   | Long | 是   | 业务产生的时间戳 (毫秒时间戳) | "**********123"           |
| usage           | String | 是   | 消耗量                        | "100"                     |
| usage_unit | String | 是 | 消耗量单位 | s |
| callback_url    | String | 否   | 扣费回调 URL                   | "https://example.com/callback" |
| data            | Object | 是   | 业务话单数据                  | 见下方 data 字段说明 不同产品 data 字段不同 |
| request_id      | String | 是   | 请求 ID                        | "req123456789"            |
| business_id     | String | 是   | 业务唯一 ID                    | "biz987654"               |

#### data 字段说明

| 字段名         | 类型   | 必填 | 描述           | 示例值       |
|----------------|--------|------|----------------|--------------|
| caller_number  | String | 是   | 主叫号码       | "***********" |

### 参数示例

```json
{
    "account_id": "acc123456",
    "product_id": "prod789",
    "service_id": "svc456",
    "business_time": "**********",
    "usage": "100",
    "callback_url": "https://example.com/callback",
    "data": {
        "caller_number": "***********"
    },
    "request_id": "req123456789",
    "business_id": "biz987654"
}
```

---

## 响应参数

### 响应体 (JSON)

| 字段名          | 类型   | 描述                           | 示例值                     |
|-----------------|--------|--------------------------------|----------------------------|
| code            | Integer | 响应状态码，0 表示成功         | 0                          |
| data            | Object  | 响应数据                       | 见下方 data 字段说明       |
| timestamp       | Long | 响应时间戳 (Unix 时间戳)       | "**********"              |

#### data 字段说明

| 字段名          | 类型   | 描述                           | 示例值                     |
|-----------------|--------|--------------------------------|----------------------------|
| bill_detail_id  | String | 账单明细 ID                    | "bill123456"              |
| account_id      | String | 账户 ID                        | "acc123456"               |
| product_id      | String | 产品 ID                        | "prod789"                 |
| service_id      | String | 服务 ID                        | "svc456"                  |
| request_id      | String | 请求 ID                        | "req123456789"            |
| business_id     | String | 业务唯一 ID                    | "biz987654"               |

### 响应示例

```json
{
    "code": 0,
    "data": {
        "bill_detail_id": "bill123456",
        "account_id": "acc123456",
        "product_id": "prod789",
        "service_id": "svc456",
        "request_id": "req123456789",
        "business_id": "biz987654",
        "timestamp": "**********"
    },
	"msg": "success"
}
```

---
# 收入账单扣费回调接口



# 成本账单明细接口文档

## 接口概述

**接口名称**: 成本账单明细接口  
**接口路径**: `/charge-cost-bill-detail`  
**请求方法**: POST  
**内容类型**: application/json  
**描述**: 用于提交成本账单明细并返回处理结果。接口需要对请求参数进行签名验证。

---

## 请求参数

### 请求体 (JSON)

| 字段名          | 类型   | 必填 | 描述                           | 示例值                     |
|-----------------|--------|------|--------------------------------|----------------------------|
| account_id      | String | 是   | 账户 ID                        | "acc123456"               |
| resource_id | String | 是   | 资源 ID                        | "prod789"                 |
| resource_service_id | String | 是   | 资源服务 ID                      | "svc456"                  |
| business_time   | Long | 是   | 业务产生的时间戳 (毫秒时间戳) | "**********123"           |
| usage           | String | 是   | 消耗量                        | "100"                     |
| usage_unit | String | 是 | 消耗量单位 | s |
| callback_url    | String | 是   | 扣费回调 URL                   | "https://example.com/callback" |
| data            | Object | 是   | 业务话单数据                  | 见下方 data 字段说明 不同产品 data 字段不同 |
| request_id      | String | 是   | 请求 ID                        | "req123456789"            |
| business_id     | String | 是   | 业务唯一 ID                    | "biz987654"               |

#### data 字段说明

| 字段名         | 类型   | 必填 | 描述           | 示例值       |
|----------------|--------|------|----------------|--------------|
| caller_number  | String | 是   | 主叫号码       | "***********" |

### 参数示例

```json
{
    "account_id": "acc123456",
    "resource_id": "资源id",
    "resource_service_id": "资源服务id",
    "business_time": "**********",
    "usage": "100",
    "callback_url": "https://example.com/callback",
    "data": {
        "caller_number": "***********"
    },
    "request_id": "req123456789",
    "business_id": "biz987654"
}
```

---

## 响应参数

### 响应体 (JSON)

| 字段名          | 类型   | 描述                           | 示例值                     |
|-----------------|--------|--------------------------------|----------------------------|
| code            | Integer | 响应状态码，0 表示成功         | 0                          |
| data            | Object  | 响应数据                       | 见下方 data 字段说明       |
| timestamp       | Long | 响应时间戳 (Unix 时间戳)       | "**********"              |

#### data 字段说明

| 字段名          | 类型   | 描述                           | 示例值                     |
|-----------------|--------|--------------------------------|----------------------------|
| bill_detail_id  | String | 账单明细 ID                    | "bill123456"              |
| account_id      | String | 账户 ID                        | "acc123456"               |
| resource_id         | String | 是          | 资源 ID        |
| resource_service_id | String | 是          | 资源服务 ID    |
| request_id      | String | 请求 ID                        | "req123456789"            |
| business_id     | String | 业务唯一 ID                    | "biz987654"               |

### 响应示例

```json
{
    "code": 0,
    "data": {
        "bill_detail_id": "bill123456",
        "account_id": "acc123456",
        "resource_id": "资源id",
        "resource_service_id": "资源服务id",
        "request_id": "req123456789",
        "business_id": "biz987654",
        "timestamp": "**********123"
    },
	"msg": "success"
}
```

# 成本账单回调接口





# 产品业务参数

