<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linkcircle.boss.module.report.web.bill.mapper.SupplierAccountBillDorisMapper">


    <select id="queryMonthBill"
            resultType="com.linkcircle.boss.module.report.web.bill.model.vo.supplier.SupplierAccountMonthBillRespVO">
        select left(bill_date, 6) billMonth,
               sum(cash_amount) cashAmount,
               any_value(cash_amount_currency) currencyCode,
               sum(point_amount) pointAmount
        from supplier_daily_usage_bill
        where supplier_id = #{req.supplierId}
        and account_id = #{req.accountId}
          and bill_date &gt;=  concat(#{dateMap.billYear},'000000')
          AND bill_date &lt; concat(#{dateMap.nextYear},'000000')
          and create_time &gt;= #{dateMap.createTimeMin}
          and create_time &lt;= #{dateMap.createTimeMax}
        GROUP BY left(bill_date, 6)
    </select>


    <select id="queryMonthBillDetail" resultType="com.linkcircle.boss.module.report.web.bill.model.vo.supplier.SupplierAccountMonthBillDetailRespVO">
        select resource_id resourceId, resource_service_id resourceServiceId,
        sum(cash_amount) cashAmount,
        any_value(cash_amount_currency) currencyCode,
        sum(point_amount) pointAmount,
        sum(`usage`) as usageCount,
        any_value(usage_unit) usageUnit
        from supplier_daily_usage_bill
        where supplier_id = #{req.supplierId}
        and account_id = #{req.accountId}
        and bill_date &gt;=  concat(#{dateMap.billMonth},'0000')
        AND bill_date &lt; concat(#{dateMap.nextMonth},'0000')
          and create_time &gt;= #{dateMap.createTimeMin}
          and create_time &lt;= #{dateMap.createTimeMax}
        group by resource_id, resource_service_id;

    </select>

    <select id="listBillDetail" resultType="com.linkcircle.boss.module.report.web.bill.model.vo.supplier.SupplierAccountBillDetailRespVO">
        select resource_id resourceId, resource_service_id resourceServiceId,
        cash_amount cashAmount,
        cash_amount_currency currencyCode,
        point_amount pointAmount,
        `usage` usageCount,
        usage_unit usageUnit,
        bill_date billDate
        from supplier_daily_usage_bill
        where supplier_id = #{req.supplierId}
          and account_id = #{req.accountId}
          <if test="req.resourceId!= null and req.resourceId != ''">
            and resource_id = #{req.resourceId}
          </if>
          <if test="req.resourceServiceId!= null and req.resourceServiceId != ''">
            and resource_service_id = #{req.resourceServiceId}
          </if>
        and bill_date &gt;=  concat(#{dateMap.billDate},'00')
        AND bill_date &lt; concat(#{dateMap.nextDate},'00')
        and create_time &gt;= #{dateMap.createTimeMin}
        and create_time &lt;= #{dateMap.createTimeMax}
    </select>

</mapper>