package com.linkcircle.boss.module.billing.web.stats.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-01-03 10:00
 * @description 每日使用量统计汇总VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DailyUsageStatsSummaryVO {

    /**
     * 账单日期
     */
    private String billDate;

    /**
     * 账户ID
     */
    private Long accountId;

    /**
     * 服务ID（客户侧）
     */
    private Long serviceId;

    /**
     * 资源服务ID（供应商侧）
     */
    private Long resourceServiceId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     *
     */
    private String timezone;

    /**
     * 计费消耗量
     */
    private BigDecimal chargeUsageCount;

    /**
     * 使用量
     */
    private BigDecimal usage;

    /**
     * 现金金额
     */
    private BigDecimal cashAmount;

    /**
     * 积分金额
     */
    private BigDecimal pointAmount;

    /**
     * 使用量单位
     */
    private String usageUnit;
}
