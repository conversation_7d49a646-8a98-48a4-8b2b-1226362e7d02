package com.linkcircle.boss.module.billing.web.stats.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-01-03 10:00
 * @description 每日使用量统计汇总VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DailyUsageStatsSummaryVO {

    /**
     * 账单日期
     */
    private String billDate;

    /**
     * 账户ID
     */
    private String accountId;

    /**
     * 服务ID（客户侧）
     */
    private String serviceId;

    /**
     * 资源服务ID（供应商侧）
     */
    private String resourceServiceId;

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 计费消耗量
     */
    private BigDecimal chargeUsageCount;

    /**
     * 使用量
     */
    private BigDecimal usage;

    /**
     * 现金金额
     */
    private BigDecimal cashAmount;

    /**
     * 积分金额
     */
    private BigDecimal pointAmount;
}
