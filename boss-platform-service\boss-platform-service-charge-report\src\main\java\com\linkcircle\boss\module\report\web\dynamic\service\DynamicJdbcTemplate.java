package com.linkcircle.boss.module.report.web.dynamic.service;

import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.framework.common.model.PageParam;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.module.report.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.report.web.dynamic.model.dto.DatabaseMetaDTO;
import com.linkcircle.boss.module.report.web.dynamic.model.dto.FieldMetaDTO;
import com.linkcircle.boss.module.report.web.dynamic.model.dto.TableMetaDTO;
import com.linkcircle.boss.module.report.web.dynamic.convert.DynamicConvert;
import com.linkcircle.boss.module.report.web.dynamic.model.vo.*;
import com.linkcircle.boss.module.report.web.dynamic.service.impl.CountDownLatchServiceImpl;
import com.linkcircle.boss.module.report.web.financial.model.dto.ChargeDetailTemplateColumnAssociationReqDTO;
import com.linkcircle.boss.module.report.web.financial.model.dto.ChargeDetailTemplateColumnReqDTO;
import com.linkcircle.boss.module.report.web.financial.model.dto.ChargeDynamicColumnDTO;
import com.linkcircle.boss.module.report.web.financial.model.vo.ChargeDetailTemplateColumnAssociationVO;
import com.linkcircle.boss.module.report.web.financial.model.vo.ChargeDetailTemplateColumnVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.shardingsphere.driver.jdbc.core.datasource.metadata.ShardingSphereDatabaseMetaData;
import org.apache.shardingsphere.infra.rule.ShardingSphereRule;
import org.apache.shardingsphere.infra.rule.attribute.RuleAttributes;
import org.apache.shardingsphere.infra.rule.attribute.datasource.aggregate.AggregatedDataSourceRuleAttribute;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import javax.sql.DataSource;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/11 10:15
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DynamicJdbcTemplate implements SmartInitializingSingleton {

    private final static Map<String, DbTemplateService> DB_TEMPLATE_SERVICE_MAP = new HashMap<>();
    private final DataSource dataSource;
    private final SqlSessionFactory sqlSessionFactory;
    private final CountDownLatchService countDownLatchService = new CountDownLatchServiceImpl();


    @Override
    public void afterSingletonsInstantiated() {
        DbTemplateService.TYPE_HANDLER_REGISTRY = sqlSessionFactory.getConfiguration().getTypeHandlerRegistry();
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData databaseMetaData = connection.getMetaData();
            ShardingSphereDatabaseMetaData metaData = null;
            if (databaseMetaData instanceof ShardingSphereDatabaseMetaData) {
                metaData = (ShardingSphereDatabaseMetaData) databaseMetaData;
                log.info("shardingSphere数据库元数据:{}", metaData);
            } else {
                metaData = databaseMetaData.unwrap(ShardingSphereDatabaseMetaData.class);
            }
            Field rules = ReflectionUtils.findField(metaData.getClass(), "rules");
            ReflectionUtils.makeAccessible(rules);
            Collection<ShardingSphereRule> shardingSphereRules = (Collection<ShardingSphereRule>) ReflectionUtils.getField(rules, metaData);
            for (ShardingSphereRule shardingSphereRule : shardingSphereRules) {
                RuleAttributes attributes = shardingSphereRule.getAttributes();
                Optional<AggregatedDataSourceRuleAttribute> attribute = attributes.findAttribute(AggregatedDataSourceRuleAttribute.class);
                if (attribute.isPresent()) {
                    Map<String, DataSource> ruleAttribute = attribute.get().getAggregatedDataSources();
                    if (MapUtils.isNotEmpty(ruleAttribute)) {
                        for (Map.Entry<String, DataSource> dataSourceEntry : ruleAttribute.entrySet()) {
                            String databaseName = dataSourceEntry.getKey();
                            DataSource dataSource = dataSourceEntry.getValue();
                            DbTemplateService dbTemplateService = DbTemplateServiceFactory.produce(dataSource, databaseName);
                            DB_TEMPLATE_SERVICE_MAP.put(databaseName, dbTemplateService);
                        }
                    }
                }
            }
            if (DB_TEMPLATE_SERVICE_MAP.isEmpty()) {
                throw new RuntimeException("加载系统的数据源配置为空");
            } else {
                Executors.newFixedThreadPool(1).execute(()-> {
                    try {
                        long startTime = System.currentTimeMillis();
                        log.info("开始加载数据源配置...");
                        countDownLatchService.executeTasks(DB_TEMPLATE_SERVICE_MAP.size(), DB_TEMPLATE_SERVICE_MAP.size(),
                                DB_TEMPLATE_SERVICE_MAP.values(), DbTemplateService::listTables);
                        long endTime = System.currentTimeMillis();
                        log.info("加载数据源配置完成,耗时:{}ms", endTime - startTime);
                    }catch (Exception e){
                        throw new RuntimeException("加载系统的数据源配置失败", e);
                    }
                });

            }
        } catch (SQLException e) {
            throw new RuntimeException("加载系统的数据源配置失败", e);
        }

    }

    /**
     * 获取数据库元数据列表
     *
     * @return 包含数据库元数据的列表
     */
    public List<DatabaseMetaVO> db() {
        return DB_TEMPLATE_SERVICE_MAP.values().stream().map(item -> new DatabaseMetaDTO(item.getLogicName(), item.getActualName()))
                .sorted(Comparator.comparing(DatabaseMetaDTO::getLogicName))
                .map(item -> DynamicConvert.INSTANCE.convertDatabase(item))
                .toList();
    }

    public List<TableMetaVO> table(String logicName) {
        DbTemplateService dbTemplateService = DB_TEMPLATE_SERVICE_MAP.get(logicName);
        return dbTemplateService == null ? new ArrayList<>() : dbTemplateService.getTABLES().stream().map(DynamicConvert.INSTANCE::convertTable).toList();
    }

    public TableMetaDTO table(String logicName, String tableName) {
        DbTemplateService dbTemplateService = DB_TEMPLATE_SERVICE_MAP.get(logicName);
        return dbTemplateService == null ? null : dbTemplateService.getTABLES().stream().filter(t -> t.getTableName().equals(tableName)).findFirst().orElse(null);
    }

    public List<FieldMetaVO> column(String logicName, String tableName) {
        DbTemplateService dbTemplateService = DB_TEMPLATE_SERVICE_MAP.get(logicName);
        if (dbTemplateService == null) {
            return new ArrayList<>();
        }
        for (TableMetaDTO table : dbTemplateService.getTABLES()) {
            if (table.getTableName().equals(tableName)) {
                return table.getColumns().stream().map(DynamicConvert.INSTANCE::convertField)
                        .peek(f -> {
                            f.setLogicName(table.getDatabase().getLogicName());
                            f.setTableName(table.getTableName());
                        })
                        .toList();
            }
        }
        return new ArrayList<>();
    }

    public void refresh(String logicName, String tableName) {
        if (StringUtils.isBlank(logicName) && StringUtils.isBlank(tableName)) {
            DB_TEMPLATE_SERVICE_MAP.forEach((k, v) -> {
                v.listTables();
            });
        } else if (StringUtils.isNotBlank(logicName) && StringUtils.isBlank(tableName)) {
            DbTemplateService dbTemplateService = DB_TEMPLATE_SERVICE_MAP.get(logicName);
            dbTemplateService.listTables();
        } else {
            DbTemplateService dbTemplateService = DB_TEMPLATE_SERVICE_MAP.get(logicName);
            dbTemplateService.refreshTableByName(tableName);
        }
    }

    public void checkExists(ChargeDetailTemplateColumnReqDTO column, String logicName, String tableName) {
        DbTemplateService dbTemplateService = DB_TEMPLATE_SERVICE_MAP.get(logicName);
        if (dbTemplateService == null) {
            log.info("logicName:{} not exists", logicName);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_DB_NOT_EXISTS);
        }
        Map<String, TableMetaDTO> tableMap = dbTemplateService.getTABLES().stream().collect(Collectors.toMap(TableMetaDTO::getTableName, t -> t));
        TableMetaDTO tableMetaDTO = tableMap.get(tableName);
        if (tableMetaDTO == null) {
            log.info("logicName:{},tableName:{} not exists", logicName, tableName);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_TABLE_NOT_EXISTS, logicName, tableName);
        }
        Map<String, FieldMetaDTO> fieldMap = tableMetaDTO.getColumns().stream().collect(Collectors.toMap(FieldMetaDTO::getColumnName, t -> t));
        FieldMetaDTO fieldMetaDTO = fieldMap.get(column.getColumnName());
        if (fieldMetaDTO == null) {
            log.info("logicName:{},tableName:{},columnName:{} not exists", logicName, tableName, column.getColumnName());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_COLUMN_NOT_EXISTS, logicName, tableName, column.getColumnName());
        }
        Integer queryType = column.getQueryType();
        if (Objects.equals(queryType, 1) && StringUtils.isNotBlank(column.getQueryAssociationName())) {
//            if (column.getQueryAssociationName() == null) {
//                log.info("logicName:{},tableName:{},columnName:{} ,queryAssociationName not exists", logicName, tableName, column.getColumnName(), column.getQueryAssociationName());
//                throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_QUERY_ASSOCIATION_COLUMN_EMPTY, logicName, tableName, column.getColumnName(), column.getQueryAssociationName());
//            }
            FieldMetaDTO associationField = fieldMap.get(column.getQueryAssociationName());
            if (associationField == null) {
                log.info("logicName:{},tableName:{},columnName:{} ,queryAssociationName not exists", logicName, tableName, column.getColumnName(), column.getQueryAssociationName());
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_QUERY_ASSOCIATION_COLUMN_NOT_EXISTS, logicName, tableName, column.getColumnName(), column.getQueryAssociationName());
            }
        }
        ChargeDetailTemplateColumnAssociationReqDTO association = column.getAssociation();
        if (association != null) {
            checkExists(association);
        }
    }

    public boolean checkExists(ChargeDetailTemplateColumnAssociationReqDTO associationReqDTO) {
        DbTemplateService dbTemplateService = DB_TEMPLATE_SERVICE_MAP.get(associationReqDTO.getLogicName());
        if (dbTemplateService == null) {
            log.info("logicName:{} not exists", associationReqDTO.getLogicName());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_DB_NOT_EXISTS);
        }
        Map<String, TableMetaDTO> tableMap = dbTemplateService.getTABLES().stream().collect(Collectors.toMap(TableMetaDTO::getTableName, t -> t));
        TableMetaDTO tableMetaDTO = tableMap.get(associationReqDTO.getTableName());
        if (tableMetaDTO == null) {
            log.info("logicName:{},tableName:{} not exists", associationReqDTO.getLogicName(), associationReqDTO.getTableName());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_TABLE_NOT_EXISTS, associationReqDTO.getLogicName(), associationReqDTO.getTableName());
        }
        Map<String, FieldMetaDTO> fieldMap = tableMetaDTO.getColumns().stream().collect(Collectors.toMap(FieldMetaDTO::getColumnName, t -> t));
        FieldMetaDTO associationField = fieldMap.get(associationReqDTO.getAssociationName());
        if (associationField == null) {
            log.info("logicName:{},tableName:{},columnName:{} not exists", associationReqDTO.getLogicName(), associationReqDTO.getTableName(), associationReqDTO.getAssociationName());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_COLUMN_NOT_EXISTS, associationReqDTO.getLogicName(), associationReqDTO.getTableName(), associationReqDTO.getAssociationName());
        }
        if(StringUtils.isBlank(associationReqDTO.getColumnName())){
              return  false;
        }else {
            FieldMetaDTO fieldMetaDTO = fieldMap.get(associationReqDTO.getColumnName());
            if (fieldMetaDTO == null) {
                log.info("logicName:{},tableName:{},columnName:{} not exists", associationReqDTO.getLogicName(), associationReqDTO.getTableName(), associationReqDTO.getColumnName());
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_COLUMN_NOT_EXISTS, associationReqDTO.getLogicName(), associationReqDTO.getTableName(), associationReqDTO.getAssociationName());
            }
        }
        return true;
    }

    public List<ShowVo> listValue(String logicName, String tableName, ChargeDetailTemplateColumnVO columnVO, ChargeDetailTemplateColumnVO associationColumnVO, List<ChargeDetailTemplateColumnVO> columns) {
        DbTemplateService dbTemplateService = DB_TEMPLATE_SERVICE_MAP.get(logicName);
        if (dbTemplateService == null) {
            log.info("logicName:{} not exists", logicName);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_DB_NOT_EXISTS);
        }
        List<ShowVo> showVos = new ArrayList<>();
        if (associationColumnVO != null) {
            Map<Object, List<Object>> associationValues = dbTemplateService.groupValue(tableName, columnVO.getQueryAssociationName(), columnVO.getColumnName());
            associationValues.forEach((key, value) -> {
                ShowVo showVo = new ShowVo();
                showVo.setValue(key);
                showVo.setLabel(key);
                showVo.setColumnName(columnVO.getColumnName());
                showVo.setExportName(columnVO.getExportName());

                List<ShowVo> children = new ArrayList<>();
                for (Object o : value) {
                    ShowVo child = new ShowVo();
                    child.setValue(o);
                    child.setLabel(o);
                    child.setColumnName(associationColumnVO.getColumnName());
                    child.setExportName(associationColumnVO.getExportName());
                    children.add(child);
                }
                showVo.setAssociations(children);

                showVos.add(showVo);
            });

            ChargeDetailTemplateColumnAssociationVO association = associationColumnVO.getAssociation();
            if (association != null) {
                DbTemplateService associationDbTemplateService = DB_TEMPLATE_SERVICE_MAP.get(association.getLogicName());
                if (associationDbTemplateService == null) {
                    log.info("logicName:{} not exists", logicName);
                    throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_DB_NOT_EXISTS);
                }
                Map<Object, Object> values = associationDbTemplateService.listValue(association.getTableName(), association.getColumnName(), association.getAssociationName());
                showVos.forEach(showVo -> {
                    List<ShowVo> children = showVo.getAssociations();
                    if (CollectionUtils.isNotEmpty(children)) {
                        for (ShowVo child : children) {
                            Object value = child.getValue();
                            Object label = values.get(value);
                            child.setLabel(label);
                        }
                    }
                });
            }

        } else {
            List<Object> columnValues = dbTemplateService.listValue(tableName, columnVO.getColumnName());
            for (Object value : columnValues) {
                ShowVo showVo = new ShowVo();
                showVo.setValue(value);
                showVo.setLabel(value);
                showVo.setColumnName(columnVO.getColumnName());
                showVo.setExportName(columnVO.getExportName());
                showVos.add(showVo);
            }
        }
        if (CollectionUtils.isNotEmpty(showVos) && columnVO.getAssociation() != null) {
            ChargeDetailTemplateColumnAssociationVO association = columnVO.getAssociation();
            DbTemplateService associationDbTemplateService = DB_TEMPLATE_SERVICE_MAP.get(association.getLogicName());
            if (associationDbTemplateService == null) {
                log.info("logicName:{} not exists", association.getLogicName());
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_DB_NOT_EXISTS);
            }
            Map<Object, Object> associationValues = associationDbTemplateService.listValue(association.getTableName(), association.getColumnName(), association.getAssociationName());
            showVos.forEach(showVo -> {
                Object value = showVo.getValue();
                Object label = associationValues.get(value);
                showVo.setLabel(label);
            });
        }
        return showVos;


    }

    /**
     * 根据逻辑名称、表名、查询列、查询条件和分页参数查询分页结果
     *
     * @param logicName 逻辑名称
     * @param tableName 表名
     * @param queryColumns 查询列信息
     * @param conditions 查询条件
     * @param pageParam 分页参数
     * @return 分页结果
     * @throws ServiceException 如果逻辑名称对应的数据库模板服务不存在，则抛出ServiceException异常
     */
    public PageResult<Map<String, Object>> queryPage(String logicName, String tableName, List<ChargeDetailTemplateColumnVO> queryColumns, List<ChargeDynamicColumnDTO> conditions, PageParam pageParam) {
        // 根据逻辑名称获取数据库模板服务
        DbTemplateService dbTemplateService = DB_TEMPLATE_SERVICE_MAP.get(logicName);
        if (dbTemplateService == null) {
            // 如果数据库模板服务不存在，记录日志并抛出异常
            log.info("logicName:{} not exists", logicName);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_DB_NOT_EXISTS);
        }

        // 获取查询列名列表
        List<String> queryColumnNames = queryColumns.stream().map(ChargeDetailTemplateColumnVO::getColumnName).toList();

        // 调用数据库模板服务查询分页结果
        PageResult<Map<String, Object>> pageResult = dbTemplateService.queryPage(tableName, queryColumnNames, conditions, pageParam.getPageNo(), pageParam.getPageSize());

        // 获取查询结果列表
        List<Map<String, Object>> list = pageResult.getList();
        if (!CollectionUtils.isEmpty(list)) {
            // 遍历查询列信息
            for (ChargeDetailTemplateColumnVO queryColumn : queryColumns) {
                // 如果当前查询列有关联信息
                if (queryColumn.getAssociation() != null) {
                    // 获取关联信息
                    ChargeDetailTemplateColumnAssociationVO association = queryColumn.getAssociation();
                    // 根据关联逻辑名称获取数据库模板服务
                    DbTemplateService associationDbTemplateService = DB_TEMPLATE_SERVICE_MAP.get(association.getLogicName());
                    if (associationDbTemplateService == null) {
                        // 如果数据库模板服务不存在，记录日志并抛出异常
                        log.info("logicName:{} not exists", association.getLogicName());
                        throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_DB_NOT_EXISTS);
                    }
                    // 调用数据库模板服务获取关联值
                    Map<Object, Object> associationValues = associationDbTemplateService.listValue(association.getTableName(), association.getColumnName(), association.getAssociationName());
                    // 遍历查询结果列表，将关联值设置到对应列中
                    for (Map<String, Object> columns : list) {
                        columns.put(queryColumn.getColumnName(), associationValues.get(columns.get(queryColumn.getColumnName())));
                    }
                }
            }
        }
        return pageResult;
    }


    /**
     * 获取数据库树形结构
     *
     * @param logicName 逻辑名称
     * @param tableName 表名
     * @return 包含数据库树形结构的列表
     */
    public List<DatabaseTreeVO> tree(String logicName, String tableName) {
        // 调用tree方法，并传入两个lambda表达式作为参数
        return tree(
                // 第一个lambda表达式，用于判断logicName是否为空且等于dbTemplateService的getLogicName()方法的返回值
                dbTemplateService -> StringUtils.isBlank(logicName) || logicName.equals(dbTemplateService.getLogicName()),
                // 第二个lambda表达式，用于判断tableName是否为空且等于tableMetaDTO的getTableName()方法的返回值
                tableMetaDTO -> StringUtils.isBlank(tableName) || tableName.equals(tableMetaDTO.getTableName())
        );
    }


    /**
     * 根据给定的数据库过滤器和表过滤器，生成数据库树结构。
     *
     * @param dbFilter 数据库过滤器，用于过滤数据库
     * @param tableFilter 表过滤器，用于过滤表
     * @return 包含数据库树结构的列表
     */
    public List<DatabaseTreeVO> tree(Predicate<DbTemplateService> dbFilter, Predicate<TableMetaDTO> tableFilter) {
        // 使用DB_TEMPLATE_SERVICE_MAP的值流
        return DB_TEMPLATE_SERVICE_MAP.values().stream()
                // 过滤数据库
                .filter(dbFilter)
                .map(item -> {
                    // 创建DatabaseTreeVO对象
                    DatabaseTreeVO treeVO = new DatabaseTreeVO();
                    treeVO.setLogicName(item.getLogicName());
                    treeVO.setActualName(item.getActualName());

                    // 创建DatabaseMetaVO对象
                    DatabaseMetaVO databaseMetaVO = new DatabaseMetaVO(item.getLogicName(), item.getActualName());
                    treeVO.setTables(item.getTABLES().stream()
                            // 过滤表
                            .filter(tableFilter)
                            .map(table -> {
                                // 创建TableTreeVO对象
                                TableTreeVO tableTreeVO = new TableTreeVO();
                                tableTreeVO.setTableName(table.getTableName());
                                tableTreeVO.setTableComment(table.getTableComment());
                                tableTreeVO.setDatabase(databaseMetaVO);

                                // 创建TableMetaVO对象
                                TableMetaVO tableMetaVO = new TableMetaVO();
                                tableMetaVO.setDatabase(databaseMetaVO);
                                tableMetaVO.setTableName(table.getTableName());
                                tableMetaVO.setTableComment(table.getTableComment());

                                // 设置表的列
                                tableTreeVO.setColumns(table.getColumns().stream()
                                        .map(column -> {
                                            // 创建FieldTreeVO对象
                                            FieldTreeVO fieldTreeVO = new FieldTreeVO();
                                            fieldTreeVO.setColumnName(column.getColumnName());
                                            fieldTreeVO.setColumnComment(column.getColumnComment());
                                            fieldTreeVO.setTable(tableMetaVO);
                                            fieldTreeVO.setColumnType(column.getColumnType());
                                            fieldTreeVO.setColumnIndex(column.getColumnIndex());
                                            return fieldTreeVO;
                                        })
                                        .toList());

                                return tableTreeVO;
                            })
                            .toList());

                    return treeVO;

                })
                // 按逻辑名称排序
                .sorted(Comparator.comparing(DatabaseTreeVO::getLogicName))
                .toList();
    }

    public  List<DbTemplateService> mysqlTemplateServices() {
        List<DbTemplateService> list = new ArrayList<>();
        // 加载mysql数据源配置
        for (DbTemplateService value : DB_TEMPLATE_SERVICE_MAP.values()) {
            if (DbTemplateService.isMysql(value.getJdbcUrl())) {
                list.add(value);
            }
        }
        return list;
    }

}
