package com.linkcircle.boss.module.billing.web.data.service;

import com.linkcircle.boss.module.billing.web.data.model.dto.ServiceCodeInfo;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;
import com.linkcircle.boss.module.crm.enums.PaymentTypeEnum;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-06-17 11:25
 * @description 订阅信息数据服务接口
 */
public interface SubscriptionDataService {

    /**
     * 根据账户ID获取订阅信息列表
     * 先从Redis缓存查询，如果没有则从远程API获取并缓存
     *
     * @param accountId 账户ID
     * @return 订阅信息列表，如果不存在返回空列表
     */
    Optional<List<AccountSubscriptionsVO>> getAccountSubscriptions(Long accountId, PaymentTypeEnum paymentType);

    /**
     * 根据订阅ID获取订阅详情
     * 先从Redis缓存查询，如果没有则从远程API获取并缓存
     *
     * @param subscriptionId 订阅ID
     * @return 订阅详情，如果不存在返回空
     */
    Optional<AccountSubscriptionsVO> getSubscriptionDetail(Long subscriptionId);

    /**
     * 查询全部订阅的账户id列表
     * 不使用缓存，直接调用远程API
     *
     * @param paymentType 支付类型 0-预付费, 1-后付费
     * @return 账户ID列表，如果不存在返回空列表
     */
    List<Long> getAllSubscriptionAccountIds(Integer paymentType);

    /**
     * 根据计费类型查询订阅列表
     * 不使用缓存，直接调用远程API
     *
     * @param billingType 计费类型 0:固定费率，1：阶梯费率，2：套餐计费，3：按量计费
     * @return 订阅信息列表，如果不存在返回空列表
     */
    List<AccountSubscriptionsVO> getSubscriptionsListByBillingType(Integer billingType);

    /**
     * 获取所有活跃的服务编码信息
     * 先从Redis缓存查询，如果没有则从远程API获取并缓存
     *
     * @return 服务编码信息列表，包含服务编码和支付类型
     */
    List<ServiceCodeInfo> getAllActiveServiceCodes();
}
