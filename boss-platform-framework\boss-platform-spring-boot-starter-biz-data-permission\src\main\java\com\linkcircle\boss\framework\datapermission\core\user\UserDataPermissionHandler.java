package com.linkcircle.boss.framework.datapermission.core.user;

import com.alibaba.fastjson.JSON;
import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.web.core.util.WebFrameworkUtils;
import com.linkcircle.boss.module.system.api.user.AdminUserApi;
import com.linkcircle.boss.module.system.api.user.vo.AdminUserRespVO;
import io.github.kk01001.redis.RedissonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.Set;

/**
 * 用户数据权限处理器
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserDataPermissionHandler {

    private static final String CACHE_PREFIX = "user:data:permission:";

    private final AdminUserApi adminUserApi;
    private final RedissonUtil redissonUtil;

    /**
     * 缓存过期时间 - 30分钟
     */
    private static final Duration CACHE_EXPIRE_TIME = Duration.ofMinutes(30);

    /**
     * 获取当前登录用户的数据权限信息。
     *
     * 1. 获取用户ID。
     * 2. 尝试从Redis缓存中获取权限信息。
     * 3. 如果缓存未命中或Redis异常，则从数据库中加载并缓存权限信息。
     * 4. 返回用户权限信息。
     *
     * @return UserDataPermissionVO 当前用户的数据权限信息
     * @throws ServiceException 获取用户ID失败时抛出
     */
    public UserDataPermissionVO getCurrentUserPermission() {
        Long userId = WebFrameworkUtils.getLoginUserId();
        log.info("UserDataPermissionHandler:获取账号id:{}", userId);
        if (ObjectUtils.isEmpty(userId)) {
            log.error("UserDataPermissionHandler:获取账号id失败");
            throw new ServiceException(GlobalErrorCodeConstants.UNAUTHORIZED);
        }

        // 尝试从Redis获取
        try {
            String cacheKey = buildCacheKey(userId);
            UserDataPermissionVO permission = redissonUtil.get(cacheKey, UserDataPermissionVO.class);
            if (permission != null) {
                return permission;
            }
        } catch (Exception e) {
            log.warn("Redis操作异常，降级直接查询数据，userId: {}, 异常: {}", userId, e.getMessage());
        }

        // Redis不可用或缓存未命中，直接加载数据
        return loadAndCacheUserPermission(userId);
    }

    /**
     * <h1>加载用户权限并缓存</h1>
     * <p>该方法用于加载指定用户的权限信息，并将其缓存到Redis中。</p>
     *
     * @param userId 用户ID
     * @return 返回用户的权限信息对象 {@link UserDataPermissionVO}
     */
    private UserDataPermissionVO loadAndCacheUserPermission(Long userId) {
        UserDataPermissionVO permission = loadUserPermission(userId);

        // 尝试缓存到Redis（即使失败也不影响主流程）
        try {
            String cacheKey = buildCacheKey(userId);
            // 存储缓存时使用JSON序列化
            redissonUtil.set(cacheKey, JSON.toJSONString(permission), CACHE_EXPIRE_TIME);
        } catch (Exception e) {
            log.warn("缓存用户权限到Redis失败，userId: {}, 异常: {}", userId, e.getMessage());
        }

        return permission;
    }

    /**
     * 加载用户权限信息
     *
     * @param userId 用户ID
     * @return 用户权限信息对象
     * @throws ServiceException 如果发生异常，则抛出ServiceException异常
     */
    private UserDataPermissionVO loadUserPermission(Long userId) {
        CommonResult<AdminUserRespVO> result = adminUserApi.getUser(userId);
        if (result.isError()) {
            log.error("UserDataPermissionHandler-adminUserApi获取账号信息失败:{}", result);
            throw new ServiceException(GlobalErrorCodeConstants.UNAUTHORIZED);
        }
        AdminUserRespVO user = result.getData();
        if (ObjectUtils.isEmpty(user)) {
            log.error("UserDataPermissionHandler:账号未登录");
            throw new ServiceException(GlobalErrorCodeConstants.UNAUTHORIZED);
        }
        return new UserDataPermissionVO(
                user.getDataScope(),
                user.getDataScopeCustomer(),
                user.getDataScopeSupplier(),
                user.getDataScopeContract(),
                user.getDataScopeProject(),
                parseIds(user.getDataScopeCustomerIds()),
                parseIds(user.getDataScopeSupplierIds()),
                parseIds(user.getDataScopeContractIds()),
                parseIds(user.getDataScopeProjectIds())
        );
    }

    /**
     * 将给定的 Long 类型集合转换为 List<Long> 列表。
     *
     * @param ids 需要转换的 Long 类型集合
     * @return 转换后的 List<Long> 列表，如果输入集合为空或为 null，则返回空的 List
     */
    private List<Long> parseIds(Set<Long> ids) {
        if (ids == null || ids.isEmpty()) return List.of();
        return ids.stream().toList();
    }

    /**
     * 清除用户权限缓存
     *
     * @param userId 用户ID
     */
    public void clearCache(Long userId) {
        try {
            String cacheKey = buildCacheKey(userId);
            redissonUtil.delete(cacheKey);
            log.info("Clear permission cache for userDataPermission: {}", userId);
        } catch (Exception e) {
            log.warn("清除Redis缓存失败，userId: {}, 异常: {}", userId, e.getMessage());
        }
    }

    /**
     * 构建缓存键
     *
     * @param userId 用户ID
     * @return 缓存键的字符串表示
     */
    private String buildCacheKey(Long userId) {
        return CACHE_PREFIX + userId;
    }
}