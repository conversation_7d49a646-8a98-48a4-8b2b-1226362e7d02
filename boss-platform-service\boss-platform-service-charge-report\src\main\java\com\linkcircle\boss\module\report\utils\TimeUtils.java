package com.linkcircle.boss.module.report.utils;

import cn.hutool.core.date.DateUtil;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/6/19 19:12
 * @description 处理时间的工具类
 */
public class TimeUtils {
    // 日期格式
    public static final String  FORMAT_YYYY = "yyyy";
    public static final String  FORMAT_YYYYMM = "yyyyMM";
    public static final String  FORMAT_YYYYMMDD = "yyyyMMdd";
    private static final Logger log = LoggerFactory.getLogger(TimeUtils.class);
    // 和UTC时间的时区差
    public  static volatile Integer ZONE_OFFSET = null;


    static Integer getZoneOffset() {
        if (ZONE_OFFSET == null) {
             synchronized (TimeUtils.class){
                 if (ZONE_OFFSET == null) {
                     ZoneOffset offset = ZoneId.systemDefault().getRules().getOffset(java.time.Instant.now());
                     ZONE_OFFSET = offset.getTotalSeconds() / 3600;  // 转换为小时数
                 }
             }
        }
        return ZONE_OFFSET;
    }

    /**
     * 将日期转换为UTC时间
     * @param date  日期
     * @param zone  比如  当前时间为东八区时间 要转行为UTC时间 传入8即可   时区差
     * @return UTC时间
     */
    public static Date transToUTC(Date date, Integer zone){
        if(zone!= null && !Objects.equals(zone,0)) {
            return DateUtil.offsetHour(date, zone);
        }
        return date;
    }

    public static Date transToUTC(Date date){
        return  transToUTC(date,getZoneOffset());
    }

    public static String nextMonth(String date,String format) {
        Date billMonth = DateUtil.parse(date, format);
        return DateUtil.format(DateUtils.addMonths(billMonth, 1), format);
    }

    public static Date  beginOfMonth(String date, String format) {
        Date billMonth = DateUtil.parse(date, format);
        return DateUtil.beginOfMonth(billMonth);
    }

    public static Date  endOfMonth(String date, String format) {
        Date billMonth = DateUtil.parse(date, format);
        return DateUtil.endOfMonth(billMonth);
    }


    public static Date nextYear(String date, String format) {
        Date year = DateUtil.parse(date, format);
        return DateUtil.offsetYear(year, 1);
    }

    public static String nextYearStr(String date, String format) {
        Date year = DateUtil.parse(date, format);
        return DateUtil.format(DateUtil.offsetYear(year, 1), format);
    }

    public static Date beginOfYear(String date, String format) {
        Date billMonth = DateUtil.parse(date, format);
        return DateUtil.beginOfYear(billMonth);
    }

    public static Date endOfYear(String date, String format) {
        Date billMonth = DateUtil.parse(date, format);
        return DateUtil.endOfYear(billMonth);
    }

    public static Date parseDate(String date) {
        return DateUtil.parse(date, com.linkcircle.boss.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
    }

    public static Date beginOfDay(String date, String format) {
        Date billMonth = DateUtil.parse(date, format);
        return DateUtil.beginOfDay(billMonth);
    }

    public static Date endOfDay(String date, String format) {
        Date billMonth = DateUtil.parse(date, format);
        return DateUtil.endOfDay(billMonth);
    }

    public static Object nextDay(String date, String format) {
        Date billMonth = DateUtil.parse(date, format);
        return DateUtil.format(DateUtil.offsetDay(billMonth, 1), format);
    }
}
