package com.linkcircle.boss.module.billing.web.stats.mapper;

import com.linkcircle.boss.framework.mybatis.core.mapper.BaseMapperX;
import com.linkcircle.boss.module.billing.api.stats.customer.model.entity.CustomerDailyUsageBillDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-18 08:58
 * @description 客户每日使用量账单表 Mapper
 */
@Mapper
public interface CustomerDailyUsageBillMapper extends BaseMapperX<CustomerDailyUsageBillDO> {

    /**
     * 查询预付费收入账单表中的所有serviceCode
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return serviceCode列表
     */
    List<String> queryServiceCodesFromPrepaidIncome(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 查询后付费收入账单表中的所有serviceCode
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return serviceCode列表
     */
    List<String> queryServiceCodesFromPostpaidIncome(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 统计预付费收入账单使用量数据
     *
     * @param serviceCode 服务编码
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 统计结果列表
     */
    List<CustomerDailyUsageBillDO> statsPrepaidIncomeUsage(@Param("serviceCode") String serviceCode,
                                                           @Param("startTime") Long startTime,
                                                           @Param("endTime") Long endTime);

    /**
     * 统计后付费收入账单使用量数据
     *
     * @param serviceCode 服务编码
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 统计结果列表
     */
    List<CustomerDailyUsageBillDO> statsPostpaidIncomeUsage(@Param("serviceCode") String serviceCode,
                                                            @Param("startTime") Long startTime,
                                                            @Param("endTime") Long endTime);
}
