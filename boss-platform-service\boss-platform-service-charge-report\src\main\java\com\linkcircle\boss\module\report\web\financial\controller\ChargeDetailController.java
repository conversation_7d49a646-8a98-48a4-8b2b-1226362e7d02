package com.linkcircle.boss.module.report.web.financial.controller;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.permission.PreAuthorize;
import com.linkcircle.boss.module.report.web.dynamic.model.vo.QueryValueVo;
import com.linkcircle.boss.module.report.web.dynamic.model.vo.ShowVo;
import com.linkcircle.boss.module.report.web.financial.model.dto.ChargeColumnReqDTO;
import com.linkcircle.boss.module.report.web.financial.model.dto.ChargeFinancialReqDTO;
import com.linkcircle.boss.module.report.web.financial.service.ChargeDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static com.linkcircle.boss.framework.common.model.CommonResult.success;

/**
 * <AUTHOR>
 * @date 2025/6/20 11:14
 * @description 财务明细查询
 */
@Tag(name = "财务-明细查询")
@RestController
@RequestMapping("/financial/charge-detail")
@Validated
public class ChargeDetailController {


    @Autowired
    private ChargeDetailService chargeDetailService;


    @PostMapping("/column/query")
    @Operation(summary = "查询条件-查询")
    @PreAuthorize("@ss.hasPermission('financial:detail:column-query')")
    public CommonResult<List<ShowVo>> commonQuery(@Valid @RequestBody final ChargeColumnReqDTO detailReqDTO) {
        return success(chargeDetailService.commonQuery(detailReqDTO));
    }

    @PostMapping("/column/customer-account-query")
    @Operation(summary = "查询条件-查询-客户+账户")
    @PreAuthorize("@ss.hasPermission('financial:detail:customer-account-query')")
    @Parameter(name = "templateId", description = "模板id", example = "********", required = true)
    public CommonResult<List<QueryValueVo>> customerAccountQuery(@Valid @RequestParam(required = true) final Long templateId) {
        return success(chargeDetailService.customerAccountQuery(templateId));
    }

    @PostMapping("/column/subscribe-query")
    @Operation(summary = "查询条件-查询-订阅")
    @PreAuthorize("@ss.hasPermission('financial:detail:subscribe-query')")
    @Parameter(name = "templateId", description = "模板id", example = "********", required = true)
    public CommonResult<List<QueryValueVo>> subscribeQuery(@Valid @RequestParam(required = true) final Long templateId) {
        return success(chargeDetailService.subscribeQuery(templateId));
    }


//    @PostMapping("/page/query")
//    @Operation(summary = "分页查询")
//    @PreAuthorize("@ss.hasPermission('financial:detail:query')")
//    public CommonResult<PageResult<Map<String,Object>>> pageQuery(@Valid @RequestBody final ChargeDynamicReqDTO detailReqDTO) {
//        return success(chargeDetailService.pageQuery(detailReqDTO,false));
//    }
//
//
//    @PostMapping("/submit")
//    @Operation(summary = "提交导出任务")
//    @PreAuthorize("@ss.hasPermission('financial:detail:submit')")
//    public CommonResult<String> submitTask(@Valid @RequestBody final ChargeDynamicReqDTO detailReqDTO) {
//        chargeDetailService.submitTask(detailReqDTO);
//        return success();
//    }


    @PostMapping("/page/query")
    @Operation(summary = "分页查询")
    @PreAuthorize("@ss.hasPermission('financial:detail:query')")
    public CommonResult<PageResult<Map<String,Object>>> pageQuery(@Valid @RequestBody final ChargeFinancialReqDTO detailReqDTO) {
        return success(chargeDetailService.pageFinancialQuery(detailReqDTO,false));
    }


    @PostMapping("/submit")
    @Operation(summary = "提交导出任务")
    @PreAuthorize("@ss.hasPermission('financial:detail:submit')")
    public CommonResult<String> submitTask(@Valid @RequestBody final ChargeFinancialReqDTO detailReqDTO) {
        chargeDetailService.submitFinancialTask(detailReqDTO);
        return success();
    }


}
