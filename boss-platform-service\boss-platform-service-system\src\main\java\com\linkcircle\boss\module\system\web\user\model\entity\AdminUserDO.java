package com.linkcircle.boss.module.system.web.user.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.linkcircle.boss.framework.common.enums.CommonStatusEnum;
import com.linkcircle.boss.framework.tanant.TenantBaseDO;
import com.linkcircle.boss.module.system.enums.common.SexEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.util.Set;

/**
 * 管理后台的用户 DO
 *
 * <AUTHOR>
 */
@TableName(value = "system_users", autoResultMap = true) // 由于 SQL Server 的 system_user 是关键字，所以使用 system_users
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdminUserDO extends TenantBaseDO {

    /**
     * 用户ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 用户账号
     */
    private String username;
    /**
     * 加密后的密码
     * <p>
     * 因为目前使用 {@link BCryptPasswordEncoder} 加密器，所以无需自己处理 salt 盐
     */
    private String password;
    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 备注
     */
    private String remark;
    /**
     * 部门 ID
     */
    private Long deptId;
    /**
     * 岗位编号数组
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<Long> postIds;
    /**
     * 用户邮箱
     */
    private String email;
    /**
     * 手机号码
     */
    private String mobile;
    /**
     * 用户性别
     * <p>
     * 枚举类 {@link SexEnum}
     */
    private Integer sex;
    /**
     * 用户头像
     */
    private String avatar;
    /**
     * 帐号状态
     * <p>
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;
    /**
     * 最后登录IP
     */
    private String loginIp;
    /**
     * 最后登录时间
     */
    private Long loginDate;

    /**
     * 时区
     */
    private String timezone;


    /**
     * 用户数据权限范围 UserDataScopeEnum 枚举
     * <p>
     */
    private Integer dataScope;

    /**
     * 客户数据范围
     */
    private Integer dataScopeCustomer;

    /**
     * 供应商数据范围
     */
    private Integer dataScopeSupplier;

    /**
     * 合同数据范围
     */
    private Integer dataScopeContract;

    /**
     * 项目数据范围
     */
    private Integer dataScopeProject;

    /**
     * 数据范围(指定客户数组)
     * <p>
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<Long> dataScopeCustomerIds;

    /**
     * 数据范围(指定供应商数组)
     * <p>
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<Long> dataScopeSupplierIds;

    /**
     * 数据范围(指定合同数组)
     * <p>
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<Long> dataScopeContractIds;

    /**
     * 数据范围(指定项目数组)
     * <p>
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<Long> dataScopeProjectIds;
}
