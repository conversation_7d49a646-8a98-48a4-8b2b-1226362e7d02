package com.linkcircle.boss.module.billing.web.data.service;

import com.linkcircle.boss.module.billing.web.data.model.dto.ServiceCodeInfo;
import com.linkcircle.boss.module.crm.api.supplier.purchase.vo.ResourcePurchaseVO;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-06-24 15:28
 * @description 资源采购数据服务接口
 */
public interface ResourcePurchaseDataService {

    /**
     * 根据账户ID获取供应商资源采购信息列表
     * 先从Redis缓存查询，如果没有则从远程API获取并缓存
     *
     * @param accountId 供应商账户ID
     * @return 资源采购信息列表，如果不存在返回空列表
     */
    List<ResourcePurchaseVO> getAccountResourcePurchases(Long accountId);

    /**
     * 根据采购ID获取资源采购详情
     * 先从Redis缓存查询，如果没有则从远程API获取并缓存
     *
     * @param purchaseId 采购ID
     * @return 资源采购详情，如果不存在返回空
     */
    Optional<ResourcePurchaseVO> getPurchaseDetail(Long purchaseId);

    /**
     * 查询全部采购的账户id列表
     * 不使用缓存，直接调用远程API
     *
     * @param paymentType 支付类型 0-预付费, 1-后付费
     * @return 供应商账户ID列表，如果不存在返回空列表
     */
    List<Long> getAllPurchaseAccountIds(Integer paymentType);

    /**
     * 根据计费类型查询采购列表
     * 不使用缓存，直接调用远程API
     *
     * @param billingType 计费类型 0:固定费率，1：阶梯费率，2：套餐计费，3：按量计费
     * @return 资源采购信息列表，如果不存在返回空列表
     */
    List<ResourcePurchaseVO> getPurchasesListByBillingType(Integer billingType);

    /**
     * 获取所有活跃的服务编码信息
     * 先从Redis缓存查询，如果没有则从远程API获取并缓存
     *
     * @return 服务编码信息列表，包含服务编码和支付类型
     */
    List<ServiceCodeInfo> getAllActiveServiceCodes();

}