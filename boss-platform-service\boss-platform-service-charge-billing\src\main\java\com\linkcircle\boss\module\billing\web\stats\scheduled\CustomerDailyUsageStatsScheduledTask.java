package com.linkcircle.boss.module.billing.web.stats.scheduled;

import cn.hutool.core.util.IdUtil;
import com.linkcircle.boss.module.billing.web.stats.service.CustomerDailyUsageStatsService;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.github.kk01001.util.TraceIdUtil;
import io.github.kk01001.xxljob.annotations.XxlJobRegister;
import io.github.kk01001.xxljob.enums.ExecutorRouteStrategyEnum;
import io.github.kk01001.xxljob.enums.MisfireStrategyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-08-01 11:06
 * @description 客户每日使用量统计定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CustomerDailyUsageStatsScheduledTask {

    private final CustomerDailyUsageStatsService customerDailyUsageStatsService;

    /**
     * 客户每日使用量统计定时任务
     * 每小时执行一次，统计前一小时的客户使用量数据
     */
    @XxlJob("customerDailyUsageStatsHandler")
    @XxlJobRegister(
            cron = "0 5 * * * ?",
            jobDesc = "客户每日使用量统计定时任务",
            author = "admin",
            triggerStatus = 1,
            executorRouteStrategy = ExecutorRouteStrategyEnum.LEAST_FREQUENTLY_USED,
            misfireStrategy = MisfireStrategyEnum.DO_NOTHING
    )
    public void customerDailyUsageStatsHandler() {
        String traceId = IdUtil.fastSimpleUUID();
        TraceIdUtil.buildAndSetTraceId(" ", "客户每日使用量统计", traceId);

        try {
            log.info("开始执行客户每日使用量统计定时任务");

            // 计算统计时间范围（前一小时）
            long currentTime = System.currentTimeMillis();
            long endTime = currentTime - (currentTime % (60 * 60 * 1000));
            long startTime = endTime - (60 * 60 * 1000);

            log.info("统计时间范围: {} - {}", startTime, endTime);

            // 执行统计
            customerDailyUsageStatsService.executeHourlyStats(startTime, endTime);

            log.info("客户每日使用量统计定时任务执行完成");
        } catch (Exception e) {
            log.error("客户每日使用量统计定时任务执行异常", e);
        } finally {
            TraceIdUtil.remove();
        }
    }
}
