package com.linkcircle.boss.module.system.web.user.model.vo.user;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.linkcircle.boss.framework.excel.core.annotations.DictFormat;
import com.linkcircle.boss.framework.excel.core.convert.DictConvert;
import com.linkcircle.boss.framework.excel.core.convert.TimestampConvert;
import com.linkcircle.boss.module.system.enums.DictTypeConstants;

import java.util.Set;

@Schema(description = "管理后台 - 用户信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UserRespVO {

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("用户编号")
    private Long id;

    @Schema(description = "用户账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "xxx")
    @ExcelProperty("用户名称")
    private String username;

    @Schema(description = "用户昵称", requiredMode = Schema.RequiredMode.REQUIRED, example = "Admin")
    @ExcelProperty("用户昵称")
    private String nickname;

    @Schema(description = "备注", example = "我是一个用户")
    private String remark;

    @Schema(description = "部门ID", example = "我是一个用户")
    private Long deptId;
    @Schema(description = "部门名称", example = "IT 部")
    @ExcelProperty("部门名称")
    private String deptName;

    @Schema(description = "岗位编号数组", example = "1")
    private Set<Long> postIds;

    @Schema(description = "用户邮箱", example = "<EMAIL>")
    @ExcelProperty("用户邮箱")
    private String email;

    @Schema(description = "手机号码", example = "15601691300")
    @ExcelProperty("手机号码")
    private String mobile;

    @Schema(description = "用户性别，参见 SexEnum 枚举类", example = "1")
    @ExcelProperty(value = "用户性别", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.USER_SEX)
    private Integer sex;

    @Schema(description = "用户头像", example = "https://www.xxx.cn/xxx.png")
    private String avatar;

    @Schema(description = "状态，参见 CommonStatusEnum 枚举类", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "帐号状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    private Integer status;

    @Schema(description = "最后登录 IP", requiredMode = Schema.RequiredMode.REQUIRED, example = "***********")
    @ExcelProperty("最后登录IP")
    private String loginIp;

    @Schema(description = "最后登录时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "时间戳格式")
    @ExcelProperty(value = "最后登录时间", converter = TimestampConvert.class)
    private Long loginDate;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "时间戳格式")
    private Long createTime;
    
    @Schema(description = "时区", example = "Asia/Shanghai")
    private String timezone;

    @Schema(description = "用户数据权限范围", example = "1")
    private Integer dataScope;

    @Schema(description = "客户数据范围,0:全选 1:自定义")
    private Integer dataScopeCustomer;

    @Schema(description = "供应商数据范围,0:全选 1:自定义")
    private Integer dataScopeSupplier;

    @Schema(description = "合同数据范围,0:全选 1:自定义")
    private Integer dataScopeContract;

    @Schema(description = "项目数据范围,0:全选 1:自定义")
    private Integer dataScopeProject;

    @Schema(description = "指定客户ID列表")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<Long> dataScopeCustomerIds;

    @Schema(description = "指定供应商ID列表")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<Long> dataScopeSupplierIds;

    @Schema(description = "指定合同ID列表")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<Long> dataScopeContractIds;

    @Schema(description = "指定项目ID列表")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<Long> dataScopeProjectIds;

}
