<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linkcircle.boss.module.billing.web.stats.mapper.SupplierDailyUsageBillMapper">
    <!-- 统计预付费成本账单使用量数据（SUM聚合） -->
    <select id="sumPrepaidCostUsage"
            resultType="com.linkcircle.boss.module.billing.web.stats.model.vo.DailyUsageStatsSummaryVO">
        SELECT
        SUM(COALESCE(charge_usage_count, 0)) AS "chargeUsageCount",
        SUM(COALESCE(usage_count, 0)) AS "usage",
        SUM(COALESCE(cash_amount, 0)) AS cashAmount,
        SUM(COALESCE(point_amount, 0)) AS pointAmount
        FROM prepaid_cost_bill_detail
        WHERE resource_service_id = #{serviceId}
        AND account_id = #{accountId}
        AND business_time between #{startTime} and #{endTime}
    </select>

    <!-- 统计后付费成本账单使用量数据（SUM聚合） -->
    <select id="sumPostpaidCostUsage"
            resultType="com.linkcircle.boss.module.billing.web.stats.model.vo.DailyUsageStatsSummaryVO">
        SELECT
        SUM(COALESCE(charge_usage_count, 0)) AS chargeUsageCount,
        SUM(COALESCE(usage_count, 0)) AS "usage",
        SUM(COALESCE(cash_amount, 0)) AS cashAmount,
        SUM(COALESCE(point_amount, 0)) AS pointAmount
        FROM prepaid_cost_bill_detail
        WHERE resource_service_id = #{serviceId}
        AND account_id = #{accountId}
        AND business_time between #{startTime} and #{endTime}
    </select>
</mapper>
