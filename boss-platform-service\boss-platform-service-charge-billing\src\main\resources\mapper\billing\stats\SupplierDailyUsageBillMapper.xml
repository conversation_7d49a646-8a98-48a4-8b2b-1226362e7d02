<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linkcircle.boss.module.billing.web.stats.mapper.SupplierDailyUsageBillMapper">



    <!-- 统计预付费成本账单使用量数据 -->
    <select id="statsPrepaidCostUsage" resultType="com.linkcircle.boss.module.billing.api.stats.supplier.model.entity.SupplierDailyUsageBillDO">
        SELECT
            DATE_FORMAT(FROM_UNIXTIME(business_time / 1000), '%Y%m%d%H') AS billDate,
            CAST(supplier_id AS CHAR) AS supplierId,
            CAST(account_id AS CHAR) AS accountId,
            CAST(resource_service_id AS CHAR) AS resourceServiceId,
            SUM(COALESCE(usage_count, 0)) AS usage,
            usage_unit AS usageUnit,
            SUM(COALESCE(cash_amount, 0)) AS cashAmount,
            currency AS cashAmountCurrency
        FROM prepaid_cost_bill_detail
        WHERE deleted = false
          AND service_code = #{serviceCode}
          AND business_time >= #{startTime}
          AND business_time &lt; #{endTime}
        GROUP BY
            DATE_FORMAT(FROM_UNIXTIME(business_time / 1000), '%Y%m%d%H'),
            supplier_id,
            account_id,
            resource_service_id,
            usage_unit,
            currency
        HAVING SUM(COALESCE(usage_count, 0)) > 0
    </select>

    <!-- 统计后付费成本账单使用量数据 -->
    <select id="statsPostpaidCostUsage" resultType="com.linkcircle.boss.module.billing.api.stats.supplier.model.entity.SupplierDailyUsageBillDO">
        SELECT
            DATE_FORMAT(FROM_UNIXTIME(business_time / 1000), '%Y%m%d%H') AS billDate,
            CAST(supplier_id AS CHAR) AS supplierId,
            CAST(account_id AS CHAR) AS accountId,
            CAST(resource_service_id AS CHAR) AS resourceServiceId,
            SUM(COALESCE(usage_count, 0)) AS usage,
            usage_unit AS usageUnit,
            SUM(COALESCE(cash_amount, 0)) AS cashAmount,
            currency AS cashAmountCurrency
        FROM postpaid_cost_bill_detail
        WHERE deleted = false
          AND service_code = #{serviceCode}
          AND business_time >= #{startTime}
          AND business_time &lt; #{endTime}
        GROUP BY
            DATE_FORMAT(FROM_UNIXTIME(business_time / 1000), '%Y%m%d%H'),
            supplier_id,
            account_id,
            resource_service_id,
            usage_unit,
            currency
        HAVING SUM(COALESCE(usage_count, 0)) > 0
    </select>

</mapper>
