package com.linkcircle.boss.module.crm.api.supplier.purchase;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.crm.api.productservice.vo.ServiceCodeInfo;
import com.linkcircle.boss.module.crm.api.supplier.purchase.vo.ResourcePurchaseVO;
import com.linkcircle.boss.module.crm.constants.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-24 15:06
 * @description
 */
@FeignClient(name = ApiConstants.NAME,
        path = ApiConstants.PREFIX + "/purchase",
        fallbackFactory = ResourcePurchaseApiFallback.class,
        dismiss404 = true)
@Tag(name = "RPC 服务 - 供应商资源采购")
public interface ResourcePurchaseApi {

    @GetMapping("/supplier/list")
    @Operation(summary = "获取供应商资源采购列表")
    @Parameter(name = "accountId", description = "供应商账户id", required = true, example = "1024")
    CommonResult<List<ResourcePurchaseVO>> getAccountSubscriptionsList(@RequestParam("accountId") Long accountId);

    @GetMapping("/detail")
    @Operation(summary = "获取供应商资源采购详情")
    @Parameter(name = "purchaseId", description = "供应商资源采购id", required = true, example = "1024")
    CommonResult<ResourcePurchaseVO> getPurchaseDetail(@RequestParam("purchaseId") Long purchaseId);

    @GetMapping("/account/ids/all")
    @Operation(summary = "查询全部采购的账户id列表")
    @Parameter(name = "paymentType", description = "支付类型 0-预付费, 1-后付费", required = true, example = "0")
    CommonResult<List<Long>> getAllPurchaseAccountIds(@RequestParam("paymentType") Integer paymentType);

    @GetMapping("/list/by-billing-type")
    @Operation(summary = "根据计费类型查询采购列表, 包含指定计费类型的采购")
    @Parameter(name = "billingType", description = "计费类型 0:固定费率，1：阶梯费率，2：套餐计费，3：按量计费", required = true, example = "0")
    CommonResult<List<ResourcePurchaseVO>> getPurchasesListByBillingType(@RequestParam("billingType") Integer billingType);

    @GetMapping("/service-codes")
    @Operation(summary = "获取所有活跃的供应商服务编码信息")
    CommonResult<List<ServiceCodeInfo>> getAllActiveServiceCodes();
}
