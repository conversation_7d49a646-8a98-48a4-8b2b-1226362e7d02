package com.linkcircle.boss.module.charge.crm.web.purchase.api;

import com.linkcircle.boss.framework.common.enums.RpcConstants;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.charge.crm.web.purchase.service.IChargePurchaseService;
import com.linkcircle.boss.module.crm.api.productservice.vo.ServiceCodeInfo;
import com.linkcircle.boss.module.crm.api.supplier.purchase.ResourcePurchaseApi;
import com.linkcircle.boss.module.crm.api.supplier.purchase.vo.ResourcePurchaseVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(RpcConstants.RPC_API_PREFIX + "/purchase")
@RequiredArgsConstructor
public class ChargePurchaseApiController implements ResourcePurchaseApi {

    private final IChargePurchaseService chargePurchaseService;

    @Override
    public CommonResult<List<ResourcePurchaseVO>> getAccountSubscriptionsList(Long accountId) {
        return CommonResult.success(chargePurchaseService.getAccountSubscriptionsList(accountId));
    }

    @Override
    public CommonResult<ResourcePurchaseVO> getPurchaseDetail(Long purchaseId) {
        return CommonResult.success(chargePurchaseService.getPurchaseDetail(purchaseId));
    }

    @Override
    public CommonResult<List<Long>> getAllPurchaseAccountIds(Integer paymentType) {
        return CommonResult.success(chargePurchaseService.getAllPurchaseAccountIds(paymentType));
    }

    @Override
    public CommonResult<List<ResourcePurchaseVO>> getPurchasesListByBillingType(Integer billingType) {
        return CommonResult.success(chargePurchaseService.getPurchasesListByBillingType(billingType));
    }

    @Override
    public CommonResult<List<ServiceCodeInfo>> getAllActiveServiceCodes() {
        return null;
    }
}
