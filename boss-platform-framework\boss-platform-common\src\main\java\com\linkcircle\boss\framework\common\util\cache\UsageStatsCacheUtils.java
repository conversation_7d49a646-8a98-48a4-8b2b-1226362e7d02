package com.linkcircle.boss.framework.common.util.cache;

import com.linkcircle.boss.framework.common.constants.ChargeCacheConstant;

/**
 * <AUTHOR>
 * @date 2025-01-03 15:00
 * @description 使用量统计缓存工具类
 */
public class UsageStatsCacheUtils {

    /**
     * 客户使用量统计缓存键前缀
     */
    private static final String CUSTOMER_USAGE_STATS_PREFIX = ChargeCacheConstant.PREFIX + "customer:usage:stats";

    /**
     * 供应商使用量统计缓存键前缀
     */
    private static final String SUPPLIER_USAGE_STATS_PREFIX = ChargeCacheConstant.PREFIX + "boss:supplier:usage:stats";

    /**
     * Hash 字段名
     */
    public static final String FIELD_CHARGE_USAGE_COUNT = "charge_usage_count";
    public static final String FIELD_USAGE_COUNT = "usage_count";
    public static final String FIELD_CASH_AMOUNT = "cash_amount";
    public static final String FIELD_POINT_AMOUNT = "point_amount";
    public static final String FIELD_USAGE_UNIT = "usage_unit";

    /**
     * 构建客户使用量统计缓存键
     *
     * @param accountId 账户ID
     * @param productId 产品ID
     * @param serviceId 服务ID
     * @param billDate  账单日期
     * @return 缓存键
     */
    public static String buildCustomerUsageStatsKey(Long accountId, Long productId, Long serviceId, String billDate) {
        return String.format("%s:%s:%s:%s:%s",
                CUSTOMER_USAGE_STATS_PREFIX, accountId, productId, serviceId, billDate);
    }

    /**
     * 构建供应商使用量统计缓存键
     *
     * @param accountId 账户ID
     * @param serviceId 服务ID
     * @param billDate  账单日期
     * @return 缓存键
     */
    public static String buildSupplierUsageStatsKey(Long accountId, Long serviceId, String billDate) {
        return String.format("%s:%s:%s:%s",
                SUPPLIER_USAGE_STATS_PREFIX, accountId, serviceId, billDate);
    }
}
