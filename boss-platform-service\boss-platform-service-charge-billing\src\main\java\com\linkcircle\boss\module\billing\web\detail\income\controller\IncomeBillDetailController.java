package com.linkcircle.boss.module.billing.web.detail.income.controller;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.tanant.TenantContextHolder;
import com.linkcircle.boss.module.billing.annotation.BillingAuth;
import com.linkcircle.boss.module.billing.web.detail.income.model.dto.IncomeBillDetailRequestDTO;
import com.linkcircle.boss.module.billing.web.detail.income.model.vo.IncomeBillDetailResponseVO;
import com.linkcircle.boss.module.billing.web.detail.income.service.IncomeBillDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025-06-16 17:06
 * @description 收入账单明细控制器
 */
@Tag(name = "收入账单明细接口", description = "业务平台对接计费平台的收入账单明细接口")
@RestController
@RequestMapping("/billing/api/v3/income")
@RequiredArgsConstructor
@Slf4j
public class IncomeBillDetailController {

    private final IncomeBillDetailService incomeBillDetailService;

    @Operation(
            summary = "创建收入账单明细",
            description = "业务平台推送消费数据到计费平台，生成收入账单明细"
    )
    @PostMapping("/charge-income-bill-detail")
    @BillingAuth
    public CommonResult<IncomeBillDetailResponseVO> createIncomeBillDetail(@Validated @RequestBody IncomeBillDetailRequestDTO requestDTO) {
        requestDTO.setTenantId(TenantContextHolder.getTenantId());
        return CommonResult.success(incomeBillDetailService.createIncomeBillDetail(requestDTO));
    }

}
