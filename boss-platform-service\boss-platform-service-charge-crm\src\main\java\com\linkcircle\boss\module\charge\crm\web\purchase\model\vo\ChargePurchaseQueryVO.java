package com.linkcircle.boss.module.charge.crm.web.purchase.model.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linkcircle.boss.framework.excel.core.annotations.DictFormat;
import com.linkcircle.boss.framework.excel.core.convert.DictConvert;
import com.linkcircle.boss.framework.excel.core.convert.TimestampConvert;
import com.linkcircle.boss.module.system.enums.DictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 供应商资源采购信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@ExcelIgnoreUnannotated
@TableName("charge_purchase")
@Schema(description = "供应商资源采购信息表")
public class ChargePurchaseQueryVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键id")
    @ExcelProperty(value = "采购ID", index = 0)
    private Long id;

    @Schema(description = "供应商id")
    private Long suppliersId;

    @Schema(description = "资源服务")
    @ExcelProperty(value = "资源", index = 1)
    private String resourceServiceNames;

    @Schema(description = "供应商名称")
    @ExcelProperty(value = "供应商", index = 4)
    private String suppliersName;

    @Schema(description = "账户ID")
    private String accountId;

    @Schema(description = "货币代码（ISO 4217 标准，如 CNY 表示人民币）")
    private String currencyCode;

    @Schema(description = "主体ID")
    private String entityId;

    @Schema(description = "主体名称")
    @ExcelProperty(value = "采购主体", index = 2)
    private String entityName;

    @Schema(description = "支付类型，0-预付费，1-后付费")
    private Boolean paymentMethod;

    @Schema(description = "采购开始时间")
    @ExcelProperty(value = "采购开始时间", index = 5, converter = TimestampConvert.class)
    private Long startTime;


    private String startTimeFormat;

    @Schema(description = "采购结束时间")
    @ExcelProperty(value = "采购结束时间", index = 6, converter = TimestampConvert.class)
    private Long endTime;

    @Schema(description = "免费试用天数")
    private Integer freeTrialDays;


    @Schema(description = "是否按比例计算，0：否，1：是")
    private Integer byProportion;


    private String endTimeFormat;


    @Schema(description = "资源采购信息id")
    private Long resourceId;

    @Schema(description = "主体ID（开单主体）")
    private String billingEntityId;

    @Schema(description = "是否含税，0：不含税，1：含税")
    private Integer isTaxInclusive;

    @Schema(description = "税率百分比（示例：9.00）")
    private BigDecimal rate;

    @Schema(description = "出账周期类型（数据字典）")
    private Integer cycleType;

    @Schema(description = "天数")
    private Integer days;

    @Schema(description = "状态，0：已结束，1：已生效，2：试用中，3：已取消")
    @ExcelProperty(value = "状态", index = 3, converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PURCHASE_STATUS)
    private Integer status;

    @Schema(description = "数据添加时间戳")
    @ExcelProperty(value = "创建时间", index = 7, converter = TimestampConvert.class)
    private Long createTime;


    private String createTimeFormat;


}
