package com.linkcircle.boss.module.billing.web.stats.event;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.ApplicationEvent;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-01-03 15:00
 * @description 使用量统计事件基类
 */
@Data
@EqualsAndHashCode(callSuper = false)
public abstract class UsageStatsEvent extends ApplicationEvent {

    /**
     * 账户ID
     */
    private Long accountId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 服务ID
     */
    private Long serviceId;

    /**
     * 账单日期 (格式: yyyyMMddHH)
     */
    private String billDate;

    /**
     * 计费使用量
     */
    private BigDecimal chargeUsageCount;

    /**
     * 使用量
     */
    private BigDecimal usageCount;

    /**
     * 现金金额
     */
    private BigDecimal cashAmount;

    /**
     * 积分金额
     */
    private BigDecimal pointAmount;

    /**
     * 事件时间戳
     */
    private Long eventTimestamp;

    public UsageStatsEvent(Object source) {
        super(source);
        this.eventTimestamp = System.currentTimeMillis();
    }

    /**
     * 获取事件类型
     */
    public abstract String getEventType();
}
