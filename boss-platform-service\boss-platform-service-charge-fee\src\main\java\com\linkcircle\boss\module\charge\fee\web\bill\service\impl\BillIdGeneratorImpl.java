package com.linkcircle.boss.module.charge.fee.web.bill.service.impl;

import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.charge.fee.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.charge.fee.web.bill.mapper.MakeupBillMapper;
import com.linkcircle.boss.module.charge.fee.web.bill.mapper.PostpaidMapper;
import com.linkcircle.boss.module.charge.fee.web.bill.mapper.PrepaidMapper;
import com.linkcircle.boss.module.charge.fee.web.bill.service.BillIdGenerator;
import com.linkcircle.boss.module.charge.fee.web.bill.service.impl.generator.DefaultIdGenerator;
import com.linkcircle.boss.module.charge.fee.web.bill.service.impl.generator.NumberHandler;
import com.linkcircle.boss.module.charge.fee.web.bill.service.impl.generator.YearMonthDayHandler;
import com.linkcircle.boss.module.crm.api.basicConfig.BasicConfigApi;
import com.linkcircle.boss.module.crm.api.basicConfig.vo.InvoiceDetailsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/7/23 16:39
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class BillIdGeneratorImpl implements BillIdGenerator {

    private final BasicConfigApi basicConfigApi;

    private final NumberHandler numberHandler;

    private final YearMonthDayHandler yearMonthDayHandler;

    private final DefaultIdGenerator defaultIdGenerator;

    private final PostpaidMapper postpaidMapper;

    private final PrepaidMapper prepaidMapper;

    private final MakeupBillMapper makeupBillMapper;

    /**
     * 生成唯一ID的方法
     *
     * @param entityId 实体ID
     * @return 生成的唯一ID字符串
     * @throws ServiceException 当账单发票号生成失败时抛出异常
     */
    @Override
    public String generateId(Long entityId, int billType) {
        CommonResult<InvoiceDetailsVO> commonResult = basicConfigApi.queryInvoice(entityId, 1);
        return generateIdInternal(commonResult, entityId, billType, 0);
    }

    private String generateIdInternal(CommonResult<InvoiceDetailsVO> commonResult, Long entityId, int billType, int times) {
        String billCode = null;
        if (commonResult.isSuccess()) {
            if (commonResult.getData() != null && StringUtils.isNotBlank(commonResult.getData().getNumberFormat())) {
                InvoiceDetailsVO config = commonResult.getData();
                billCode = numberHandler.handle(config, yearMonthDayHandler);
            } else {
                log.info("账单号生成失败,未找到规则: {},采用默认规则生成BS+时间戳", entityId);
                billCode = defaultIdGenerator.generateId("BS");
            }
        } else {
            log.info("账单号生成失败,未找到规则: {}", entityId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.BILL_ID_GENERATE_ERROR_NOT_FOUND_RULE, entityId);
        }
        if (StringUtils.isBlank(billCode)) {
            billCode = defaultIdGenerator.generateId("BS");
        }
        // 判断是否重复
        if (isDuplicate(billCode, billType)) {
            if (times < 30) {
                log.info("账单号生成失败,重复: {}", billCode);
                return generateIdInternal(commonResult, entityId, billType, times + 1);
            } else {
                return defaultIdGenerator.generateSnowId("BS");
            }
        }
        return billCode;
    }

    private boolean isDuplicate(String billCode, int billType) {
        switch (billType) {
            case 1:
                return prepaidMapper.checkBillCodeDuplicate(billCode);
            case 2:
                return postpaidMapper.checkBillCodeDuplicate(billCode);
            case 3:
                return makeupBillMapper.checkBillCodeDuplicate(billCode);
        }
        return false;
    }

}
