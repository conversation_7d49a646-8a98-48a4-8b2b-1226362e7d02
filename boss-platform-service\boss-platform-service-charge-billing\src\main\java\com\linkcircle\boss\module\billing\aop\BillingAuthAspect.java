package com.linkcircle.boss.module.billing.aop;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SignUtil;
import cn.hutool.crypto.digest.DigestAlgorithm;
import com.fasterxml.jackson.core.type.TypeReference;
import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.framework.tanant.TenantUtils;
import com.linkcircle.boss.framework.web.context.EnableLoginContext;
import com.linkcircle.boss.module.billing.annotation.BillingAuth;
import com.linkcircle.boss.module.billing.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.billing.web.data.service.SecretKeyDataService;
import com.linkcircle.boss.module.billing.web.data.service.UniquenessCheckDataService;
import com.linkcircle.boss.module.system.api.key.vo.KeyRespVO;
import io.github.kk01001.util.JacksonUtil;
import io.github.kk01001.util.TraceIdUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-06-16 17:06
 * @description 计费平台鉴权AOP切面
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class BillingAuthAspect {

    public static final String HEADER_APP_ID = "X-AppId";
    public static final String HEADER_TENANT_ID = "X-Tenant";
    public static final String HEADER_TIMESTAMP = "X-Timestamp";
    public static final String HEADER_SIGN = "X-Sign";

    private final SecretKeyDataService secretKeyDataService;
    private final UniquenessCheckDataService uniquenessCheckDataService;
    private final HttpServletRequest request;

    @Value("${billing.auth.timestamp-tolerance:300000}")
    private Long timestampTolerance;

    @Around("@annotation(billingAuth)")
    public Object around(ProceedingJoinPoint joinPoint, BillingAuth billingAuth) throws Throwable {
        // 如果不需要鉴权，直接执行
        if (!billingAuth.value()) {
            return joinPoint.proceed();
        }

        // 从请求头获取鉴权参数
        String appId = request.getHeader(HEADER_APP_ID);
        String tenantIdStr = request.getHeader(HEADER_TENANT_ID);
        String timestamp = request.getHeader(HEADER_TIMESTAMP);
        String sign = request.getHeader(HEADER_SIGN);

        // 获取请求参数
        Object[] args = joinPoint.getArgs();
        if (args.length < 1) {
            throw new ServiceException(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "请求参数不能为空");
        }

        // 获取请求对象（第一个参数是DTO）
        Object requestParam = args[0];
        if (requestParam == null) {
            throw new ServiceException(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "请求参数不能为空");
        }
        // 将DTO转换为Map进行验证
        Map<String, Object> paramMap = convertDtoToMap(requestParam);

        String businessId = getBusinessId(paramMap);

        try {
            TraceIdUtil.setTraceId(businessId);
            // 验证必要参数
            validateRequiredParams(paramMap, timestamp, sign);

            // 验证时间戳
            validateTimestamp(timestamp);

            // 执行原方法
            return TenantUtils.executeWithException(Convert.toLong(tenantIdStr), () -> {
                // 验证AppId并获取秘钥
                String secretKey = validateAppIdAndGetSecretKey(appId);

                // 验证签名
                validateSign(paramMap, secretKey, timestamp, sign);

                // 验证唯一性（在验签之后）
                validateUniqueness(paramMap);

                try {
                    EnableLoginContext.setContext(false);
                    return joinPoint.proceed();
                } catch (Throwable e) {
                    log.error("请求失败: ", e);
                    throw new ServiceException(ErrorCodeConstants.REQUEST_FAIL);
                } finally {
                    EnableLoginContext.clearContext();
                }
            });
        } finally {
            TraceIdUtil.remove();
        }
    }

    private String getBusinessId(Map<String, Object> paramMap) {
        return Convert.toStr(paramMap.get("business_id"));
    }

    /**
     * 验证必要参数
     */
    private void validateRequiredParams(Map<String, Object> paramMap, String timestamp, String sign) {
        // 验证请求头参数
        if (StrUtil.isBlank(timestamp)) {
            log.error("请求头参数缺失: {}", HEADER_TIMESTAMP);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.AUTH_PARAM_MISSING, HEADER_TIMESTAMP);
        }
        if (StrUtil.isBlank(sign)) {
            log.error("请求头参数缺失: {}", HEADER_SIGN);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.AUTH_PARAM_MISSING, HEADER_SIGN);
        }

        // 验证请求体参数
        String[] requiredBodyParams = {"request_id", "business_id"};
        for (String param : requiredBodyParams) {
            if (!paramMap.containsKey(param) || paramMap.get(param) == null) {
                log.error("请求体参数缺失: {}", param);
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.AUTH_PARAM_MISSING, param);
            }
        }
    }

    /**
     * 验证时间戳
     */
    private void validateTimestamp(String timestampStr) {
        long timestamp;
        try {
            timestamp = Long.parseLong(timestampStr);
        } catch (NumberFormatException e) {
            throw new ServiceException(ErrorCodeConstants.AUTH_TIMESTAMP_FORMAT_ERROR);
        }

        long currentTime = System.currentTimeMillis();
        if (Math.abs(currentTime - timestamp) > timestampTolerance) {
            log.error("请求时间戳过期, 当前时间: {}, 请求时间: {}, 差值: {}",
                    currentTime, timestamp, Math.abs(currentTime - timestamp));
            throw new ServiceException(ErrorCodeConstants.AUTH_TIMESTAMP_EXPIRED);
        }
    }

    /**
     * 验证AppId并获取秘钥
     */
    private String validateAppIdAndGetSecretKey(String appId) {
        if (StrUtil.isBlank(appId)) {
            log.error("请求头参数缺失: {}", HEADER_APP_ID);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.AUTH_PARAM_MISSING, HEADER_APP_ID);
        }

        KeyRespVO keyRespVO = secretKeyDataService.getSecretKeyByAppId(appId)
                .orElseThrow(() -> {
                    log.error("AppId无效或已禁用: {}", appId);
                    return new ServiceException(ErrorCodeConstants.AUTH_APPID_INVALID);
                });

        if (!keyRespVO.enabled(keyRespVO)) {
            log.error("AppId已禁用: {}", appId);
            throw new ServiceException(ErrorCodeConstants.AUTH_APPID_INVALID);
        }

        log.info("AppId验证通过: {}", appId);
        return keyRespVO.getAppSecretKey();
    }

    /**
     * 验证签名
     */
    private void validateSign(Map<String, Object> paramMap, String secretKey, String timestamp, String requestSign) {
        // 生成签名（需要包含timestamp参数）
        String calculatedSign = generateSign(paramMap, secretKey, timestamp);

        if (!requestSign.equals(calculatedSign)) {
            log.error("签名验证失败, 请求签名: {}, 计算签名: {}", requestSign, calculatedSign);
            throw new ServiceException(ErrorCodeConstants.AUTH_SIGN_INVALID);
        }

        log.info("签名验证通过");
    }

    /**
     * 验证唯一性
     */
    private void validateUniqueness(Map<String, Object> paramMap) {
        String requestId = (String) paramMap.get("request_id");
        String businessId = (String) paramMap.get("business_id");

        // 检查请求ID唯一性
        if (!uniquenessCheckDataService.checkAndSetRequestId(requestId)) {
            log.error("请求ID重复: {}", requestId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REQUEST_ID_DUPLICATE, requestId);
        }

        // 检查业务ID唯一性
        if (!uniquenessCheckDataService.checkAndSetBusinessId(businessId)) {
            log.error("业务ID重复: {}", businessId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.BUSINESS_ID_DUPLICATE, businessId);
        }

        log.info("唯一性验证通过, requestId: {}, businessId: {}", requestId, businessId);
    }

    /**
     * 生成签名
     */
    private String generateSign(Map<String, Object> paramMap, String secretKey, String timestamp) {
        // 创建签名参数Map，包含请求体参数和timestamp
        Map<String, Object> signParamMap = new HashMap<>(paramMap);
        signParamMap.put("timestamp", timestamp);
        signParamMap.remove("sign");

        return SignUtil.signParams(DigestAlgorithm.SHA256, signParamMap, "&", "=", true,
                "&secret_key=" + secretKey);
    }

    /**
     * 将DTO转换为Map
     */
    private Map<String, Object> convertDtoToMap(Object object) {
        Map<String, Object> map = JacksonUtil.convertValue(object, new TypeReference<>() {
        });
        Map<String, Object> paramMap = new HashMap<>(map);
        Object data = map.get("data");
        if (Objects.nonNull(data)) {
            Map<String, Object> dataMap = JacksonUtil.convertValue(data, new TypeReference<>() {
            });
            paramMap.putAll(dataMap);
        }
        paramMap.remove("data");
        return paramMap;
    }
}
