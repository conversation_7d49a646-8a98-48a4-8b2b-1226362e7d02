package com.linkcircle.boss.module.billing.api.stats.supplier.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-18 08:58
 * @description 供应商每日使用量账单表
 */
@TableName("supplier_daily_usage_bill")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierDailyUsageBillDO {

    /**
     * 主键 ID
     */
    @TableId(type = IdType.INPUT)
    private Long id;

    /**
     * 创建时间戳
     */
    private Long createTime;

    /**
     * 账单日期(yyyyMMddhh)
     */
    private String billDate;

    /**
     * 供应商 ID
     */
    private Long supplierId;

    /**
     * 账户 ID
     */
    private Long accountId;

    /**
     * 资源服务 ID
     */
    private Long resourceServiceId;

    /**
     * 使用量
     */
    @TableField("usage_count")
    private BigDecimal usage;

    /**
     * 使用量单位
     */
    private String usageUnit;

    /**
     * 现金消费金额
     */
    private BigDecimal cashAmount;

    /**
     * 现金消费金额货币单位
     */
    private String cashAmountCurrency;

    /**
     * 积分消费金额
     */
    private BigDecimal pointAmount;
}
