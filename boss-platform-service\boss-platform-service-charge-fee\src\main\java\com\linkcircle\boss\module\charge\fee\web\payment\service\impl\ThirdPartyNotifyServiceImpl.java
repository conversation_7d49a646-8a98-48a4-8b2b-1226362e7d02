package com.linkcircle.boss.module.charge.fee.web.payment.service.impl;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.sharding.algorithms.dto.HitBusinessTimeDTO;
import com.linkcircle.boss.module.charge.fee.enums.BillCallbackStatusEnum;
import com.linkcircle.boss.module.charge.fee.web.bill.service.MakeupBillService;
import com.linkcircle.boss.module.charge.fee.web.payment.model.dto.ThirdPartyNotifyDTO;
import com.linkcircle.boss.module.charge.fee.web.payment.service.IPostpaidProductIncomeBillService;
import com.linkcircle.boss.module.charge.fee.web.payment.service.IPostpaidProductServiceIncomeBillService;
import com.linkcircle.boss.module.charge.fee.web.payment.service.IPrepaidIncomeBillDetailService;
import com.linkcircle.boss.module.charge.fee.web.payment.service.IThirdPartyNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class ThirdPartyNotifyServiceImpl implements IThirdPartyNotifyService {

    /**
     * 后付费账单
     */
    @Autowired
    private IPostpaidProductIncomeBillService postpaidProductIncomeBillService;
    @Autowired
    private IPostpaidProductServiceIncomeBillService postpaidProductServiceIncomeBillService;
    /**
     * 预付费账单详情
     */
    @Autowired
    private IPrepaidIncomeBillDetailService prepaidIncomeBillDetailService;
    @Autowired
    private MakeupBillService makeupBillService;

    @Async
    @Override
    @Transactional
    public void notifyThirdParty(String callbackUrl, HitBusinessTimeDTO businessTimeDTO, ThirdPartyNotifyDTO thirdPartyNotifyDTO) {
        try {
            log.info("[支付流程] 第三方回调推送: " + JSONUtil.toJsonPrettyStr(thirdPartyNotifyDTO));
            // 调用第三方接口
            String json = JSONUtil.toJsonPrettyStr(thirdPartyNotifyDTO);
            String response = HttpUtil.post(callbackUrl, json);
            log.info("[支付流程] 第三方回调响应: " + JSONUtil.toJsonPrettyStr(response));

            if (StringUtils.isEmpty(response)) {
                return;
            }

            JSONObject responseJSON = JSONObject.parseObject(response);
            Integer code = responseJSON.getInteger("code");

            boolean result = false;
            if (0 == code) {
                // 第三方响应成功 修改账单回调状态
                result = updateNotifyResult(thirdPartyNotifyDTO.getBillId(), thirdPartyNotifyDTO.getBillType(), BillCallbackStatusEnum.SUCCESS.getCode(), businessTimeDTO);
            } else {
                // 第三方响应失败 修改账单回调状态
                result = updateNotifyResult(thirdPartyNotifyDTO.getBillId(), thirdPartyNotifyDTO.getBillType(), BillCallbackStatusEnum.FAIL.getCode(), businessTimeDTO);
            }

            if (!result) {
                log.error("[支付流程] 修改账单回调状态失败，billId:{},billType:{}", thirdPartyNotifyDTO.getBillId(), thirdPartyNotifyDTO.getBillType());
                throw new ServiceException(500, "[修改账单回调状态]修改账单回调状态失败");
            }
        } catch (Exception e) {
            log.error("[支付流程] 第三方回调响应: {}", e.getMessage());
            throw new ServiceException(500, "[第三方回调响应]第三方回调响应异常");
        }
    }

    @Transactional
    public boolean updateNotifyResult(Long billId, Integer billType, Integer callbackStatus, HitBusinessTimeDTO businessTimeDTO) throws Exception {
        boolean result = false;
        // 更新通知状态
        switch (billType) {
            case 0:
                result = prepaidIncomeBillDetailService.updateBillCallbackStatus(billId, callbackStatus, businessTimeDTO);
                break;
            case 1:
                result = postpaidProductIncomeBillService.updateBillCallbackStatus(billId, callbackStatus);
                break;
            default:
                break;
        }
        return result;
    }
}
