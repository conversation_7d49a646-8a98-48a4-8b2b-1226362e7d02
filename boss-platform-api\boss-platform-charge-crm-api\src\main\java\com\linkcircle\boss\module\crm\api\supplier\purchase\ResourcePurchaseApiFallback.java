package com.linkcircle.boss.module.crm.api.supplier.purchase;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.crm.api.productservice.vo.ServiceCodeInfo;
import com.linkcircle.boss.module.crm.api.supplier.purchase.vo.ResourcePurchaseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-04 17:21
 * @description 供应商资源采购API降级工厂实现
 */
@Slf4j
@Component
public class ResourcePurchaseApiFallback implements FallbackFactory<ResourcePurchaseApi> {

    @Override
    public ResourcePurchaseApi create(Throwable cause) {
        return new ResourcePurchaseApi() {

            @Override
            public CommonResult<List<ResourcePurchaseVO>> getAccountSubscriptionsList(Long accountId) {
                log.error("调用供应商资源采购API失败，accountId: {}, 异常信息: ", accountId, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }

            @Override
            public CommonResult<ResourcePurchaseVO> getPurchaseDetail(Long purchaseId) {
                log.error("调用供应商资源采购API失败，purchaseId: {}, 异常信息: ", purchaseId, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }

            @Override
            public CommonResult<List<Long>> getAllPurchaseAccountIds(Integer paymentType) {
                log.error("调用供应商资源采购API失败，paymentType: {}, 异常信息: ", paymentType, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }

            @Override
            public CommonResult<List<ResourcePurchaseVO>> getPurchasesListByBillingType(Integer billingType) {
                log.error("调用供应商资源采购API失败，billingType: {}, 异常信息: ", billingType, cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }

            @Override
            public CommonResult<List<ServiceCodeInfo>> getAllActiveServiceCodes() {
                log.error("调用供应商资源采购API失败，异常信息: ", cause);
                return CommonResult.error(500, "调用失败: " + cause.getMessage());
            }
        };
    }

}
