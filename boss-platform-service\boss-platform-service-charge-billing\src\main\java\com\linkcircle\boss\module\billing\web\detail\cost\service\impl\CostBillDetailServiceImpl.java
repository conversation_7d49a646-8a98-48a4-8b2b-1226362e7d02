package com.linkcircle.boss.module.billing.web.detail.cost.service.impl;

import cn.hutool.json.JSONUtil;
import com.linkcircle.boss.framework.common.util.topic.ChargeTopicUtils;
import com.linkcircle.boss.module.billing.web.detail.cost.convert.CostBillDetailConvert;
import com.linkcircle.boss.module.billing.web.detail.cost.model.dto.CostBillDetailRequestDTO;
import com.linkcircle.boss.module.billing.web.detail.cost.model.dto.ReceiveCostBillMqDTO;
import com.linkcircle.boss.module.billing.web.detail.cost.model.vo.CostBillDetailResponseVO;
import com.linkcircle.boss.module.billing.web.detail.cost.service.CostBillDetailService;
import com.linkcircle.boss.module.billing.web.fail.util.FailureRecordUtil;
import com.linkcircle.boss.module.billing.web.manager.BillIdManager;
import com.linkcircle.boss.module.billing.web.manager.ProducerManager;
import com.linkcircle.boss.module.crm.enums.BillTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2025-06-18 11:04
 * @description 成本账单明细服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CostBillDetailServiceImpl implements CostBillDetailService {

    private final BillIdManager billIdManager;
    private final ProducerManager producerManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CostBillDetailResponseVO createCostBillDetail(CostBillDetailRequestDTO requestDTO) {
        log.info("创建成本账单明细, 请求参数: {}", JSONUtil.toJsonStr(requestDTO));

        // 1. 生成唯一账单ID
        String billId = billIdManager.createBillIdNow(BillTypeEnum.COST);

        // 2. 构造MQ消息
        ReceiveCostBillMqDTO costBillMqDTO = ReceiveCostBillMqDTO.builder()
                .billId(billId)
                .requestParams(requestDTO)
                .tenantId(requestDTO.getTenantId())
                .sendTime(System.currentTimeMillis())
                .retryCount(0)
                .build();

        // 3. 发送MQ消息
        boolean mqSendSuccess = producerManager.sendNormalMessage(ChargeTopicUtils.getChargeReceiveCostBill(), billId, costBillMqDTO);
        if (!mqSendSuccess) {
            //  MQ发送失败，写入本地文件
            FailureRecordUtil.recordCostBillDetailSendMqFailure(billId, costBillMqDTO);
        }

        // 4. 构造响应结果
        CostBillDetailResponseVO responseVO = CostBillDetailConvert.INSTANCE.toResponseVO(requestDTO);
        responseVO.setBillDetailId(Long.valueOf(billId));
        responseVO.setTimestamp(System.currentTimeMillis());
        log.info("创建成本账单明细成功, billId: {}, accountId: {}", billId, requestDTO.getAccountId());
        return responseVO;
    }


}
