package com.linkcircle.boss.module.charge.crm.web.purchase.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linkcircle.boss.framework.common.model.common.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 供应商资源采购信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@TableName("charge_purchase")
@Schema(description = "供应商资源采购信息表")
public class ChargePurchase extends BaseDO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "供应商id")
    @TableField("suppliers_id")
    private Long suppliersId;

    @Schema(description = "账户ID")
    @TableField("account_id")
    private Long accountId;

    @Schema(description = "合同ID")
    @TableField("contract_id")
    private Long contractId;

    @Schema(description = "货币代码（ISO 4217 标准，如 CNY 表示人民币）")
    @TableField("currency_code")
    private String currencyCode;

    @Schema(description = "主体ID")
    @TableField("entity_id")
    private Long entityId;

    @Schema(description = "支付类型，0-预付费，1-后付费")
    @TableField("payment_type")
    private Integer paymentType;

    @Schema(description = "采购开始时间")
    @TableField("start_time")
    private Long startTime;

    @Schema(description = "采购结束时间")
    @TableField("end_time")
    private Long endTime;

    @Schema(description = "免费试用天数")
    @TableField("free_trial_days")
    private Integer freeTrialDays;


    @Schema(description = "是否按比例计算，0：否，1：是")
    @TableField("by_proportion")
    private Integer byProportion;

    @Schema(description = "主体ID（开单主体）")
    @TableField("billing_entity_id")
    private Long billingEntityId;

    @Schema(description = "是否含税，0：不含税，1：含税")
    @TableField("is_tax_inclusive")
    private Boolean isTaxInclusive;

    @Schema(description = "税率百分比（示例：9.00）")
    @TableField("rate")
    private Integer rate;

    @Schema(description = "出账周期类型（数据字典）")
    @TableField("cycle_type")
    private Integer cycleType;

    @Schema(description = "天数")
    @TableField("days")
    private Integer days;

    @Schema(description = "状态，0：已结束，1：已生效，2：试用中，3：已取消")
    @TableField("`status`")
    private Integer status;

    @Schema(description = "租户编号")
    @TableField("tenant_id")
    private Long tenantId;


}
