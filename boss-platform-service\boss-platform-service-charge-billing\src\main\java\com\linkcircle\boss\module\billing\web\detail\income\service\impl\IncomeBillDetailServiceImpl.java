package com.linkcircle.boss.module.billing.web.detail.income.service.impl;

import cn.hutool.json.JSONUtil;
import com.linkcircle.boss.framework.common.util.topic.ChargeTopicUtils;
import com.linkcircle.boss.module.billing.web.detail.income.convert.IncomeBillDetailConvert;
import com.linkcircle.boss.module.billing.web.detail.income.model.dto.IncomeBillDetailRequestDTO;
import com.linkcircle.boss.module.billing.web.detail.income.model.dto.ReceiveIncomeBillMqDTO;
import com.linkcircle.boss.module.billing.web.detail.income.model.vo.IncomeBillDetailResponseVO;
import com.linkcircle.boss.module.billing.web.detail.income.service.IncomeBillDetailService;
import com.linkcircle.boss.module.billing.web.fail.util.FailureRecordUtil;
import com.linkcircle.boss.module.billing.web.manager.BillIdManager;
import com.linkcircle.boss.module.billing.web.manager.ProducerManager;
import com.linkcircle.boss.module.crm.enums.BillTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2025-06-16 17:06
 * @description 收入账单明细服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class IncomeBillDetailServiceImpl implements IncomeBillDetailService {

    private final BillIdManager billIdManager;
    private final ProducerManager producerManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IncomeBillDetailResponseVO createIncomeBillDetail(IncomeBillDetailRequestDTO requestDTO) {
        log.info("创建收入账单明细, 请求参数: {}", JSONUtil.toJsonStr(requestDTO));

        // 1. 生成唯一账单ID
        String billId = billIdManager.createBillIdNow(BillTypeEnum.INCOME);

        // 2. 构造MQ消息
        ReceiveIncomeBillMqDTO incomeBillMqDTO = ReceiveIncomeBillMqDTO.builder()
                .billId(billId)
                .requestParams(requestDTO)
                .sendTime(System.currentTimeMillis())
                .tenantId(requestDTO.getTenantId())
                .retryCount(0)
                .build();

        // 3. 发送MQ消息
        boolean mqSendSuccess = producerManager.sendNormalMessage(ChargeTopicUtils.getChargeReceiveIncomeBill(), billId, incomeBillMqDTO);
        if (!mqSendSuccess) {
            //  MQ发送失败，写入本地文件
            FailureRecordUtil.recordIncomeBillDetailSendMqFailure(billId, incomeBillMqDTO);
        }

        // 4. 构造响应结果
        IncomeBillDetailResponseVO responseVO = IncomeBillDetailConvert.INSTANCE.toResponseVO(requestDTO);
        responseVO.setBillDetailId(Long.valueOf(billId));
        responseVO.setTimestamp(System.currentTimeMillis());
        log.info("创建收入账单明细成功, billId: {}, accountId: {}", billId, requestDTO.getAccountId());
        return responseVO;
    }

}
