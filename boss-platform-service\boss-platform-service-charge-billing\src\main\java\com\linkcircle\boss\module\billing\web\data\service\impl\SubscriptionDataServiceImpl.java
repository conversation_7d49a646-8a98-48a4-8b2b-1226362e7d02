package com.linkcircle.boss.module.billing.web.data.service.impl;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.util.cache.ChargeCacheUtils;
import com.linkcircle.boss.framework.common.util.cache.FunctionUtil;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.module.billing.api.cache.dto.CacheUpdateMessageDTO;
import com.linkcircle.boss.module.billing.web.cache.manager.LocalCacheManager;
import com.linkcircle.boss.module.billing.web.data.service.SubscriptionDataService;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.ChargeSubscriptionsApi;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;
import com.linkcircle.boss.module.crm.api.productservice.vo.ServiceCodeInfo;
import com.linkcircle.boss.module.crm.enums.PaymentTypeEnum;
import io.github.kk01001.redis.RedissonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-06-17 11:25
 * @description 订阅信息数据服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SubscriptionDataServiceImpl implements SubscriptionDataService {

    private final RedissonUtil redissonUtil;
    private final ChargeSubscriptionsApi chargeSubscriptionsApi;
    private final LocalCacheManager localCacheManager;

    /**
     * 缓存过期时间 - 30分钟
     */
    private static final Duration CACHE_EXPIRE_TIME = Duration.ofHours(8);

    @Override
    public Optional<List<AccountSubscriptionsVO>> getAccountSubscriptions(Long accountId, PaymentTypeEnum paymentType) {
        String accountIdStr = String.valueOf(accountId);

        // 1. 先从本地缓存查询
        Optional<List<AccountSubscriptionsVO>> localCached = localCacheManager.getList(
                CacheUpdateMessageDTO.Type.ACCOUNT_SUBSCRIPTION,
                accountIdStr
        );
        if (localCached.isPresent()) {
            log.debug("从本地缓存获取账户订阅列表: accountId={}", accountId);
            return localCached;
        }

        // 2. 本地缓存未命中，从Redis缓存查询
        String redisCacheKey = ChargeCacheUtils.getAccountSubscriptionsKey(accountId);
        Optional<List<AccountSubscriptionsVO>> optional = FunctionUtil.getCachedOrLoadDb(redisCacheKey,
                () -> {
                    String data = redissonUtil.get(redisCacheKey);
                    if (StrUtil.isEmpty(data)) {
                        return Optional.empty();
                    }
                    return Optional.ofNullable(JsonUtils.parseObjectQuietly(data, new TypeReference<>() {
                    }));
                },
                () -> {
                    CommonResult<List<AccountSubscriptionsVO>> result = chargeSubscriptionsApi.getAccountSubscriptionsList(accountId, null);
                    if (Objects.isNull(result)) {
                        return Optional.empty();
                    }
                    return Optional.of(result.getCheckedData());
                },
                data -> {
                    redissonUtil.set(redisCacheKey, JsonUtils.toJsonString(data), CACHE_EXPIRE_TIME);
                });

        // 3. 如果从Redis或API获取到数据，同时更新本地缓存
        optional.ifPresent(subscriptions -> {
            localCacheManager.put(CacheUpdateMessageDTO.Type.ACCOUNT_SUBSCRIPTION, accountIdStr, subscriptions);
            log.debug("更新本地缓存账户订阅列表: accountId={}", accountId);
        });

        return optional;
    }

    @Override
    public Optional<AccountSubscriptionsVO> getSubscriptionDetail(Long subscriptionId) {
        String cacheKey = ChargeCacheUtils.getSubscriptionsDetailKey(subscriptionId);
        return FunctionUtil.getCachedOrLoadDb(cacheKey,
                () -> Optional.ofNullable(redissonUtil.get(cacheKey, AccountSubscriptionsVO.class)),
                () -> {
                    CommonResult<AccountSubscriptionsVO> result = chargeSubscriptionsApi.getSubscriptionDetail(subscriptionId);
                    if (Objects.isNull(result)) {
                        return Optional.empty();
                    }
                    return Optional.of(result.getCheckedData());
                },
                data -> {
                    redissonUtil.set(cacheKey, JsonUtils.toJsonString(data), CACHE_EXPIRE_TIME);
                });
    }

    @Override
    public List<Long> getAllSubscriptionAccountIds(Integer paymentType) {
        try {
            CommonResult<List<Long>> result = chargeSubscriptionsApi.getAllSubscriptionAccountIds(paymentType);
            if (Objects.isNull(result)) {
                log.warn("获取全部订阅账户ID列表失败，支付类型: {}", paymentType);
                return List.of();
            }
            List<Long> accountIds = result.getCheckedData();
            log.debug("获取全部订阅账户ID列表成功，支付类型: {}, 数量: {}", paymentType, accountIds.size());
            return accountIds;
        } catch (Exception e) {
            log.error("获取全部订阅账户ID列表异常，支付类型: {}", paymentType, e);
            return List.of();
        }
    }

    @Override
    public List<AccountSubscriptionsVO> getSubscriptionsListByBillingType(Integer billingType) {
        try {
            CommonResult<List<AccountSubscriptionsVO>> result = chargeSubscriptionsApi.getSubscriptionsListByBillingType(billingType);
            if (Objects.isNull(result)) {
                log.warn("根据计费类型获取订阅列表失败，计费类型: {}", billingType);
                return List.of();
            }
            List<AccountSubscriptionsVO> subscriptions = result.getCheckedData();
            log.debug("根据计费类型获取订阅列表成功，计费类型: {}, 数量: {}", billingType, subscriptions.size());
            return subscriptions;
        } catch (Exception e) {
            log.error("根据计费类型获取订阅列表异常，计费类型: {}", billingType, e);
            return List.of();
        }
    }

    @Override
    public List<ServiceCodeInfo> getAllActiveServiceCodes() {
        CommonResult<List<ServiceCodeInfo>> result = chargeSubscriptionsApi.getAllActiveServiceCodes();
        return result.getCheckedData();
    }

}
