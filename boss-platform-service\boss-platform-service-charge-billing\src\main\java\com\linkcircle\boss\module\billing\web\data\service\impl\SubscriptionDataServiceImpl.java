package com.linkcircle.boss.module.billing.web.data.service.impl;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.util.cache.ChargeCacheUtils;
import com.linkcircle.boss.framework.common.util.cache.FunctionUtil;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.module.billing.api.cache.dto.CacheUpdateMessageDTO;
import com.linkcircle.boss.module.billing.web.cache.manager.LocalCacheManager;
import com.linkcircle.boss.module.billing.web.data.model.dto.ServiceCodeInfo;
import com.linkcircle.boss.module.billing.web.data.service.SubscriptionDataService;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.ChargeSubscriptionsApi;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;
import com.linkcircle.boss.module.crm.enums.PaymentTypeEnum;
import io.github.kk01001.redis.RedissonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-06-17 11:25
 * @description 订阅信息数据服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SubscriptionDataServiceImpl implements SubscriptionDataService {

    private final RedissonUtil redissonUtil;
    private final ChargeSubscriptionsApi chargeSubscriptionsApi;
    private final LocalCacheManager localCacheManager;

    /**
     * 缓存过期时间 - 30分钟
     */
    private static final Duration CACHE_EXPIRE_TIME = Duration.ofHours(8);

    @Override
    public Optional<List<AccountSubscriptionsVO>> getAccountSubscriptions(Long accountId, PaymentTypeEnum paymentType) {
        String accountIdStr = String.valueOf(accountId);

        // 1. 先从本地缓存查询
        Optional<List<AccountSubscriptionsVO>> localCached = localCacheManager.getList(
                CacheUpdateMessageDTO.Type.ACCOUNT_SUBSCRIPTION,
                accountIdStr
        );
        if (localCached.isPresent()) {
            log.debug("从本地缓存获取账户订阅列表: accountId={}", accountId);
            return localCached;
        }

        // 2. 本地缓存未命中，从Redis缓存查询
        String redisCacheKey = ChargeCacheUtils.getAccountSubscriptionsKey(accountId);
        Optional<List<AccountSubscriptionsVO>> optional = FunctionUtil.getCachedOrLoadDb(redisCacheKey,
                () -> {
                    String data = redissonUtil.get(redisCacheKey);
                    if (StrUtil.isEmpty(data)) {
                        return Optional.empty();
                    }
                    return Optional.ofNullable(JsonUtils.parseObjectQuietly(data, new TypeReference<>() {
                    }));
                },
                () -> {
                    CommonResult<List<AccountSubscriptionsVO>> result = chargeSubscriptionsApi.getAccountSubscriptionsList(accountId, null);
                    if (Objects.isNull(result)) {
                        return Optional.empty();
                    }
                    return Optional.of(result.getCheckedData());
                },
                data -> {
                    redissonUtil.set(redisCacheKey, JsonUtils.toJsonString(data), CACHE_EXPIRE_TIME);
                });

        // 3. 如果从Redis或API获取到数据，同时更新本地缓存
        optional.ifPresent(subscriptions -> {
            localCacheManager.put(CacheUpdateMessageDTO.Type.ACCOUNT_SUBSCRIPTION, accountIdStr, subscriptions);
            log.debug("更新本地缓存账户订阅列表: accountId={}", accountId);
        });

        return optional;
    }

    @Override
    public Optional<AccountSubscriptionsVO> getSubscriptionDetail(Long subscriptionId) {
        String cacheKey = ChargeCacheUtils.getSubscriptionsDetailKey(subscriptionId);
        return FunctionUtil.getCachedOrLoadDb(cacheKey,
                () -> Optional.ofNullable(redissonUtil.get(cacheKey, AccountSubscriptionsVO.class)),
                () -> {
                    CommonResult<AccountSubscriptionsVO> result = chargeSubscriptionsApi.getSubscriptionDetail(subscriptionId);
                    if (Objects.isNull(result)) {
                        return Optional.empty();
                    }
                    return Optional.of(result.getCheckedData());
                },
                data -> {
                    redissonUtil.set(cacheKey, JsonUtils.toJsonString(data), CACHE_EXPIRE_TIME);
                });
    }

    @Override
    public List<Long> getAllSubscriptionAccountIds(Integer paymentType) {
        try {
            CommonResult<List<Long>> result = chargeSubscriptionsApi.getAllSubscriptionAccountIds(paymentType);
            if (Objects.isNull(result)) {
                log.warn("获取全部订阅账户ID列表失败，支付类型: {}", paymentType);
                return List.of();
            }
            List<Long> accountIds = result.getCheckedData();
            log.debug("获取全部订阅账户ID列表成功，支付类型: {}, 数量: {}", paymentType, accountIds.size());
            return accountIds;
        } catch (Exception e) {
            log.error("获取全部订阅账户ID列表异常，支付类型: {}", paymentType, e);
            return List.of();
        }
    }

    @Override
    public List<AccountSubscriptionsVO> getSubscriptionsListByBillingType(Integer billingType) {
        try {
            CommonResult<List<AccountSubscriptionsVO>> result = chargeSubscriptionsApi.getSubscriptionsListByBillingType(billingType);
            if (Objects.isNull(result)) {
                log.warn("根据计费类型获取订阅列表失败，计费类型: {}", billingType);
                return List.of();
            }
            List<AccountSubscriptionsVO> subscriptions = result.getCheckedData();
            log.debug("根据计费类型获取订阅列表成功，计费类型: {}, 数量: {}", billingType, subscriptions.size());
            return subscriptions;
        } catch (Exception e) {
            log.error("根据计费类型获取订阅列表异常，计费类型: {}", billingType, e);
            return List.of();
        }
    }

    @Override
    public List<ServiceCodeInfo> getAllActiveServiceCodes() {
        String cacheKey = ChargeCacheUtils.buildServiceCodeCacheKey();
        log.debug("获取所有活跃的服务编码信息，缓存键: {}", cacheKey);

        try {
            // 先从Redis缓存查询
            String cachedData = redissonUtil.get(cacheKey);
            if (StrUtil.isNotBlank(cachedData)) {
                List<ServiceCodeInfo> serviceCodeInfos = JsonUtils.parseObject(cachedData, new TypeReference<List<ServiceCodeInfo>>() {});
                if (serviceCodeInfos != null && !serviceCodeInfos.isEmpty()) {
                    log.debug("从缓存获取到{}个服务编码信息", serviceCodeInfos.size());
                    return serviceCodeInfos;
                }
            }

            // 缓存未命中，从远程API获取
            List<ServiceCodeInfo> serviceCodeInfos = fetchAllActiveServiceCodesFromApi();

            // 缓存结果
            if (!serviceCodeInfos.isEmpty()) {
                redissonUtil.set(cacheKey, JsonUtils.toJsonString(serviceCodeInfos), Duration.ofHours(1));
                log.debug("已缓存{}个服务编码信息", serviceCodeInfos.size());
            }

            return serviceCodeInfos;
        } catch (Exception e) {
            log.error("获取所有活跃的服务编码信息异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 从远程API获取所有活跃的服务编码信息
     */
    private List<ServiceCodeInfo> fetchAllActiveServiceCodesFromApi() {
        log.debug("从远程API获取所有活跃的服务编码信息");

        try {
            // 获取所有活跃的订阅信息（预付费和后付费）
            List<ServiceCodeInfo> allServiceCodes = new ArrayList<>();

            // 获取预付费订阅的服务编码
            List<AccountSubscriptionsVO> prepaidSubscriptions = getSubscriptionsListByBillingType(null);
            List<ServiceCodeInfo> prepaidServiceCodes = extractServiceCodesFromSubscriptions(prepaidSubscriptions);
            allServiceCodes.addAll(prepaidServiceCodes);

            log.debug("从订阅信息中提取到{}个服务编码", allServiceCodes.size());

            // 去重并返回
            return allServiceCodes.stream()
                    .filter(Objects::nonNull)
                    .filter(info -> StrUtil.isNotBlank(info.getServiceCode()))
                    .collect(Collectors.toMap(
                            info -> info.getServiceCode() + "_" + info.getPaymentType(),
                            info -> info,
                            (existing, replacement) -> existing))
                    .values()
                    .stream()
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("从远程API获取服务编码信息异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 从订阅信息中提取服务编码
     */
    private List<ServiceCodeInfo> extractServiceCodesFromSubscriptions(List<AccountSubscriptionsVO> subscriptions) {
        List<ServiceCodeInfo> serviceCodeInfos = new ArrayList<>();

        for (AccountSubscriptionsVO subscription : subscriptions) {
            if (subscription.getDetails() == null) {
                continue;
            }

            for (AccountSubscriptionsVO.Detail detail : subscription.getDetails()) {
                if (detail.getProducts() == null) {
                    continue;
                }

                for (AccountSubscriptionsVO.Product product : detail.getProducts()) {
                    if (product.getServices() == null) {
                        continue;
                    }

                    for (AccountSubscriptionsVO.Service service : product.getServices()) {
                        if (StrUtil.isNotBlank(service.getServiceCode())) {
                            ServiceCodeInfo serviceCodeInfo = ServiceCodeInfo.builder()
                                    .serviceCode(service.getServiceCode())
                                    .paymentType(subscription.getPaymentType())
                                    .build();
                            serviceCodeInfos.add(serviceCodeInfo);
                        }
                    }
                }
            }
        }

        return serviceCodeInfos;
    }

}
