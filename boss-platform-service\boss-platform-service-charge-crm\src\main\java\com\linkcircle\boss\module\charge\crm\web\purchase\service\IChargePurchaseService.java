package com.linkcircle.boss.module.charge.crm.web.purchase.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.module.charge.crm.web.purchase.model.ChargePurchase;
import com.linkcircle.boss.module.charge.crm.web.purchase.model.dto.ChargePurchaseAddDTO;
import com.linkcircle.boss.module.charge.crm.web.purchase.model.dto.ChargePurchaseQueryDTO;
import com.linkcircle.boss.module.charge.crm.web.purchase.model.dto.ChargePurchaseUpdateDTO;
import com.linkcircle.boss.module.charge.crm.web.purchase.model.vo.ChargePurchaseQueryVO;
import com.linkcircle.boss.module.charge.crm.web.purchase.model.vo.ResourcePurchaseDetailVO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.vo.ChargeResourceServiceVersionInfoVO;
import com.linkcircle.boss.module.crm.api.supplier.purchase.vo.ResourcePurchaseVO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * <p>
 * 供应商资源采购信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
public interface IChargePurchaseService extends IService<ChargePurchase> {


    PageResult<ChargePurchaseQueryVO> pageQuery(@Valid ChargePurchaseQueryDTO queryDTO);

    List<ChargeResourceServiceVersionInfoVO> getResourceServiceBySupplierId(Long supplierId, String CurrencyCode);

    List<ResourcePurchaseVO> getAccountSubscriptionsList(Long accountId);


    Long add(ChargePurchaseAddDTO addDTO);

    boolean edit(@Valid ChargePurchaseUpdateDTO updateDTO);

    boolean cancel(Long id);

    List<Long> getAllPurchaseAccountIds(Integer paymentType);

    ResourcePurchaseVO getPurchaseDetail(Long purchaseId);

    ResourcePurchaseDetailVO getPurchaseDetailByPurchaseId(Long purchaseId);

    List<ResourcePurchaseVO> getPurchasesListByBillingType(Integer billingType);

    Object getPurchaseInfoByContractId(Long contractId);
}
