
package com.linkcircle.boss.module.report.web.financial.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.linkcircle.boss.framework.tanant.TenantBaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * '明细模板' DO
 *
 * <AUTHOR>
 */
@Schema(description = "财务 - 明细模板 - 列明细 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChargeDetailTemplateColumnReqDTO  {


    @Schema(description = "列明", example = "客户")
    @NotEmpty(message = "列明不能为空")
    private String columnName;

    @Schema(description = "导出名称/数据名称", example = "customer_name")
    @NotEmpty(message = "数据来源不能为空")
    private String exportName;


    @Schema(description = "是否作为查询字段 0/null 不是 1 值查询  2 范围查询 3 时间范围查询  ", example = "0")
    private Integer queryType;



    // 只有在queryType为1时才会有这个字段
    @Schema(description = "查询关联字段  比如  客户id 和账户id  在查询时需要关联  --- 这里的限制条件是 要和关联字段的逻辑名称和表名一致  不能和columnName一致  否则会报错")
    private String queryAssociationName;


    @Schema(description = "导出关联字段")
    private ChargeDetailTemplateColumnAssociationReqDTO association;



}