package com.linkcircle.boss.module.billing.web.stats.cache;

/**
 * <AUTHOR>
 * @date 2025-01-03 15:00
 * @description 使用量统计缓存工具类
 */
public class UsageStatsCacheUtils {

    /**
     * 客户使用量统计缓存键前缀
     */
    private static final String CUSTOMER_USAGE_STATS_PREFIX = "customer:usage:stats";

    /**
     * 供应商使用量统计缓存键前缀
     */
    private static final String SUPPLIER_USAGE_STATS_PREFIX = "supplier:usage:stats";

    /**
     * 缓存过期时间（7天，单位：秒）
     */
    public static final long CACHE_EXPIRE_SECONDS = 7 * 24 * 60 * 60L;

    /**
     * Hash 字段名
     */
    public static final String FIELD_CHARGE_USAGE_COUNT = "charge_usage_count";
    public static final String FIELD_USAGE_COUNT = "usage_count";
    public static final String FIELD_CASH_AMOUNT = "cash_amount";
    public static final String FIELD_POINT_AMOUNT = "point_amount";

    /**
     * 构建客户使用量统计缓存键
     *
     * @param accountId 账户ID
     * @param productId 产品ID
     * @param serviceId 服务ID
     * @param billDate  账单日期
     * @return 缓存键
     */
    public static String buildCustomerUsageStatsKey(Long accountId, Long productId, Long serviceId, String billDate) {
        return String.format("%s:%s:%s:%s:%s", 
                CUSTOMER_USAGE_STATS_PREFIX, accountId, productId, serviceId, billDate);
    }

    /**
     * 构建供应商使用量统计缓存键
     *
     * @param accountId 账户ID
     * @param serviceId 服务ID
     * @param billDate  账单日期
     * @return 缓存键
     */
    public static String buildSupplierUsageStatsKey(Long accountId, Long serviceId, String billDate) {
        return String.format("%s:%s:%s:%s", 
                SUPPLIER_USAGE_STATS_PREFIX, accountId, serviceId, billDate);
    }

    /**
     * 构建客户使用量统计模式键（用于批量查询）
     *
     * @param accountId 账户ID
     * @param productId 产品ID
     * @param serviceId 服务ID
     * @return 模式键
     */
    public static String buildCustomerUsageStatsPattern(Long accountId, Long productId, Long serviceId) {
        return String.format("%s:%s:%s:%s:*", 
                CUSTOMER_USAGE_STATS_PREFIX, accountId, productId, serviceId);
    }

    /**
     * 构建供应商使用量统计模式键（用于批量查询）
     *
     * @param accountId 账户ID
     * @param serviceId 服务ID
     * @return 模式键
     */
    public static String buildSupplierUsageStatsPattern(Long accountId, Long serviceId) {
        return String.format("%s:%s:%s:*", 
                SUPPLIER_USAGE_STATS_PREFIX, accountId, serviceId);
    }
}
