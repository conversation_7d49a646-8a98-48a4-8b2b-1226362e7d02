package com.linkcircle.boss.module.report.web.financial.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.mybatis.core.util.MyBatisUtils;
import com.linkcircle.boss.module.report.web.dynamic.service.DynamicJdbcTemplate;
import com.linkcircle.boss.module.report.web.financial.convert.ChargeDetailTemplateConvert;
import com.linkcircle.boss.module.report.web.financial.mapper.ChargeDetailTemplateMapper;
import com.linkcircle.boss.module.report.web.financial.model.dto.ChargeDetailTemplateColumnAssociationReqDTO;
import com.linkcircle.boss.module.report.web.financial.model.dto.ChargeDetailTemplateColumnReqDTO;
import com.linkcircle.boss.module.report.web.financial.model.dto.ChargeDetailTemplatePageQueryVo;
import com.linkcircle.boss.module.report.web.financial.model.dto.ChargeDetailTemplateReqDTO;
import com.linkcircle.boss.module.report.web.financial.model.entity.ChargeDetailTemplateDO;
import com.linkcircle.boss.module.report.web.financial.model.vo.ChargeDetailTemplateColumnVO;
import com.linkcircle.boss.module.report.web.financial.model.vo.ChargeDetailTemplateRespVO;
import com.linkcircle.boss.module.report.web.financial.service.ChargeDetailTemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.linkcircle.boss.module.report.constants.ErrorCodeConstants.*;


/**
 * <AUTHOR>
 * @date 2025/6/12 16:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChargeDetailTemplateServiceImpl implements ChargeDetailTemplateService {


    private final ChargeDetailTemplateMapper chargeDetailTemplateMapper;

    private final DynamicJdbcTemplate dynamicJdbcTemplate;

    @Override
    public void createTemplate(ChargeDetailTemplateReqDTO templateReqDTO) {
        log.info("createTemplate: {}", templateReqDTO);
        ChargeDetailTemplateDO template = chargeDetailTemplateMapper.selectByTemplateName(templateReqDTO.getTemplateName());
        if (template != null) {
            log.error("template name already exists: {}", templateReqDTO.getTemplateName());
            throw new ServiceException(FINANCE_TEMPLATE_NAME_DUPLICATE.getCode(), FINANCE_TEMPLATE_NAME_DUPLICATE.getMsg());
        }
        ChargeDetailTemplateDO templateDO = ChargeDetailTemplateConvert.INSTANCE.convert(templateReqDTO);
        List<ChargeDetailTemplateColumnReqDTO> columns = templateReqDTO.getColumns();
        if (CollectionUtils.isEmpty(columns)) {
            log.error("columns is empty");
            throw new ServiceException(FINANCE_TEMPLATE_COLUMN_EMPTY.getCode(), FINANCE_TEMPLATE_COLUMN_EMPTY.getMsg());
        }
        for (ChargeDetailTemplateColumnReqDTO column : columns) {
            ChargeDetailTemplateColumnAssociationReqDTO association = column.getAssociation();
            if(association!= null && StringUtils.isBlank(association.getLogicName())){
                column.setAssociation(null);
            }
            dynamicJdbcTemplate.checkExists(column, templateDO.getLogicName(), templateDO.getTableName());
        }
        templateDO.setExportColumns(JSONUtil.toJsonStr(columns));
        chargeDetailTemplateMapper.insert(templateDO);
    }

    @Override
    public void updateTemplate(ChargeDetailTemplateReqDTO templateReqDTO) {
        log.info("updateTemplate: {}", templateReqDTO);
        if (templateReqDTO.getId() == null) {
            throw new ServiceException(FINANCE_TEMPLATE_NOT_EXISTS.getCode(), FINANCE_TEMPLATE_NOT_EXISTS.getMsg());
        }
        ChargeDetailTemplateDO templateDO = chargeDetailTemplateMapper.selectById(templateReqDTO.getId());
        if (templateDO == null) {
            throw new ServiceException(FINANCE_TEMPLATE_NOT_EXISTS.getCode(), FINANCE_TEMPLATE_NOT_EXISTS.getMsg());
        }

        Long count = chargeDetailTemplateMapper.countByTemplateNameExcludeId(templateReqDTO.getTemplateName(), templateDO.getId());
        if (count > 0) {
            log.error("template name already exists: {}", templateDO.getTemplateName());
            throw new ServiceException(FINANCE_TEMPLATE_NOT_EXISTS.getCode(), FINANCE_TEMPLATE_NOT_EXISTS.getMsg());
        }
        templateDO.setTemplateName(templateReqDTO.getTemplateName());
        templateDO.setTableName(templateReqDTO.getTableName());
        templateDO.setLogicName(templateReqDTO.getLogicName());
        List<ChargeDetailTemplateColumnReqDTO> columns = templateReqDTO.getColumns();
        if (CollectionUtils.isEmpty(columns)) {
            log.error("columns is empty");
            throw new ServiceException(FINANCE_TEMPLATE_COLUMN_EMPTY.getCode(), FINANCE_TEMPLATE_COLUMN_EMPTY.getMsg());
        }
        for (ChargeDetailTemplateColumnReqDTO column : columns) {
            dynamicJdbcTemplate.checkExists(column, templateDO.getLogicName(), templateDO.getTableName());
        }
        templateDO.setExportColumns(JSONUtil.toJsonStr(columns));
        chargeDetailTemplateMapper.updateById(templateDO);
    }

    @Override
    public void deleteTemplate(String templateId) {
        log.info("deleteTemplate: {}", templateId);
        ChargeDetailTemplateDO templateDO = chargeDetailTemplateMapper.selectById(templateId);
        if (templateDO == null) {
            log.error("template not exists: {}", templateId);
            return;
        }
        chargeDetailTemplateMapper.deleteById(templateDO);
    }

    @Override
    public List<ChargeDetailTemplateRespVO> listTemplate(String templateName, String templateId) {
        log.info("listTemplate: {}, {}", templateName, templateId);
        if (StringUtils.isNotBlank(templateId)) {
            ChargeDetailTemplateDO templateDO = chargeDetailTemplateMapper.selectById(templateId);
            if (templateDO == null) {
                log.error("template not exists: {}", templateId);
                return List.of();
            }
            ChargeDetailTemplateRespVO templateRespVO = ChargeDetailTemplateConvert.INSTANCE.convert(templateDO);
            return List.of(templateRespVO);
        }
        List<ChargeDetailTemplateDO> templateDOList = chargeDetailTemplateMapper.blurTemplateName(templateName);
        if (CollectionUtils.isNotEmpty(templateDOList)) {
            return templateDOList.stream().map(ChargeDetailTemplateConvert.INSTANCE::convert)
                    .peek(template -> {
                        if (StringUtils.isNotBlank(template.getExportColumns())) {
                            template.setExportColumnist(JSONUtil.toList(template.getExportColumns(), ChargeDetailTemplateColumnVO.class));
                            Map<String, String> columnMap = template.getExportColumnist().stream().collect(Collectors.toMap(ChargeDetailTemplateColumnVO::getColumnName, ChargeDetailTemplateColumnVO::getExportName, (column1, column2) -> column2));

                            List<ChargeDetailTemplateColumnVO> queryList = template.getExportColumnist().stream().filter(column -> column.getQueryType() != null && column.getQueryType() > 0).peek(t -> {
                                        if (StringUtils.isNotBlank(t.getQueryAssociationName())) {
                                            t.setExportName(t.getExportName() + "/" + columnMap.get(t.getQueryAssociationName()));
                                        }
                                    }
                            ).toList();
                            if (CollectionUtils.isNotEmpty(queryList)) {
                                Set<String> filterNames = queryList.stream().filter(column -> StringUtils.isNotBlank(column.getQueryAssociationName())).map(ChargeDetailTemplateColumnVO::getQueryAssociationName).collect(Collectors.toSet());
                                if (CollectionUtils.isNotEmpty(filterNames)) {
                                    queryList = queryList.stream().filter(column -> !filterNames.contains(column.getColumnName())).toList();
                                }
                            }
                            template.setQueryColumnList(queryList);
                        }
                    })
                    .toList();
        }
        return List.of();
    }

    @Override
    public PageResult<ChargeDetailTemplateRespVO> pageQuery(ChargeDetailTemplatePageQueryVo pageQueryVo) {
        log.info("pageQuery: {}", pageQueryVo);
        Page<?> page = MyBatisUtils.buildPage(pageQueryVo);
        List<ChargeDetailTemplateDO> templateDOList = chargeDetailTemplateMapper.templatePageQuery(page, pageQueryVo);
        List<ChargeDetailTemplateRespVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(templateDOList)) {
            list = templateDOList.stream().map(ChargeDetailTemplateConvert.INSTANCE::convert)
                    .peek(template -> {
                        if (StringUtils.isNotBlank(template.getExportColumns())) {
                            template.setExportColumnist(JSONUtil.toList(template.getExportColumns(), ChargeDetailTemplateColumnVO.class));
                            Map<String, String> columnMap = template.getExportColumnist().stream().collect(Collectors.toMap(ChargeDetailTemplateColumnVO::getColumnName, ChargeDetailTemplateColumnVO::getExportName, (column1, column2) -> column2));

                            List<ChargeDetailTemplateColumnVO> queryList = template.getExportColumnist().stream()
                                    .filter(column -> column.getQueryType() != null && column.getQueryType() > 0)
                                    .peek(t -> {
                                                if (StringUtils.isNotBlank(t.getQueryAssociationName())) {
                                                    t.setExportName(t.getExportName() + "/" + columnMap.get(t.getQueryAssociationName()));
                                                }
                                            }
                                    ).toList();
                            if (CollectionUtils.isNotEmpty(queryList)) {
                                Set<String> filterNames = queryList.stream().filter(column -> StringUtils.isNotBlank(column.getQueryAssociationName())).map(ChargeDetailTemplateColumnVO::getQueryAssociationName).collect(Collectors.toSet());
                                if (CollectionUtils.isNotEmpty(filterNames)) {
                                    queryList = queryList.stream().filter(column -> !filterNames.contains(column.getColumnName())).toList();
                                }
                            }
                            template.setQueryColumnList(queryList);
                        }
                    })
                    .toList();
        }
        return MyBatisUtils.convert2PageResult(page, list);
    }


}
