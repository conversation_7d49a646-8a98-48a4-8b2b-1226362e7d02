package com.linkcircle.boss.module.billing.web.stats.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.linkcircle.boss.module.billing.api.stats.customer.model.entity.CustomerDailyUsageBillDO;
import com.linkcircle.boss.module.billing.web.data.service.CustomerAccountDataService;
import com.linkcircle.boss.module.billing.web.data.service.SubscriptionDataService;
import com.linkcircle.boss.module.billing.web.stats.mapper.CustomerDailyUsageBillMapper;
import com.linkcircle.boss.module.billing.web.stats.model.vo.DailyUsageStatsSummaryVO;
import com.linkcircle.boss.module.billing.web.stats.service.CustomerDailyUsageStatsService;
import com.linkcircle.boss.module.billing.web.stats.service.RedisUsageStatsService;
import com.linkcircle.boss.module.crm.api.customer.account.vo.CustomerAccountVO;
import com.linkcircle.boss.module.crm.api.productservice.vo.ServiceCodeInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-08-01 11:06
 * @description 客户每日使用量统计服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerDailyUsageStatsServiceImpl extends AbstractDailyUsageStatsService implements CustomerDailyUsageStatsService {

    private final CustomerDailyUsageBillMapper customerDailyUsageBillMapper;
    private final SubscriptionDataService subscriptionDataService;
    private final CustomerAccountDataService customerAccountDataService;
    private final RedisUsageStatsService redisUsageStatsService;

    @Override
    protected String getServiceType() {
        return "客户";
    }

    @Override
    protected List<ServiceCodeInfo> queryAllServiceCodes() {
        return subscriptionDataService.getAllActiveServiceCodes();
    }

    @Override
    protected DailyUsageStatsSummaryVO queryStats(ServiceCodeInfo serviceCodeInfo) {
        return redisUsageStatsService.getCustomerUsageStats(serviceCodeInfo);
    }

    @Override
    protected boolean checkDataExists(ServiceCodeInfo serviceCodeInfo) {
        LambdaQueryWrapper<CustomerDailyUsageBillDO> queryWrapper = Wrappers.lambdaQuery(CustomerDailyUsageBillDO.class)
                .eq(CustomerDailyUsageBillDO::getBillDate, serviceCodeInfo.getBillDate())
                .eq(CustomerDailyUsageBillDO::getServiceId, serviceCodeInfo.getServiceId())
                .eq(CustomerDailyUsageBillDO::getProductId, serviceCodeInfo.getProductId())
                .eq(CustomerDailyUsageBillDO::getAccountId, serviceCodeInfo.getAccountId());
        return customerDailyUsageBillMapper.exists(queryWrapper);
    }

    @Override
    protected ZoneId getTimeZone(Long accountId) {
        // 根据accountId查询客户账户信息获取时区
        Optional<CustomerAccountVO> accountInfoOptional = customerAccountDataService.getCustomerAccountInfo(accountId);
        if (accountInfoOptional.isPresent()) {
            return ZoneId.of(accountInfoOptional.get().getTimezone());
        }
        log.debug("账户 {} 未配置时区或时区为空，使用系统默认时区", accountId);
        return ZoneId.systemDefault();
    }

    @Override
    protected int saveSingleStatsData(DailyUsageStatsSummaryVO summaryVO) {
        CustomerDailyUsageBillDO data = convertToCustomerDO(summaryVO);
        int insert = customerDailyUsageBillMapper.insert(data);
        log.debug("保存客户使用量统计数据成功: {}", data);
        return insert;
    }

    /**
     * 转换为CustomerDailyUsageBillDO
     */
    private CustomerDailyUsageBillDO convertToCustomerDO(DailyUsageStatsSummaryVO summaryVO) {
        CustomerDailyUsageBillDO data = new CustomerDailyUsageBillDO();
        data.setBillDate(summaryVO.getBillDate());
        data.setTimezone(summaryVO.getTimezone());
        data.setCustomerId(summaryVO.getCustomerId());
        data.setAccountId(summaryVO.getAccountId());
        data.setServiceId(summaryVO.getServiceId());
        data.setProductId(summaryVO.getProductId());
        data.setUsage(summaryVO.getUsage());
        data.setCashAmount(summaryVO.getCashAmount());
        data.setPointAmount(summaryVO.getPointAmount());
        data.setCreateTime(System.currentTimeMillis());
        data.setUsageUnit(summaryVO.getUsageUnit());
        return data;
    }
}
