package com.linkcircle.boss.module.billing.web.stats.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.linkcircle.boss.framework.sharding.algorithms.dto.HitBusinessTimeDTO;
import com.linkcircle.boss.framework.sharding.constants.LogicTableConstant;
import com.linkcircle.boss.module.billing.api.stats.customer.model.entity.CustomerDailyUsageBillDO;
import com.linkcircle.boss.module.billing.web.data.model.dto.ServiceCodeInfo;
import com.linkcircle.boss.module.billing.web.data.service.SubscriptionDataService;
import com.linkcircle.boss.module.billing.web.stats.mapper.CustomerDailyUsageBillMapper;
import com.linkcircle.boss.module.billing.web.stats.service.CustomerDailyUsageStatsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-01 11:06
 * @description 客户每日使用量统计服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerDailyUsageStatsServiceImpl implements CustomerDailyUsageStatsService {

    private final CustomerDailyUsageBillMapper customerDailyUsageBillMapper;
    private final SubscriptionDataService subscriptionDataService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executeHourlyStats(long startTime, long endTime) {
        log.info("开始执行客户每日使用量统计, 时间范围: {} - {}", startTime, endTime);

        try {
            // 1. 查询所有存在的serviceCode
            List<ServiceCodeInfo> serviceCodeInfos = queryAllServiceCodes();
            if (serviceCodeInfos.isEmpty()) {
                log.info("未找到任何serviceCode，跳过统计");
                return;
            }

            log.info("找到{}个serviceCode: {}", serviceCodeInfos.size(),
                    serviceCodeInfos.stream().map(ServiceCodeInfo::getServiceCode).toList());

            // 2. 遍历每个serviceCode进行统计
            int totalProcessed = 0;
            int successCount = 0;
            int failureCount = 0;

            for (ServiceCodeInfo serviceCodeInfo : serviceCodeInfos) {
                try {
                    log.info("开始统计serviceCode: {}, paymentType: {}",
                            serviceCodeInfo.getServiceCode(), serviceCodeInfo.getPaymentType());

                    // 统计该serviceCode的数据
                    int processedCount = processServiceCodeStats(serviceCodeInfo, startTime, endTime);
                    totalProcessed += processedCount;
                    successCount++;

                    log.info("serviceCode: {} 统计完成，处理记录数: {}",
                            serviceCodeInfo.getServiceCode(), processedCount);
                } catch (Exception e) {
                    failureCount++;
                    log.error("serviceCode: {} 统计失败", serviceCodeInfo.getServiceCode(), e);
                }
            }

            log.info("客户每日使用量统计完成，总处理serviceCode: {}, 成功: {}, 失败: {}, 总记录数: {}", 
                    serviceCodes.size(), successCount, failureCount, totalProcessed);

        } catch (Exception e) {
            log.error("客户每日使用量统计执行异常", e);
            throw e;
        }
    }

    /**
     * 查询所有存在的serviceCode
     */
    private List<ServiceCodeInfo> queryAllServiceCodes() {
        log.debug("从数据服务获取所有活跃的服务编码信息");

        try {
            List<ServiceCodeInfo> serviceCodeInfos = subscriptionDataService.getAllActiveServiceCodes();
            log.debug("从数据服务获取到{}个服务编码信息", serviceCodeInfos.size());
            return serviceCodeInfos;
        } catch (Exception e) {
            log.error("从数据服务获取服务编码信息异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 处理单个serviceCode的统计
     */
    private int processServiceCodeStats(ServiceCodeInfo serviceCodeInfo, long startTime, long endTime) {
        String serviceCode = serviceCodeInfo.getServiceCode();
        Integer paymentType = serviceCodeInfo.getPaymentType();

        HitBusinessTimeDTO businessTimeDTO = HitBusinessTimeDTO.build(List.of(startTime, endTime), List.of(serviceCode));

        int savedCount = 0;

        // 根据支付类型决定查询哪个表
        if (paymentType == 0) {
            // 预付费：只查询预付费收入账单数据
            try (HintManager hintManager = HintManager.getInstance()) {
                hintManager.addTableShardingValue(LogicTableConstant.PREPAID_INCOME_BILL_DETAIL, businessTimeDTO);
                List<CustomerDailyUsageBillDO> prepaidStats = customerDailyUsageBillMapper.statsPrepaidIncomeUsage(
                        serviceCode, startTime, endTime);
                savedCount += saveStatsData(prepaidStats);
                log.debug("预付费serviceCode: {} 统计完成，记录数: {}", serviceCode, prepaidStats.size());
            }
        } else if (paymentType == 1) {
            // 后付费：只查询后付费收入账单数据
            try (HintManager hintManager = HintManager.getInstance()) {
                hintManager.addTableShardingValue(LogicTableConstant.POSTPAID_INCOME_BILL_DETAIL, businessTimeDTO);
                List<CustomerDailyUsageBillDO> postpaidStats = customerDailyUsageBillMapper.statsPostpaidIncomeUsage(
                        serviceCode, startTime, endTime);
                savedCount += saveStatsData(postpaidStats);
                log.debug("后付费serviceCode: {} 统计完成，记录数: {}", serviceCode, postpaidStats.size());
            }
        } else {
            log.warn("未知的支付类型: {}, serviceCode: {}", paymentType, serviceCode);
        }

        return savedCount;
    }

    /**
     * 保存统计数据
     */
    private int saveStatsData(List<CustomerDailyUsageBillDO> statsData) {
        if (statsData.isEmpty()) {
            return 0;
        }

        int savedCount = 0;
        for (CustomerDailyUsageBillDO data : statsData) {
            try {
                // 检查是否已存在
                if (checkDataExists(data)) {
                    log.debug("数据已存在，跳过保存: billDate={}, accountId={}, serviceId={}", 
                            data.getBillDate(), data.getAccountId(), data.getServiceId());
                    continue;
                }

                // 设置ID和创建时间
                data.setId(IdUtil.getSnowflakeNextId());
                data.setCreateTime(System.currentTimeMillis());

                // 保存数据
                customerDailyUsageBillMapper.insert(data);
                savedCount++;
                
                log.debug("保存客户使用量统计数据成功: {}", data);
            } catch (Exception e) {
                log.error("保存客户使用量统计数据失败: {}", data, e);
            }
        }

        return savedCount;
    }

    /**
     * 检查数据是否已存在
     * 根据 billDate + accountId + serviceId 判断
     */
    private boolean checkDataExists(CustomerDailyUsageBillDO data) {
        return customerDailyUsageBillMapper.lambdaQuery()
                .eq(CustomerDailyUsageBillDO::getBillDate, data.getBillDate())
                .eq(CustomerDailyUsageBillDO::getAccountId, data.getAccountId())
                .eq(CustomerDailyUsageBillDO::getServiceId, data.getServiceId())
                .exists();
    }
}
