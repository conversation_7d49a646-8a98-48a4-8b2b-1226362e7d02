package com.linkcircle.boss.module.billing.web.stats.service.impl;

import cn.hutool.core.util.IdUtil;
import com.linkcircle.boss.framework.sharding.constants.LogicTableConstant;
import com.linkcircle.boss.module.billing.api.stats.customer.model.entity.CustomerDailyUsageBillDO;
import com.linkcircle.boss.module.billing.web.data.service.SubscriptionDataService;
import com.linkcircle.boss.module.billing.web.stats.mapper.CustomerDailyUsageBillMapper;
import com.linkcircle.boss.module.billing.web.stats.model.vo.DailyUsageStatsSummaryVO;
import com.linkcircle.boss.module.billing.web.stats.service.AbstractDailyUsageStatsService;
import com.linkcircle.boss.module.billing.web.stats.service.CustomerDailyUsageStatsService;
import com.linkcircle.boss.module.crm.api.productservice.vo.ServiceCodeInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-01 11:06
 * @description 客户每日使用量统计服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerDailyUsageStatsServiceImpl extends AbstractDailyUsageStatsService implements CustomerDailyUsageStatsService {

    private final CustomerDailyUsageBillMapper customerDailyUsageBillMapper;
    private final SubscriptionDataService subscriptionDataService;

    @Override
    protected String getServiceType() {
        return "客户";
    }

    @Override
    protected List<ServiceCodeInfo> queryAllServiceCodes() {
        log.debug("从数据服务获取所有活跃的服务编码信息");

        try {
            List<ServiceCodeInfo> serviceCodeInfos = subscriptionDataService.getAllActiveServiceCodes();
            log.debug("从数据服务获取到{}个服务编码信息", serviceCodeInfos.size());
            return serviceCodeInfos;
        } catch (Exception e) {
            log.error("从数据服务获取服务编码信息异常", e);
            return new ArrayList<>();
        }
    }

    @Override
    protected DailyUsageStatsSummaryVO queryPrepaidStats(String serviceCode, Long serviceId, Long productId,
                                                         long startTime, long endTime) {
        return customerDailyUsageBillMapper.sumPrepaidIncomeUsage(serviceCode, serviceId, productId, null, startTime, endTime);
    }

    @Override
    protected DailyUsageStatsSummaryVO queryPostpaidStats(String serviceCode, Long serviceId, Long productId,
                                                          long startTime, long endTime) {
        return customerDailyUsageBillMapper.sumPostpaidIncomeUsage(serviceCode, serviceId, productId, null, startTime, endTime);
    }

    @Override
    protected String getLogicTableConstantPrepaid() {
        return LogicTableConstant.PREPAID_INCOME_BILL_DETAIL;
    }

    @Override
    protected String getLogicTableConstantPostpaid() {
        return LogicTableConstant.POSTPAID_INCOME_BILL_DETAIL;
    }

    @Override
    protected boolean checkDataExists(String serviceCode, Long serviceId, Long productId,
                                      Long accountId, long startTime, long endTime, boolean isPrepaid) {
        // 生成账单日期
        String billDate = generateBillDate(startTime);

        // 根据 billDate + accountId + serviceId 判断是否已存在
        return customerDailyUsageBillMapper.lambdaQuery()
                .eq(CustomerDailyUsageBillDO::getBillDate, billDate)
                .eq(accountId != null, CustomerDailyUsageBillDO::getAccountId, String.valueOf(accountId))
                .eq(serviceId != null, CustomerDailyUsageBillDO::getServiceId, String.valueOf(serviceId))
                .exists();
    }

    @Override
    protected int saveSingleStatsData(DailyUsageStatsSummaryVO summaryVO) {
        if (summaryVO == null) {
            return 0;
        }

        try {
            // 转换为CustomerDailyUsageBillDO
            CustomerDailyUsageBillDO data = convertToCustomerDO(summaryVO);

            // 设置ID和创建时间
            data.setId(IdUtil.getSnowflakeNextId());
            data.setCreateTime(System.currentTimeMillis());

            // 保存数据
            customerDailyUsageBillMapper.insert(data);

            log.debug("保存客户使用量统计数据成功: {}", data);
            return 1;
        } catch (Exception e) {
            log.error("保存客户使用量统计数据失败: {}", summaryVO, e);
            return 0;
        }
    }

    /**
     * 生成账单日期
     */
    private String generateBillDate(long startTime) {
        return java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHH")
                .format(java.time.LocalDateTime.ofInstant(
                        java.time.Instant.ofEpochMilli(startTime),
                        java.time.ZoneId.systemDefault()));
    }

    /**
     * 转换为CustomerDailyUsageBillDO
     */
    private CustomerDailyUsageBillDO convertToCustomerDO(DailyUsageStatsSummaryVO summaryVO) {
        CustomerDailyUsageBillDO data = new CustomerDailyUsageBillDO();
        data.setBillDate(summaryVO.getBillDate());
        data.setAccountId(summaryVO.getAccountId());
        data.setServiceId(summaryVO.getServiceId());
        data.setProductId(summaryVO.getProductId());
        data.setUsage(summaryVO.getUsage());
        data.setCashAmount(summaryVO.getCashAmount());
        data.setPointAmount(summaryVO.getPointAmount());
        return data;
    }
}
