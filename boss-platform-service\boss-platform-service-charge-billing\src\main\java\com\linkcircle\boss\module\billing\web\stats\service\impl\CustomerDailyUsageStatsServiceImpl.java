package com.linkcircle.boss.module.billing.web.stats.service.impl;

import cn.hutool.core.util.IdUtil;
import com.linkcircle.boss.framework.sharding.constants.LogicTableConstant;
import com.linkcircle.boss.module.billing.api.stats.customer.model.entity.CustomerDailyUsageBillDO;
import com.linkcircle.boss.module.billing.web.data.service.SubscriptionDataService;
import com.linkcircle.boss.module.billing.web.stats.mapper.CustomerDailyUsageBillMapper;
import com.linkcircle.boss.module.billing.web.stats.model.vo.DailyUsageStatsSummaryVO;
import com.linkcircle.boss.module.billing.web.stats.service.AbstractDailyUsageStatsService;
import com.linkcircle.boss.module.billing.web.stats.service.CustomerDailyUsageStatsService;
import com.linkcircle.boss.module.crm.api.productservice.vo.ServiceCodeInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-01 11:06
 * @description 客户每日使用量统计服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerDailyUsageStatsServiceImpl extends AbstractDailyUsageStatsService implements CustomerDailyUsageStatsService {

    private final CustomerDailyUsageBillMapper customerDailyUsageBillMapper;
    private final SubscriptionDataService subscriptionDataService;

    @Override
    protected String getServiceType() {
        return "客户";
    }

    @Override
    protected List<ServiceCodeInfo> queryAllServiceCodes() {
        log.debug("从数据服务获取所有活跃的服务编码信息");

        try {
            List<ServiceCodeInfo> serviceCodeInfos = subscriptionDataService.getAllActiveServiceCodes();
            log.debug("从数据服务获取到{}个服务编码信息", serviceCodeInfos.size());
            return serviceCodeInfos;
        } catch (Exception e) {
            log.error("从数据服务获取服务编码信息异常", e);
            return new ArrayList<>();
        }
    }

    @Override
    protected DailyUsageStatsSummaryVO queryPrepaidStats(String serviceCode, Long serviceId, Long productId,
                                                         long startTime, long endTime) {
        return customerDailyUsageBillMapper.sumPrepaidIncomeUsage(serviceCode, serviceId, productId, null, startTime, endTime);
    }

    @Override
    protected DailyUsageStatsSummaryVO queryPostpaidStats(String serviceCode, Long serviceId, Long productId,
                                                          long startTime, long endTime) {
        return customerDailyUsageBillMapper.sumPostpaidIncomeUsage(serviceCode, serviceId, productId, null, startTime, endTime);
    }

    @Override
    protected String getLogicTableConstantPrepaid() {
        return LogicTableConstant.PREPAID_INCOME_BILL_DETAIL;
    }

    @Override
    protected String getLogicTableConstantPostpaid() {
        return LogicTableConstant.POSTPAID_INCOME_BILL_DETAIL;
    }

    @Override
    protected int saveSingleStatsData(DailyUsageStatsSummaryVO summaryVO) {
        if (summaryVO == null) {
            return 0;
        }

        try {
            // 转换为CustomerDailyUsageBillDO
            CustomerDailyUsageBillDO data = convertToCustomerDO(summaryVO);

            // 检查是否已存在
            if (checkDataExists(data)) {
                log.debug("数据已存在，跳过保存: billDate={}, accountId={}, serviceId={}",
                        data.getBillDate(), data.getAccountId(), data.getServiceId());
                return 0;
            }

            // 设置ID和创建时间
            data.setId(IdUtil.getSnowflakeNextId());
            data.setCreateTime(System.currentTimeMillis());

            // 保存数据
            customerDailyUsageBillMapper.insert(data);

            log.debug("保存客户使用量统计数据成功: {}", data);
            return 1;
        } catch (Exception e) {
            log.error("保存客户使用量统计数据失败: {}", summaryVO, e);
            return 0;
        }
    }

    /**
     * 转换为CustomerDailyUsageBillDO
     */
    private CustomerDailyUsageBillDO convertToCustomerDO(DailyUsageStatsSummaryVO summaryVO) {
        CustomerDailyUsageBillDO data = new CustomerDailyUsageBillDO();
        data.setBillDate(summaryVO.getBillDate());
        data.setAccountId(summaryVO.getAccountId());
        data.setServiceId(summaryVO.getServiceId());
        data.setProductId(summaryVO.getProductId());
        data.setUsage(summaryVO.getUsage());
        data.setCashAmount(summaryVO.getCashAmount());
        data.setPointAmount(summaryVO.getPointAmount());
        return data;
    }

    /**
     * 检查数据是否已存在
     */
    private boolean checkDataExists(CustomerDailyUsageBillDO data) {
        // 简化实现，实际应该查询数据库
        return false;
    }
}
