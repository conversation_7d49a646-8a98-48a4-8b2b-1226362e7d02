package com.linkcircle.boss.module.billing.web.stats.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.linkcircle.boss.framework.sharding.algorithms.dto.HitBusinessTimeDTO;
import com.linkcircle.boss.framework.sharding.constants.LogicTableConstant;
import com.linkcircle.boss.module.billing.api.stats.customer.model.entity.CustomerDailyUsageBillDO;
import com.linkcircle.boss.module.billing.web.stats.mapper.CustomerDailyUsageBillMapper;
import com.linkcircle.boss.module.billing.web.stats.service.CustomerDailyUsageStatsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-01 11:06
 * @description 客户每日使用量统计服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerDailyUsageStatsServiceImpl implements CustomerDailyUsageStatsService {

    private final CustomerDailyUsageBillMapper customerDailyUsageBillMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executeHourlyStats(long startTime, long endTime) {
        log.info("开始执行客户每日使用量统计, 时间范围: {} - {}", startTime, endTime);

        try {
            // 1. 查询所有存在的serviceCode
            List<String> serviceCodes = queryAllServiceCodes(startTime, endTime);
            if (serviceCodes.isEmpty()) {
                log.info("未找到任何serviceCode，跳过统计");
                return;
            }

            log.info("找到{}个serviceCode: {}", serviceCodes.size(), serviceCodes);

            // 2. 遍历每个serviceCode进行统计
            int totalProcessed = 0;
            int successCount = 0;
            int failureCount = 0;

            for (String serviceCode : serviceCodes) {
                try {
                    log.info("开始统计serviceCode: {}", serviceCode);
                    
                    // 统计该serviceCode的数据
                    int processedCount = processServiceCodeStats(serviceCode, startTime, endTime);
                    totalProcessed += processedCount;
                    successCount++;
                    
                    log.info("serviceCode: {} 统计完成，处理记录数: {}", serviceCode, processedCount);
                } catch (Exception e) {
                    failureCount++;
                    log.error("serviceCode: {} 统计失败", serviceCode, e);
                }
            }

            log.info("客户每日使用量统计完成，总处理serviceCode: {}, 成功: {}, 失败: {}, 总记录数: {}", 
                    serviceCodes.size(), successCount, failureCount, totalProcessed);

        } catch (Exception e) {
            log.error("客户每日使用量统计执行异常", e);
            throw e;
        }
    }

    /**
     * 查询所有存在的serviceCode
     */
    private List<String> queryAllServiceCodes(long startTime, long endTime) {
        // 这里需要查询预付费和后付费收入账单表中的所有serviceCode
        // 由于涉及分表查询，需要使用HintManager
        HitBusinessTimeDTO businessTimeDTO = HitBusinessTimeDTO.build(List.of(startTime, endTime), List.of());

        List<String> allServiceCodes = new ArrayList<>();

        // 查询预付费收入账单表
        try (HintManager hintManager = HintManager.getInstance()) {
            hintManager.addTableShardingValue(LogicTableConstant.PREPAID_INCOME_BILL_DETAIL, businessTimeDTO);
            List<String> prepaidServiceCodes = customerDailyUsageBillMapper.queryServiceCodesFromPrepaidIncome(startTime, endTime);
            allServiceCodes.addAll(prepaidServiceCodes);
        }

        // 查询后付费收入账单表
        try (HintManager hintManager = HintManager.getInstance()) {
            hintManager.addTableShardingValue(LogicTableConstant.POSTPAID_INCOME_BILL_DETAIL, businessTimeDTO);
            List<String> postpaidServiceCodes = customerDailyUsageBillMapper.queryServiceCodesFromPostpaidIncome(startTime, endTime);
            allServiceCodes.addAll(postpaidServiceCodes);
        }

        // 去重并返回
        return allServiceCodes.stream().distinct().toList();
    }

    /**
     * 处理单个serviceCode的统计
     */
    private int processServiceCodeStats(String serviceCode, long startTime, long endTime) {
        HitBusinessTimeDTO businessTimeDTO = HitBusinessTimeDTO.build(List.of(startTime, endTime), List.of(serviceCode));

        int savedCount = 0;

        // 统计预付费收入账单数据
        try (HintManager hintManager = HintManager.getInstance()) {
            hintManager.addTableShardingValue(LogicTableConstant.PREPAID_INCOME_BILL_DETAIL, businessTimeDTO);
            List<CustomerDailyUsageBillDO> prepaidStats = customerDailyUsageBillMapper.statsPrepaidIncomeUsage(
                    serviceCode, startTime, endTime);
            savedCount += saveStatsData(prepaidStats);
        }

        // 统计后付费收入账单数据
        try (HintManager hintManager = HintManager.getInstance()) {
            hintManager.addTableShardingValue(LogicTableConstant.POSTPAID_INCOME_BILL_DETAIL, businessTimeDTO);
            List<CustomerDailyUsageBillDO> postpaidStats = customerDailyUsageBillMapper.statsPostpaidIncomeUsage(
                    serviceCode, startTime, endTime);
            savedCount += saveStatsData(postpaidStats);
        }

        return savedCount;
    }

    /**
     * 保存统计数据
     */
    private int saveStatsData(List<CustomerDailyUsageBillDO> statsData) {
        if (statsData.isEmpty()) {
            return 0;
        }

        int savedCount = 0;
        for (CustomerDailyUsageBillDO data : statsData) {
            try {
                // 检查是否已存在
                if (checkDataExists(data)) {
                    log.debug("数据已存在，跳过保存: billDate={}, accountId={}, serviceId={}", 
                            data.getBillDate(), data.getAccountId(), data.getServiceId());
                    continue;
                }

                // 设置ID和创建时间
                data.setId(IdUtil.getSnowflakeNextId());
                data.setCreateTime(System.currentTimeMillis());

                // 保存数据
                customerDailyUsageBillMapper.insert(data);
                savedCount++;
                
                log.debug("保存客户使用量统计数据成功: {}", data);
            } catch (Exception e) {
                log.error("保存客户使用量统计数据失败: {}", data, e);
            }
        }

        return savedCount;
    }

    /**
     * 检查数据是否已存在
     * 根据 billDate + accountId + serviceId 判断
     */
    private boolean checkDataExists(CustomerDailyUsageBillDO data) {
        return customerDailyUsageBillMapper.lambdaQuery()
                .eq(CustomerDailyUsageBillDO::getBillDate, data.getBillDate())
                .eq(CustomerDailyUsageBillDO::getAccountId, data.getAccountId())
                .eq(CustomerDailyUsageBillDO::getServiceId, data.getServiceId())
                .exists();
    }
}
