package com.linkcircle.boss.module.charge.fee.web.payment.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.util.cache.ChargeCacheUtils;
import com.linkcircle.boss.framework.sharding.algorithms.dto.HitBusinessTimeDTO;
import com.linkcircle.boss.framework.sharding.constants.LogicTableConstant;
import com.linkcircle.boss.framework.tanant.TenantIgnore;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductIncomeBillDO;
import com.linkcircle.boss.module.billing.api.detail.income.model.entity.PrepaidIncomeBillDetailDO;
import com.linkcircle.boss.module.billing.api.wallet.model.dto.WalletDeductionMqDTO;
import com.linkcircle.boss.module.charge.fee.enums.InvoiceEnum;
import com.linkcircle.boss.module.charge.fee.enums.WalletsDeductStatusEnum;
import com.linkcircle.boss.module.charge.fee.enums.WalletsEventEnum;
import com.linkcircle.boss.module.charge.fee.web.bill.mapper.MakeupBillMapper;
import com.linkcircle.boss.module.charge.fee.web.bill.model.entity.MakeupIncomeBillDO;
import com.linkcircle.boss.module.charge.fee.web.bill.service.MakeupBillService;
import com.linkcircle.boss.module.charge.fee.web.payment.model.dto.ThirdPartyNotifyDTO;
import com.linkcircle.boss.module.charge.fee.web.payment.model.dto.WalletBalanceDoMakeupUnPayDTO;
import com.linkcircle.boss.module.charge.fee.web.payment.model.dto.WalletBalanceDoPostpaidUnPayDTO;
import com.linkcircle.boss.module.charge.fee.web.payment.model.dto.WalletBalanceDoPrepaidUnPayDTO;
import com.linkcircle.boss.module.charge.fee.web.payment.service.*;
import com.linkcircle.boss.module.fee.api.wallets.model.entity.ChargeCustomerAccountWalletsDO;
import com.linkcircle.boss.module.fee.api.wallets.model.entity.ChargeCustomerAccountWalletsTransactionsDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.baomidou.mybatisplus.core.toolkit.Wrappers.lambdaUpdate;

@Service
@Slf4j
public class UnpayBillSettleServiceImpl implements IUnpayBillSettleService {

    /**
     * 后付费账单
     */
    @Autowired
    private IPostpaidProductIncomeBillService postpaidProductIncomeBillService;
    @Autowired
    private IPostpaidProductServiceIncomeBillService postpaidProductServiceIncomeBillService;
    /**
     * 预付费账单详情
     */
    @Autowired
    private IPrepaidIncomeBillDetailService prepaidIncomeBillDetailService;
    /**
     * 钱包信息
     */
    @Autowired
    private IChargeCustomerAccountWalletsService chargeCustomerAccountWalletsService;
    /**
     * 钱包交易明细
     */
    @Autowired
    private IChargeCustomerAccountWalletsTransactionsService chargeCustomerAccountWalletsTransactionsService;

    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private IWalletPaymentService walletPaymentService;
    @Autowired
    private MakeupBillMapper makeupBillMapper;
    @Autowired
    private MakeupBillService makeupBillService;
    @Autowired
    private IThirdPartyNotifyService thirdPartyNotifyService;

    @Override
    @Transactional
    public CommonResult<Map<Long, Boolean>> doUnPayPrepaidBill(WalletBalanceDoPrepaidUnPayDTO walletBalanceDoPrepaidUnPayDTO) {
        Map<Long, Boolean> result = new HashMap<>();
        RLock rLock = null;
        boolean locked = false;

        List<Long> startToEndTimeRange = walletBalanceDoPrepaidUnPayDTO.getStartToEndTimeRange();
        if (startToEndTimeRange.size() != 2) {
            return CommonResult.error(500, "批量账单交易最开始最晚时间错误");
        }

        Long start = startToEndTimeRange.getFirst();
        Long end = startToEndTimeRange.getLast();
        if (start >= end) {
            return CommonResult.error(500, "开始时间必须小于结束时间");
        }

        List<Long> prepaidIncomeBillIds = walletBalanceDoPrepaidUnPayDTO.getPrepaidIncomeBillIds();

        WalletDeductionMqDTO walletDeductionMqDTO = new WalletDeductionMqDTO();

        HitBusinessTimeDTO businessTimeDTO = HitBusinessTimeDTO.build(walletBalanceDoPrepaidUnPayDTO.getStartToEndTimeRange(),
                walletBalanceDoPrepaidUnPayDTO.getServiceCodes());

        try (HintManager hintManager = HintManager.getInstance();) {
            hintManager.addTableShardingValue(LogicTableConstant.PREPAID_INCOME_BILL_DETAIL, businessTimeDTO);

            List<PrepaidIncomeBillDetailDO> prepaidIncomeBillDetailDOS = prepaidIncomeBillDetailService.listByIds(prepaidIncomeBillIds);
            boolean allValid = prepaidIncomeBillDetailDOS.stream()
                    .allMatch(bill -> {
                        Integer status = bill.getWalletDeductStatus();
                        return status != null && (status == 3 || status == 4);
                    });

            if (!allValid) {
                return CommonResult.error(400, "存在账单不是未结清和余额不足的状态");
            }

            // 根据paymentMethod分组现金和积分集合
            Map<Integer, List<PrepaidIncomeBillDetailDO>> groupedBills = prepaidIncomeBillDetailDOS.stream()
                    .collect(Collectors.groupingBy(PrepaidIncomeBillDetailDO::getPaymentMethod));

            // 获取现金账单集合并按cashAmount从小到大排序
            List<PrepaidIncomeBillDetailDO> cashBills = groupedBills.getOrDefault(0, new ArrayList<>());
            cashBills.sort(Comparator.comparing(PrepaidIncomeBillDetailDO::getCashAmount));

            // 获取积分账单集合并按pointAmount从小到大排序
            List<PrepaidIncomeBillDetailDO> pointBills = groupedBills.getOrDefault(1, new ArrayList<>());
            pointBills.sort(Comparator.comparing(PrepaidIncomeBillDetailDO::getPointAmount));

            if (CollectionUtils.isNotEmpty(cashBills)) {
                // 存在现金为结算账单 开始结算
                for (PrepaidIncomeBillDetailDO cashBill : cashBills) {
                    boolean isPay = false;
                    try {
                        walletDeductionMqDTO.setWalletsId(cashBill.getWalletId());
                        // 现金 + 预付费
                        walletDeductionMqDTO.setPaymentMethod(0);
                        walletDeductionMqDTO.setPaymentType(0);
                        walletDeductionMqDTO.setServiceCode(cashBill.getServiceCode());
                        walletDeductionMqDTO.setBusinessTime(cashBill.getBusinessTime());
                        walletDeductionMqDTO.setCustomerId(cashBill.getCustomerId());
                        walletDeductionMqDTO.setAccountId(cashBill.getAccountId());
                        walletDeductionMqDTO.setBillId(cashBill.getBillDetailId() + "");
                        walletDeductionMqDTO.setCallbackUrl(cashBill.getCallbackUrl());
                        Integer walletDeductStatus = cashBill.getWalletDeductStatus();

                        rLock = redissonClient.getLock(ChargeCacheUtils.getConsumerPaymentLockKey(walletDeductionMqDTO.getAccountId()));
                        // 尝试获取锁，等待10秒，锁自动过期30秒
                        locked = rLock.tryLock(10, TimeUnit.SECONDS);

                        if (!locked) {
                            log.info("[手动结清支付流程]账户{}获取锁失败, 钱包: {}", walletDeductionMqDTO.getAccountId(), walletDeductionMqDTO.getWalletsId());
                            continue;
                        }

                        if (walletDeductStatus != null && walletDeductStatus.equals(WalletsDeductStatusEnum.UNSETTLED.getCode())) {
                            // 未结清 开始结算
                            HintManager.clear();
                            isPay = this.dealUnSettledBusiness(walletDeductionMqDTO);
                        }

                        if (walletDeductStatus != null && walletDeductStatus.equals(WalletsDeductStatusEnum.NOTSUFFICIENTFUNDS.getCode())) {
                            // 首次余额不足, 尝试再次支付
                            HintManager.clear();
                            isPay = walletPaymentService.dealPaymentBusiness(walletDeductionMqDTO);
                        }

                    } catch (Exception e) {
                        log.error("[手动结清支付流程]处理账单失败, 账单ID: {}", cashBill.getBillDetailId(), e);
                        result.put(cashBill.getBillDetailId(), isPay);
                    } finally {
                        // 确保锁被释放
                        if (locked && rLock != null) {
                            rLock.unlock();
                        }
                    }

                }
            }

            if (CollectionUtils.isNotEmpty(pointBills)) {
                for (PrepaidIncomeBillDetailDO pointBill : pointBills) {
                    boolean isPay = false;
                    try {
                        walletDeductionMqDTO.setWalletsId(pointBill.getWalletId());
                        // 积分 + 预付费
                        walletDeductionMqDTO.setPaymentMethod(1);
                        walletDeductionMqDTO.setPaymentType(0);
                        walletDeductionMqDTO.setServiceCode(pointBill.getServiceCode());
                        walletDeductionMqDTO.setBusinessTime(pointBill.getBusinessTime());
                        walletDeductionMqDTO.setCustomerId(pointBill.getCustomerId());
                        walletDeductionMqDTO.setAccountId(pointBill.getAccountId());
                        walletDeductionMqDTO.setBillId(pointBill.getBillDetailId() + "");
                        walletDeductionMqDTO.setCallbackUrl(pointBill.getCallbackUrl());
                        Integer walletDeductStatus = pointBill.getWalletDeductStatus();

                        rLock = redissonClient.getLock(ChargeCacheUtils.getConsumerPaymentLockKey(walletDeductionMqDTO.getAccountId()));
                        // 尝试获取锁，等待10秒，锁自动过期30秒
                        locked = rLock.tryLock(10, TimeUnit.SECONDS);

                        if (!locked) {
                            log.info("[手动结清支付流程]账户{}获取锁失败, 钱包: {}", walletDeductionMqDTO.getAccountId(), walletDeductionMqDTO.getWalletsId());
                            continue;
                        }

                        if (walletDeductStatus != null && walletDeductStatus.equals(WalletsDeductStatusEnum.UNSETTLED.getCode())) {
                            // 未结清 开始结算
                            HintManager.clear();
                            isPay = this.dealUnSettledBusiness(walletDeductionMqDTO);
                        }

                        if (walletDeductStatus != null && walletDeductStatus.equals(WalletsDeductStatusEnum.NOTSUFFICIENTFUNDS.getCode())) {
                            // 首次余额不足, 尝试再次支付
                            HintManager.clear();
                            isPay = walletPaymentService.dealPaymentBusiness(walletDeductionMqDTO);
                        }
                    } catch (Exception e) {
                        log.error("[手动结清支付流程]处理账单失败, 账单ID: {}", pointBill.getBillDetailId(), e);
                        result.put(pointBill.getBillDetailId(), isPay);
                    } finally {
                        // 确保锁被释放
                        if (locked && rLock != null) {
                            rLock.unlock();
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(500, "支付失败");
        }
        return CommonResult.success(result);
    }

    @Override
    @Transactional
    public CommonResult<Map<Long, Boolean>> doUnPayPostpaidBill(WalletBalanceDoPostpaidUnPayDTO walletBalanceDoPostpaidUnPayDTO) throws Exception {
        Map<Long, Boolean> result = new HashMap<>();
        RLock rLock = null;
        boolean locked = false;

        WalletDeductionMqDTO walletDeductionMqDTO = new WalletDeductionMqDTO();

        // 查询分表逻辑使用 billingStartTime
        LambdaQueryWrapper<PostpaidProductIncomeBillDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PostpaidProductIncomeBillDO::getProductBillId, walletBalanceDoPostpaidUnPayDTO.getPostpaidIncomeBillIds());
        queryWrapper.ge(PostpaidProductIncomeBillDO::getBillingStartTime, walletBalanceDoPostpaidUnPayDTO.getBillingStartTime());
        List<PostpaidProductIncomeBillDO> postpaidProductServiceIncomeBillDOS = postpaidProductIncomeBillService.list(queryWrapper);

        boolean allValid = postpaidProductServiceIncomeBillDOS.stream()
                .allMatch(bill -> {
                    Integer status = bill.getWalletDeductStatus();
                    return status != null && (status == 3 || status == 4);
                });

        if (!allValid) {
            return CommonResult.error(400, "存在账单不是未结清和余额不足的状态");
        }


        // 根据paymentMethod分组现金和积分集合
        Map<Integer, List<PostpaidProductIncomeBillDO>> groupedBills = postpaidProductServiceIncomeBillDOS.stream()
                .collect(Collectors.groupingBy(PostpaidProductIncomeBillDO::getPaymentMethod));

        // 获取现金账单集合并按getAmountWithoutTax从小到大排序
        List<PostpaidProductIncomeBillDO> cashBills = groupedBills.getOrDefault(0, new ArrayList<>());
        cashBills.sort(Comparator.comparing(PostpaidProductIncomeBillDO::getAmountWithoutTax));

        // 获取积分账单集合并按getAmountWithoutTax从小到大排序
        List<PostpaidProductIncomeBillDO> pointBills = groupedBills.getOrDefault(1, new ArrayList<>());
        pointBills.sort(Comparator.comparing(PostpaidProductIncomeBillDO::getAmountWithoutTax));

        if (CollectionUtils.isNotEmpty(cashBills)) {
            // 存在现金为结算账单 开始结算
            for (PostpaidProductIncomeBillDO cashBill : cashBills) {
                boolean isPay = false;
                try {
                    walletDeductionMqDTO.setWalletsId(cashBill.getWalletId());
                    // 现金 + 预付费
                    walletDeductionMqDTO.setPaymentMethod(0);
                    walletDeductionMqDTO.setPaymentType(1);
                    walletDeductionMqDTO.setServiceCode("");
                    walletDeductionMqDTO.setBusinessTime(cashBill.getBillingStartTime());
                    walletDeductionMqDTO.setCustomerId(cashBill.getCustomerId());
                    walletDeductionMqDTO.setAccountId(cashBill.getAccountId());
                    walletDeductionMqDTO.setBillId(cashBill.getProductBillId() + "");
                    walletDeductionMqDTO.setCallbackUrl(cashBill.getCallbackUrl());
                    Integer walletDeductStatus = cashBill.getWalletDeductStatus();

                    rLock = redissonClient.getLock(ChargeCacheUtils.getConsumerPaymentLockKey(walletDeductionMqDTO.getAccountId()));
                    // 尝试获取锁，等待10秒，锁自动过期30秒
                    locked = rLock.tryLock(10, TimeUnit.SECONDS);

                    if (!locked) {
                        log.info("[手动结清支付流程]账户{}获取锁失败, 钱包: {}", walletDeductionMqDTO.getAccountId(), walletDeductionMqDTO.getWalletsId());
                        continue;
                    }

                    if (walletDeductStatus != null && walletDeductStatus.equals(WalletsDeductStatusEnum.UNSETTLED.getCode())) {
                        // 未结清 开始结算
                        isPay = this.dealUnSettledBusiness(walletDeductionMqDTO);
                    }

                    if (walletDeductStatus != null && walletDeductStatus.equals(WalletsDeductStatusEnum.NOTSUFFICIENTFUNDS.getCode())) {
                        // 首次余额不足, 尝试再次支付
                        isPay = walletPaymentService.dealPaymentBusiness(walletDeductionMqDTO);
                    }
                } catch (Exception e) {
                    log.error("[手动结清支付流程]处理账单失败, 账单ID: {}", cashBill.getProductBillId(), e);
                    result.put(cashBill.getProductBillId(), isPay);
                } finally {
                    // 确保锁被释放
                    if (locked && rLock != null) {
                        rLock.unlock();
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(pointBills)) {
            for (PostpaidProductIncomeBillDO pointBill : pointBills) {
                boolean isPay = false;
                try {
                    walletDeductionMqDTO.setWalletsId(pointBill.getWalletId());
                    // 积分 + 预付费
                    walletDeductionMqDTO.setPaymentMethod(1);
                    walletDeductionMqDTO.setPaymentType(1);
                    walletDeductionMqDTO.setServiceCode("");
                    walletDeductionMqDTO.setBusinessTime(pointBill.getBillingStartTime());
                    walletDeductionMqDTO.setCustomerId(pointBill.getCustomerId());
                    walletDeductionMqDTO.setAccountId(pointBill.getAccountId());
                    walletDeductionMqDTO.setBillId(pointBill.getProductBillId() + "");
                    walletDeductionMqDTO.setCallbackUrl(pointBill.getCallbackUrl());
                    Integer walletDeductStatus = pointBill.getWalletDeductStatus();

                    rLock = redissonClient.getLock(ChargeCacheUtils.getConsumerPaymentLockKey(walletDeductionMqDTO.getAccountId()));
                    // 尝试获取锁，等待10秒，锁自动过期30秒
                    locked = rLock.tryLock(10, TimeUnit.SECONDS);

                    if (!locked) {
                        log.info("[手动结清支付流程]账户{}获取锁失败, 钱包: {}", walletDeductionMqDTO.getAccountId(), walletDeductionMqDTO.getWalletsId());
                        continue;
                    }

                    if (walletDeductStatus != null && walletDeductStatus.equals(WalletsDeductStatusEnum.UNSETTLED.getCode())) {
                        // 未结清 开始结算
                        isPay = this.dealUnSettledBusiness(walletDeductionMqDTO);
                    }

                    if (walletDeductStatus != null && walletDeductStatus.equals(WalletsDeductStatusEnum.NOTSUFFICIENTFUNDS.getCode())) {
                        // 首次余额不足, 尝试再次支付
                        isPay = walletPaymentService.dealPaymentBusiness(walletDeductionMqDTO);
                    }
                } catch (Exception e) {
                    log.error("[手动结清支付流程]处理账单失败, 账单ID: {}", pointBill.getProductBillId(), e);
                    result.put(pointBill.getProductBillId(), isPay);
                } finally {
                    // 确保锁被释放
                    if (locked && rLock != null) {
                        rLock.unlock();
                    }
                }
            }
        }

        return CommonResult.success(result);
    }

    @Override
    @Transactional
    public CommonResult<Map<Long, Boolean>> doUnPayMakeupBill(WalletBalanceDoMakeupUnPayDTO walletBalanceDoMakeupUnPayDTO) {
        Map<Long, Boolean> result = new HashMap<>();
        RLock rLock = null;
        boolean locked = false;

        WalletDeductionMqDTO walletDeductionMqDTO = new WalletDeductionMqDTO();

        // 查询分表逻辑使用 billingStartTime
        LambdaQueryWrapper<MakeupIncomeBillDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MakeupIncomeBillDO::getBillId, walletBalanceDoMakeupUnPayDTO.getMakeupIncomeBillIds());
        queryWrapper.ge(MakeupIncomeBillDO::getBillingStartTime, walletBalanceDoMakeupUnPayDTO.getBillingStartTime());
        // 目前手工账单只有现金支付的方式
        List<MakeupIncomeBillDO> makeupIncomeBillDOS = makeupBillMapper.selectList(queryWrapper);

        boolean allValid = makeupIncomeBillDOS.stream()
                .allMatch(bill -> {
                    Integer status = bill.getWalletDeductStatus();
                    return status != null && (status == 3 || status == 4);
                });

        if (!allValid) {
            return CommonResult.error(400, "存在账单不是未结清和余额不足的状态");
        }

        if (CollectionUtils.isNotEmpty(makeupIncomeBillDOS)) {
            // 存在现金为结算账单 开始结算
            for (MakeupIncomeBillDO cashBill : makeupIncomeBillDOS) {
                boolean isPay = false;
                try {
                    walletDeductionMqDTO.setWalletsId(cashBill.getWalletId());
                    // 现金 + 预付费
                    walletDeductionMqDTO.setPaymentMethod(0);
                    walletDeductionMqDTO.setPaymentType(2);
//                    walletDeductionMqDTO.setServiceCode(cashBill.getServiceCode());
                    walletDeductionMqDTO.setBusinessTime(cashBill.getBillingStartTime());
                    walletDeductionMqDTO.setCustomerId(cashBill.getCustomerId());
                    walletDeductionMqDTO.setAccountId(cashBill.getAccountId());
                    walletDeductionMqDTO.setBillId(cashBill.getBillId() + "");
                    Integer walletDeductStatus = cashBill.getWalletDeductStatus();

                    rLock = redissonClient.getLock(ChargeCacheUtils.getConsumerPaymentLockKey(walletDeductionMqDTO.getAccountId()));
                    // 尝试获取锁，等待10秒，锁自动过期30秒
                    locked = rLock.tryLock(10, TimeUnit.SECONDS);

                    if (!locked) {
                        log.info("[手动结清支付流程]账户{}获取锁失败, 钱包: {}", walletDeductionMqDTO.getAccountId(), walletDeductionMqDTO.getWalletsId());
                        continue;
                    }

                    if (walletDeductStatus != null && walletDeductStatus.equals(WalletsDeductStatusEnum.UNSETTLED.getCode())) {
                        // 未结清 开始结算
                        isPay = this.dealUnSettledBusiness(walletDeductionMqDTO);
                    }

                    if (walletDeductStatus != null && walletDeductStatus.equals(WalletsDeductStatusEnum.NOTSUFFICIENTFUNDS.getCode())) {
                        // 首次余额不足, 尝试再次支付
                        isPay = walletPaymentService.dealPaymentBusiness(walletDeductionMqDTO);
                    }
                } catch (Exception e) {
                    log.error("[手动结清支付流程]处理账单失败, 账单ID: {}", cashBill.getBillId(), e);
                    result.put(cashBill.getBillId(), isPay);
                } finally {
                    // 确保锁被释放
                    if (locked && rLock != null) {
                        rLock.unlock();
                    }
                }
            }
        }

        return CommonResult.success(result);
    }

    @Override
    @Transactional
    @TenantIgnore
    public boolean dealUnSettledBusiness(WalletDeductionMqDTO walletDeductionMqDTO) throws Exception {
        ChargeCustomerAccountWalletsDO walletsDO = chargeCustomerAccountWalletsService.getById(walletDeductionMqDTO.getWalletsId());
        if (ObjectUtil.isEmpty(walletsDO)) {
            log.info("[手动结清支付流程]钱包不存在, id:{}", walletDeductionMqDTO.getWalletsId());
            return false;
        }

        // 支付方式，0：现金，1积分
        Integer paymentMethod = walletDeductionMqDTO.getPaymentMethod();
        // 支付类型，0：预付费，1后付费 2手工账单
        Integer paymentType = walletDeductionMqDTO.getPaymentType();

        if (paymentType == 2 && paymentMethod == 1) {
            log.info("[手动结清支付流程]手工账单不能使用积分支付, id:{}", walletDeductionMqDTO.getBillId());
            return false;
        }

        Long businessTime = walletDeductionMqDTO.getBusinessTime();
        String serviceCode = walletDeductionMqDTO.getServiceCode();
        HitBusinessTimeDTO businessTimeDTO = HitBusinessTimeDTO.build(businessTime, List.of(serviceCode));
        LambdaUpdateWrapper<ChargeCustomerAccountWalletsDO> updateWrapper = lambdaUpdate();

        /*
         * 处理幂等性, 检查推送的订单号是否已经支付
         * 通过 billId 校验账单是否已经处理过, 只有待支付、未结清才处理
         * 返回 cashAmount、pointAmount 是预付或者后付的现金或者积分
         */
        JSONObject billStatusRs = this.getUnSettledBill(paymentType, Long.valueOf(walletDeductionMqDTO.getBillId()), businessTimeDTO);
        Boolean isNull = billStatusRs.getBoolean("isNull");
        if (isNull) {
            log.info("[手动结清支付流程]订单不存在, 不处理; 账单类型: {}, 订单id: {}", paymentType, walletDeductionMqDTO.getBillId());
            return false;
        }

        BigDecimal cashAmount = billStatusRs.getBigDecimal("cashAmount");
        BigDecimal pointAmount = billStatusRs.getBigDecimal("pointAmount");
        if (paymentMethod == 0 && cashAmount == null) {
            log.info("[手动结清支付流程]订单缺少金额信息, 不处理; 账单类型: {}, 订单id: {}", paymentType, walletDeductionMqDTO.getBillId());
            return false;
        }
        if (paymentMethod == 1 && pointAmount == null) {
            log.info("[手动结清支付流程]订单缺少金额信息, 不处理; 账单类型: {}, 订单id: {}", paymentType, walletDeductionMqDTO.getBillId());
            return false;
        }

        billStatusRs.put("paymentMethod", paymentMethod);
        Boolean checkBillStatus = billStatusRs.getBoolean("checkBillStatus");
        if (!checkBillStatus) {
            log.info("[手动结清支付流程]订单已支付或者不存在, 不处理; 账单类型: {}, 订单id: {}", paymentType, walletDeductionMqDTO.getBillId());
            return false;
        }

        int billStatus = InvoiceEnum.BillStatus.WAIT_PAYMENT.getCode();

        switch (paymentMethod) {
            case 0:
                // 钱包余额
                BigDecimal cashBalance = walletsDO.getCashBalance();
                // 这里是尚未支付的金额 获取的是 订单的 unpaidAmount
                BigDecimal amountWithTax = billStatusRs.getBigDecimal("cashAmount");
                // 比较扣费金额(含税总金额) 是否超过钱包余额
                // 如果钱包余额不足 开始配置订单状态为金额不足无法付款 等待后续手动结清账单
                int comparedCash = BigDecimal.ZERO.compareTo(cashBalance);
                if (comparedCash >= 0) {
                    // 余额还是不足的情况不做任何处理
                    log.info("[手动结清支付流程]钱包余额不足, 账单:{}, 钱包:{}, 余额:{}, 待扣:{}", walletDeductionMqDTO.getBillId(), walletsDO.getId(), cashBalance, amountWithTax);
                    return false;
                } else {
                    /*
                     * 开始现金扣费逻辑 更新钱包余额 cashBalance(余额) - amountWithTax(带税金额|积分)
                     * 扣款后如果是负数 表示未结清
                     * 扣款后如果是正数 已支付
                     */
                    BigDecimal subtract = cashBalance.subtract(amountWithTax);

                    ChargeCustomerAccountWalletsTransactionsDO transactionsDO = ChargeCustomerAccountWalletsTransactionsDO.builder()
                            .accountId(walletsDO.getAccountId())
                            .walletsId(walletsDO.getId())
                            .walletsEven(WalletsEventEnum.DEDUCTION.getCode())
                            .customerId(walletDeductionMqDTO.getCustomerId())
//                            .cashAmount(amountWithTax)
//                            .balanceCash(subtract)
                            .pointsAmount(BigDecimal.ZERO) // 现金扣款不涉及积分
                            .balancePoints(walletsDO.getPointsBalance()) // 积分余额不变
                            .transactionTime(walletDeductionMqDTO.getBusinessTime())
                            .billId(Long.valueOf(walletDeductionMqDTO.getBillId()))
                            .billType(paymentType)
                            .tenantId(walletsDO.getTenantId())
                            .build();
                    long recordTime = Calendar.getInstance().getTimeInMillis();
                    transactionsDO.setCreateTime(recordTime);

                    // 未结清方式支付
                    if (subtract.compareTo(BigDecimal.ZERO) < 0) {
                        billStatusRs.put("paidAmount", cashBalance);
                        // 负数转正数 记录未支付金额
                        BigDecimal unPaid = subtract.multiply(BigDecimal.valueOf(-1));
                        billStatusRs.put("unPaidAmount", unPaid);
                        // 余额不足 未结清
                        boolean updatedCashWalletsStatus = this.unSettledAccountBalanceBelowStatus(paymentType, Long.valueOf(walletDeductionMqDTO.getBillId()),
                                InvoiceEnum.BillStatus.UNSETTLED.getCode(), WalletsDeductStatusEnum.UNSETTLED.getCode(), businessTimeDTO, billStatusRs);
                        if (!updatedCashWalletsStatus) {
                            log.error("[手动结清支付流程]现金账单扣款状态修改, 钱包:{}, 账单:{}", walletsDO.getId(), walletDeductionMqDTO.getBillId());
                            throw new ServiceException(500, "[扣款后更新账单状态]更新订单状态失败");
                        }
                        billStatus = InvoiceEnum.BillStatus.UNSETTLED.getCode();
                        subtract = BigDecimal.ZERO;
                        updateWrapper.eq(ChargeCustomerAccountWalletsDO::getId, walletsDO.getId())
                                .set(ChargeCustomerAccountWalletsDO::getCashBalance, subtract);
                        boolean deductionRS = chargeCustomerAccountWalletsService.update(updateWrapper);
                        if (!deductionRS) {
                            log.error("[手动结清支付流程]现金扣款失败, 账单:{}, 钱包:{}, 余额:{}, 待扣:{}", walletDeductionMqDTO.getBillId(), walletsDO.getId(), cashBalance, amountWithTax);
                            throw new ServiceException(500, "钱包扣款失败");
                        }
                        transactionsDO.setCashAmount(cashBalance);
                        transactionsDO.setBalanceCash(subtract);
                    } else {
                        // 可以结清 已支付金额是带税金额 未支付金额则是 0
                        billStatusRs.put("paidAmount", amountWithTax);
                        billStatusRs.put("unPaidAmount", BigDecimal.ZERO);
                        // 结清更新
                        boolean updatedCashWalletsStatus = this.unSettledAccountBalanceBelowStatus(paymentType, Long.valueOf(walletDeductionMqDTO.getBillId()),
                                InvoiceEnum.BillStatus.PAID.getCode(), WalletsDeductStatusEnum.SUCCESS.getCode(), businessTimeDTO, billStatusRs);
                        if (!updatedCashWalletsStatus) {
                            log.error("[手动结清支付流程]现金账单扣款状态修改, 钱包:{}, 账单:{}", walletsDO.getId(), walletDeductionMqDTO.getBillId());
                            throw new ServiceException(500, "[扣款后更新账单状态]更新订单状态失败");
                        }
                        billStatus = InvoiceEnum.BillStatus.PAID.getCode();
                        updateWrapper.eq(ChargeCustomerAccountWalletsDO::getId, walletsDO.getId())
                                .set(ChargeCustomerAccountWalletsDO::getCashBalance, subtract);
                        boolean deductionRS = chargeCustomerAccountWalletsService.update(updateWrapper);
                        if (!deductionRS) {
                            log.error("[手动结清支付流程]现金扣款失败, 账单:{}, 钱包:{}, 余额:{}, 待扣:{}", walletDeductionMqDTO.getBillId(), walletsDO.getId(), cashBalance, amountWithTax);
                            throw new ServiceException(500, "钱包扣款失败");
                        }
                        transactionsDO.setCashAmount(amountWithTax);
                        transactionsDO.setBalanceCash(subtract);
                    }

                    boolean savedWalletOperation = chargeCustomerAccountWalletsTransactionsService.recordHistory(transactionsDO);
                    if (!savedWalletOperation) {
                        log.error("[手动结清支付流程]现金钱包交易记录保存失败, 钱包:{}, 账单:{}", walletsDO.getId(), walletDeductionMqDTO.getBillId());
                        throw new ServiceException(500, "[手动结清支付流程]钱包交易记录保存失败");
                    }

                    ThirdPartyNotifyDTO thirdPartyNotifyDTO = ThirdPartyNotifyDTO.builder()
                            .transactionId(transactionsDO.getId())
                            .transactionTime(transactionsDO.getTransactionTime())
                            .billId(transactionsDO.getBillId())
                            .billType(paymentType)
                            .billStatus(billStatus)
                            .pointBalance(transactionsDO.getBalancePoints())
                            .cashBalance(transactionsDO.getBalanceCash())
                            .build();
                    thirdPartyNotifyService.notifyThirdParty(walletDeductionMqDTO.getCallbackUrl(), businessTimeDTO, thirdPartyNotifyDTO);

                }


                break;
            case 1:
                // 上一次待扣积分
                BigDecimal discountedPrice = billStatusRs.getBigDecimal("pointAmount");
                // 钱包积分余额
                BigDecimal pointsBalance = walletsDO.getPointsBalance();
                // 比较积分余额 是否不足; 如果积分不足 配置账单状态余额不足
                int comparedPoints = BigDecimal.ZERO.compareTo(pointsBalance);
                if (comparedPoints >= 0) {
                    // 余额还是不足的情况不做任何处理
                    log.info("[手动结清支付流程]积分余额不足, 账单:{}, 钱包:{}, 积分余额:{}, 待扣:{}", walletDeductionMqDTO.getBillId(), walletsDO.getId(), pointsBalance, discountedPrice);
                    return false;
                } else {
                    /*
                     * 开始积分扣费逻辑
                     */
                    BigDecimal pointDiff = pointsBalance.subtract(discountedPrice);

                    ChargeCustomerAccountWalletsTransactionsDO transactionsPointDO = ChargeCustomerAccountWalletsTransactionsDO.builder()
                            .accountId(walletsDO.getAccountId())
                            .walletsId(walletsDO.getId())
                            .walletsEven(WalletsEventEnum.DEDUCTION.getCode())
                            .customerId(walletDeductionMqDTO.getCustomerId())
//                            .pointsAmount(discountedPrice)
//                            .balancePoints(pointDiff)
                            .cashAmount(BigDecimal.ZERO) // 积分扣款不涉及现金
                            .balanceCash(walletsDO.getCashBalance()) // 现金余额不变
                            .transactionTime(walletDeductionMqDTO.getBusinessTime())
                            .billType(paymentType)
                            .billId(Long.valueOf(walletDeductionMqDTO.getBillId()))
                            .tenantId(walletsDO.getTenantId())
                            .build();
                    long recordPayTime = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    transactionsPointDO.setCreateTime(recordPayTime);

                    // 未结清方式支付
                    if (pointDiff.compareTo(BigDecimal.ZERO) < 0) {
                        billStatusRs.put("paidAmount", pointsBalance);
                        // 负数转正数 记录未支付金额
                        BigDecimal unPaid = pointDiff.multiply(BigDecimal.valueOf(-1));
                        billStatusRs.put("unPaidAmount", unPaid);

                        // 扣费后更新账单未结清状态 只有部分账单可以支付完成
                        boolean updatedPointWalletsStatus = this.unSettledAccountBalanceBelowStatus(paymentType, Long.valueOf(walletDeductionMqDTO.getBillId()),
                                InvoiceEnum.BillStatus.UNSETTLED.getCode(), WalletsDeductStatusEnum.UNSETTLED.getCode(), businessTimeDTO, billStatusRs);
                        if (!updatedPointWalletsStatus) {
                            log.error("[手动结清支付流程]积分账单扣款状态修改, 钱包:{}, 账单:{}", walletsDO.getId(), walletDeductionMqDTO.getBillId());
                            throw new ServiceException(500, "[扣款后更新积分账单状态]更新订单状态失败");
                        }
                        billStatus = InvoiceEnum.BillStatus.UNSETTLED.getCode();
                        pointDiff = BigDecimal.ZERO;
                        updateWrapper.eq(ChargeCustomerAccountWalletsDO::getId, walletsDO.getId())
                                .set(ChargeCustomerAccountWalletsDO::getPointsBalance, pointDiff);
                        boolean deductionPointRS = chargeCustomerAccountWalletsService.update(updateWrapper);
                        if (!deductionPointRS) {
                            log.error("[手动结清支付流程]积分扣款失败, 账单:{}, 钱包:{}, 余额:{}, 待扣:{}", walletDeductionMqDTO.getBillId(), walletsDO.getId(), pointsBalance, discountedPrice);
                            throw new ServiceException(500, "钱包扣款失败");
                        }
                        transactionsPointDO.setPointsAmount(pointsBalance);
                        transactionsPointDO.setBalancePoints(pointDiff);
                    } else {
                        // 积分余额充足
                        billStatusRs.put("paidAmount", discountedPrice);
                        billStatusRs.put("unPaidAmount", BigDecimal.ZERO);
                        boolean updatedPointWalletsStatus = this.unSettledAccountBalanceBelowStatus(paymentType, Long.valueOf(walletDeductionMqDTO.getBillId()),
                                InvoiceEnum.BillStatus.PAID.getCode(), WalletsDeductStatusEnum.SUCCESS.getCode(), businessTimeDTO, billStatusRs);
                        if (!updatedPointWalletsStatus) {
                            log.error("[手动结清支付流程]积分账单扣款状态修改, 钱包:{}, 账单:{}", walletsDO.getId(), walletDeductionMqDTO.getBillId());
                            throw new ServiceException(500, "[扣款后更新积分账单状态]更新订单状态失败");
                        }
                        billStatus = InvoiceEnum.BillStatus.PAID.getCode();
                        updateWrapper.eq(ChargeCustomerAccountWalletsDO::getId, walletsDO.getId())
                                .set(ChargeCustomerAccountWalletsDO::getPointsBalance, pointDiff);
                        boolean deductionPointRS = chargeCustomerAccountWalletsService.update(updateWrapper);
                        if (!deductionPointRS) {
                            log.error("[手动结清支付流程]积分扣款失败, 账单:{}, 钱包:{}, 余额:{}, 待扣:{}", walletDeductionMqDTO.getBillId(), walletsDO.getId(), pointsBalance, discountedPrice);
                            throw new ServiceException(500, "钱包扣款失败");
                        }
                        transactionsPointDO.setPointsAmount(discountedPrice);
                        transactionsPointDO.setBalancePoints(pointDiff);
                    }

                    boolean savedPointWalletOperation = chargeCustomerAccountWalletsTransactionsService.recordHistory(transactionsPointDO);
                    if (!savedPointWalletOperation) {
                        log.error("[手动结清支付流程]积分钱包交易记录保存失败, 钱包:{}, 账单:{}", walletsDO.getId(), walletDeductionMqDTO.getBillId());
                        throw new ServiceException(500, "积分钱包交易记录保存失败");
                    }

                    ThirdPartyNotifyDTO thirdPartyNotifyForPoint = ThirdPartyNotifyDTO.builder()
                            .transactionId(transactionsPointDO.getId())
                            .transactionTime(transactionsPointDO.getTransactionTime())
                            .billId(transactionsPointDO.getBillId())
                            .billType(paymentType)
                            .billStatus(billStatus)
                            .pointBalance(transactionsPointDO.getBalancePoints())
                            .cashBalance(transactionsPointDO.getBalanceCash())
                            .build();
                    thirdPartyNotifyService.notifyThirdParty(walletDeductionMqDTO.getCallbackUrl(), businessTimeDTO, thirdPartyNotifyForPoint);
                }

                break;
            default:
                log.info("[手动结清支付流程]付款类型错误, 钱包:{}, 账单:{}, paymentMethod:{}", walletsDO.getId(), walletDeductionMqDTO.getBillId(), paymentMethod);
                return false;
        }
        return true;
    }

    @Transactional
    public boolean unSettledAccountBalanceBelowStatus(Integer paymentType, Long billId, Integer billStatus, Integer walletsStatus,
                                                      HitBusinessTimeDTO businessTimeDTO, JSONObject billJSON) throws Exception {
        return switch (paymentType) {
            case 0 ->
                    prepaidIncomeBillDetailService.unSettledBillPaidStatus(billId, billStatus, walletsStatus, businessTimeDTO, billJSON);
            case 1 -> postpaidProductIncomeBillService.unSettledBillStatus(billId, billStatus, walletsStatus, billJSON);
            case 2 -> makeupBillService.unSettledBillStatus(billId, billStatus, walletsStatus, billJSON);
            default -> false;
        };
    }

    private JSONObject getUnSettledBill(Integer paymentType, Long billId, HitBusinessTimeDTO businessTimeDTO) throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("isNull", false);
        switch (paymentType) {
            case 0:
                // 处理预付费账单 查看账单状态是否是待处理 即 1
                PrepaidIncomeBillDetailDO prepaidBill = prepaidIncomeBillDetailService.getPrepaidBillByID(billId, businessTimeDTO);
                if (ObjectUtil.isNotNull(prepaidBill)) {
                    boolean checkBillStatus = (Objects.equals(WalletsDeductStatusEnum.UNSETTLED.getCode(), prepaidBill.getWalletDeductStatus()) ||
                            Objects.equals(WalletsDeductStatusEnum.NOTSUFFICIENTFUNDS.getCode(), prepaidBill.getWalletDeductStatus()));
                    jsonObject.put("checkBillStatus", checkBillStatus);
                    // 获取 未支付金额
                    jsonObject.put("pointAmount", prepaidBill.getUnpaidAmount());
                    jsonObject.put("cashAmount", prepaidBill.getUnpaidAmount());
                    // 获取已经支付的金额
                    jsonObject.put("lastPaidAmount", prepaidBill.getPaidAmount());
                } else {
                    jsonObject.put("isNull", true);
                }
                break;
            case 1:
                // 处理后付费账单 查看账单状态是否是待处理 即 1
                PostpaidProductIncomeBillDO postpaidProductIncomeBillDO = postpaidProductIncomeBillService.getBillByID(billId);
                if (ObjectUtil.isNotNull(postpaidProductIncomeBillDO)) {
                    boolean checkBillStatus = (Objects.equals(WalletsDeductStatusEnum.UNSETTLED.getCode(), postpaidProductIncomeBillDO.getWalletDeductStatus()) ||
                            Objects.equals(WalletsDeductStatusEnum.NOTSUFFICIENTFUNDS.getCode(), postpaidProductIncomeBillDO.getWalletDeductStatus()));
                    jsonObject.put("checkBillStatus", checkBillStatus);
                    // 通过 paymentType 区别积分还是现金, 但值字段一样
                    // 获取 未支付金额
                    jsonObject.put("pointAmount", postpaidProductIncomeBillDO.getUnpaidAmount());
                    jsonObject.put("cashAmount", postpaidProductIncomeBillDO.getUnpaidAmount());
                    // 获取已经支付的金额
                    jsonObject.put("lastPaidAmount", postpaidProductIncomeBillDO.getPaidAmount());
                } else {
                    jsonObject.put("isNull", true);
                }
                break;
            case 2:
                // 手工账单 只有现金的情况
                MakeupIncomeBillDO makeupIncomeBillDO = makeupBillMapper.selectById(billId);
                if (ObjectUtil.isNotNull(makeupIncomeBillDO)) {
                    boolean checkBillStatus = (Objects.equals(WalletsDeductStatusEnum.UNSETTLED.getCode(), makeupIncomeBillDO.getWalletDeductStatus()) ||
                            Objects.equals(WalletsDeductStatusEnum.NOTSUFFICIENTFUNDS.getCode(), makeupIncomeBillDO.getWalletDeductStatus()));
                    jsonObject.put("checkBillStatus", checkBillStatus);
                    // 获取 未支付金额
                    jsonObject.put("cashAmount", makeupIncomeBillDO.getUnpaidAmount());
                    // 获取已经支付的金额
                    jsonObject.put("lastPaidAmount", makeupIncomeBillDO.getPaidAmount());
                } else {
                    jsonObject.put("isNull", true);
                }
                break;
            default:
                jsonObject.put("isNull", true);
                break;
        }
        return jsonObject;
    }

}
