package com.linkcircle.boss.module.charge.fee.web.payment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO;
import com.linkcircle.boss.module.charge.fee.web.payment.mapper.PostpaidProductServiceIncomeBillMapper;
import com.linkcircle.boss.module.charge.fee.web.payment.service.IPostpaidProductServiceIncomeBillService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PostpaidProductServiceIncomeBillServiceImpl extends ServiceImpl<PostpaidProductServiceIncomeBillMapper, PostpaidProductServiceIncomeBillDO> implements IPostpaidProductServiceIncomeBillService {


}
