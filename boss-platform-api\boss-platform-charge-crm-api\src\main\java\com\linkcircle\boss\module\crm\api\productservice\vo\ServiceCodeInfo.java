package com.linkcircle.boss.module.crm.api.productservice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-08-01 11:20
 * @description 服务编码信息VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "服务编码信息VO")
public class ServiceCodeInfo {

    @Schema(description = "产品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2001")
    private Long productId;

    @Schema(description = "服务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long serviceId;

    @Schema(description = "服务编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "voice_call")
    private String serviceCode;

    @Schema(description = "支付类型，0-预付费，1-后付费", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer paymentType;

    @Schema(description = "计费类型，0-固定费率，1-阶梯费率，2-套餐计费，3-按量计费", requiredMode = Schema.RequiredMode.REQUIRED, example = "3")
    private Integer chargeType;
}
