package com.linkcircle.boss.module.billing.web.stats.listener;

import com.linkcircle.boss.framework.redis.core.util.RedissonUtil;
import com.linkcircle.boss.module.billing.web.stats.cache.UsageStatsCacheUtils;
import com.linkcircle.boss.module.billing.web.stats.event.CustomerUsageStatsEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-01-03 15:00
 * @description 客户使用量统计事件监听器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CustomerUsageStatsEventListener {

    private final RedissonUtil redissonUtil;

    /**
     * 监听客户使用量统计事件
     *
     * @param event 客户使用量统计事件
     */
    @Async
    @EventListener
    public void handleCustomerUsageStatsEvent(CustomerUsageStatsEvent event) {
        try {
            log.debug("处理客户使用量统计事件: accountId={}, productId={}, serviceId={}, billDate={}", 
                    event.getAccountId(), event.getProductId(), event.getServiceId(), event.getBillDate());

            // 构建缓存键
            String cacheKey = UsageStatsCacheUtils.buildCustomerUsageStatsKey(
                    event.getAccountId(), event.getProductId(), event.getServiceId(), event.getBillDate());

            // 准备要更新的字段
            Map<String, Object> fieldsToUpdate = new HashMap<>();
            
            if (event.getChargeUsageCount() != null) {
                fieldsToUpdate.put(UsageStatsCacheUtils.FIELD_CHARGE_USAGE_COUNT, event.getChargeUsageCount().toString());
            }
            
            if (event.getUsageCount() != null) {
                fieldsToUpdate.put(UsageStatsCacheUtils.FIELD_USAGE_COUNT, event.getUsageCount().toString());
            }
            
            if (event.getCashAmount() != null) {
                fieldsToUpdate.put(UsageStatsCacheUtils.FIELD_CASH_AMOUNT, event.getCashAmount().toString());
            }
            
            if (event.getPointAmount() != null) {
                fieldsToUpdate.put(UsageStatsCacheUtils.FIELD_POINT_AMOUNT, event.getPointAmount().toString());
            }

            if (!fieldsToUpdate.isEmpty()) {
                // 原子性地增加统计数据
                incrementUsageStats(cacheKey, fieldsToUpdate);
                
                // 设置过期时间
                redissonUtil.expire(cacheKey, Duration.ofSeconds(UsageStatsCacheUtils.CACHE_EXPIRE_SECONDS));
                
                log.debug("客户使用量统计数据已更新到Redis: key={}, fields={}", cacheKey, fieldsToUpdate.keySet());
            }

        } catch (Exception e) {
            log.error("处理客户使用量统计事件失败: {}", event, e);
        }
    }

    /**
     * 原子性地增加使用量统计数据
     *
     * @param cacheKey       缓存键
     * @param fieldsToUpdate 要更新的字段
     */
    private void incrementUsageStats(String cacheKey, Map<String, Object> fieldsToUpdate) {
        for (Map.Entry<String, Object> entry : fieldsToUpdate.entrySet()) {
            String field = entry.getKey();
            String valueStr = entry.getValue().toString();
            
            try {
                BigDecimal incrementValue = new BigDecimal(valueStr);
                
                // 获取当前值
                String currentValueStr = redissonUtil.hget(cacheKey, field);
                BigDecimal currentValue = currentValueStr != null ? new BigDecimal(currentValueStr) : BigDecimal.ZERO;
                
                // 计算新值
                BigDecimal newValue = currentValue.add(incrementValue);
                
                // 更新到Redis
                redissonUtil.hset(cacheKey, field, newValue.toString());
                
            } catch (NumberFormatException e) {
                log.warn("无效的数值格式，跳过字段更新: field={}, value={}", field, valueStr);
            }
        }
    }
}
