package com.linkcircle.boss.framework.datapermission.core.user;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.function.Function;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TableUserPermissionConfig {
    private String columnName;
    private Function<UserDataPermissionVO, Integer> dataScopeOwn;
    private Function<UserDataPermissionVO, List<Long>> idListFunction;
}
