package com.linkcircle.boss.module.crm.api.productservice;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.crm.api.common.CommonApi;
import com.linkcircle.boss.module.crm.api.common.dto.CommonDTO;
import com.linkcircle.boss.module.crm.api.common.vo.CommonVO;
import com.linkcircle.boss.module.crm.api.productservice.vo.ChargeProductServicePriceVo;
import com.linkcircle.boss.module.crm.api.productservice.vo.ProductServiceVO;
import com.linkcircle.boss.module.crm.api.productservice.vo.ServiceCodeInfo;
import com.linkcircle.boss.module.crm.constants.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/25 16:31
 */
@FeignClient(name = ApiConstants.NAME,path = ApiConstants.PREFIX + "/product-service" ,
        fallback = ChargeProductServiceApiFallback.class)
@Tag(name = "RPC 产品-服务 信息")
public interface ChargeProductServiceApi extends CommonApi {


    @PostMapping( "/findNameByIds")
    @Operation(summary = "根据id 获取 名称 信息")
    CommonResult<List<CommonVO>> findNameByIds(@RequestBody CommonDTO commonDTO);


    @GetMapping( "/all")
    @Operation(summary = "获取所有的服务信息")
    CommonResult<List<ProductServiceVO>> allService();


    @GetMapping(value = "/detail")
    @Operation(summary = "根据ID查询服务")
    public CommonResult<ChargeProductServicePriceVo> findById(@RequestParam("serviceId") Long serviceId);

    @GetMapping("/service-codes")
    @Operation(summary = "获取所有活跃的服务编码信息")
    CommonResult<List<ServiceCodeInfo>> getAllActiveServiceCodes();
}
