package com.linkcircle.boss.module.billing.web.stats.service.impl;

import cn.hutool.core.util.IdUtil;
import com.linkcircle.boss.framework.sharding.constants.LogicTableConstant;
import com.linkcircle.boss.module.billing.api.stats.supplier.model.entity.SupplierDailyUsageBillDO;
import com.linkcircle.boss.module.billing.web.data.service.ResourcePurchaseDataService;
import com.linkcircle.boss.module.billing.web.stats.mapper.SupplierDailyUsageBillMapper;
import com.linkcircle.boss.module.billing.web.stats.model.vo.DailyUsageStatsSummaryVO;
import com.linkcircle.boss.module.billing.web.stats.service.AbstractDailyUsageStatsService;
import com.linkcircle.boss.module.billing.web.stats.service.SupplierDailyUsageStatsService;
import com.linkcircle.boss.module.crm.api.productservice.vo.ServiceCodeInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-01-03 10:00
 * @description 供应商每日使用量统计服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierDailyUsageStatsServiceImpl extends AbstractDailyUsageStatsService implements SupplierDailyUsageStatsService {

    private final SupplierDailyUsageBillMapper supplierDailyUsageBillMapper;
    private final ResourcePurchaseDataService resourcePurchaseDataService;

    @Override
    protected String getServiceType() {
        return "供应商";
    }

    @Override
    protected List<ServiceCodeInfo> queryAllServiceCodes() {
        log.debug("从数据服务获取所有活跃的服务编码信息");

        try {
            List<ServiceCodeInfo> serviceCodeInfos = resourcePurchaseDataService.getAllActiveServiceCodes();
            log.debug("从数据服务获取到{}个服务编码信息", serviceCodeInfos.size());
            return serviceCodeInfos;
        } catch (Exception e) {
            log.error("从数据服务获取服务编码信息异常", e);
            return new ArrayList<>();
        }
    }

    @Override
    protected DailyUsageStatsSummaryVO queryPrepaidStats(String serviceCode, Long serviceId, Long productId, 
                                                         long startTime, long endTime) {
        return supplierDailyUsageBillMapper.sumPrepaidCostUsage(serviceCode, serviceId, productId, null, startTime, endTime);
    }

    @Override
    protected DailyUsageStatsSummaryVO queryPostpaidStats(String serviceCode, Long serviceId, Long productId, 
                                                          long startTime, long endTime) {
        return supplierDailyUsageBillMapper.sumPostpaidCostUsage(serviceCode, serviceId, productId, null, startTime, endTime);
    }

    @Override
    protected String getLogicTableConstantPrepaid() {
        return LogicTableConstant.PREPAID_COST_BILL_DETAIL;
    }

    @Override
    protected String getLogicTableConstantPostpaid() {
        return LogicTableConstant.POSTPAID_COST_BILL_DETAIL;
    }

    @Override
    protected boolean checkDataExists(String billDate, Long serviceId, Long productId,
                                      Long accountId, boolean isPrepaid) {
        // 根据 billDate + accountId + resourceServiceId 判断是否已存在
        return supplierDailyUsageBillMapper.lambdaQuery()
                .eq(SupplierDailyUsageBillDO::getBillDate, billDate)
                .eq(accountId != null, SupplierDailyUsageBillDO::getAccountId, String.valueOf(accountId))
                .eq(serviceId != null, SupplierDailyUsageBillDO::getResourceServiceId, String.valueOf(serviceId))
                .exists();
    }

    @Override
    protected ZoneId getTimeZone(Long accountId) {
        // 供应商固定使用UTC时区
        return ZoneId.of("UTC");
    }

    @Override
    protected int saveSingleStatsData(DailyUsageStatsSummaryVO summaryVO) {
        if (summaryVO == null) {
            return 0;
        }

        try {
            // 转换为SupplierDailyUsageBillDO
            SupplierDailyUsageBillDO data = convertToSupplierDO(summaryVO);

            // 设置ID和创建时间
            data.setId(IdUtil.getSnowflakeNextId());
            data.setCreateTime(System.currentTimeMillis());

            // 保存数据
            supplierDailyUsageBillMapper.insert(data);

            log.debug("保存供应商使用量统计数据成功: {}", data);
            return 1;
        } catch (Exception e) {
            log.error("保存供应商使用量统计数据失败: {}", summaryVO, e);
            return 0;
        }
    }

    /**
     * 转换为SupplierDailyUsageBillDO
     */
    private SupplierDailyUsageBillDO convertToSupplierDO(DailyUsageStatsSummaryVO summaryVO) {
        SupplierDailyUsageBillDO data = new SupplierDailyUsageBillDO();
        data.setBillDate(summaryVO.getBillDate());
        data.setAccountId(summaryVO.getAccountId());
        data.setResourceServiceId(summaryVO.getResourceServiceId());
        data.setUsage(summaryVO.getUsage());
        data.setCashAmount(summaryVO.getCashAmount());
        return data;
    }
}
