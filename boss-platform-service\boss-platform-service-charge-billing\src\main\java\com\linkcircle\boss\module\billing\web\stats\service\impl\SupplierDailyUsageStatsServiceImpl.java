package com.linkcircle.boss.module.billing.web.stats.service.impl;

import cn.hutool.core.util.IdUtil;
import com.linkcircle.boss.framework.sharding.algorithms.dto.HitBusinessTimeDTO;
import com.linkcircle.boss.framework.sharding.constants.LogicTableConstant;
import com.linkcircle.boss.module.billing.api.stats.supplier.model.entity.SupplierDailyUsageBillDO;
import com.linkcircle.boss.module.billing.web.data.model.dto.ServiceCodeInfo;
import com.linkcircle.boss.module.billing.web.data.service.ResourcePurchaseDataService;
import com.linkcircle.boss.module.billing.web.stats.mapper.SupplierDailyUsageBillMapper;
import com.linkcircle.boss.module.billing.web.stats.service.SupplierDailyUsageStatsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-01 11:06
 * @description 供应商每日使用量统计服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierDailyUsageStatsServiceImpl implements SupplierDailyUsageStatsService {

    private final SupplierDailyUsageBillMapper supplierDailyUsageBillMapper;
    private final ResourcePurchaseDataService resourcePurchaseDataService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executeHourlyStats(long startTime, long endTime) {
        log.info("开始执行供应商每日使用量统计, 时间范围: {} - {}", startTime, endTime);

        try {
            // 1. 查询所有存在的serviceCode
            List<ServiceCodeInfo> serviceCodeInfos = queryAllServiceCodes();
            if (serviceCodeInfos.isEmpty()) {
                log.info("未找到任何serviceCode，跳过统计");
                return;
            }

            log.info("找到{}个serviceCode: {}", serviceCodeInfos.size(),
                    serviceCodeInfos.stream().map(ServiceCodeInfo::getServiceCode).toList());

            // 2. 遍历每个serviceCode进行统计
            int totalProcessed = 0;
            int successCount = 0;
            int failureCount = 0;

            for (ServiceCodeInfo serviceCodeInfo : serviceCodeInfos) {
                try {
                    log.info("开始统计serviceCode: {}, paymentType: {}",
                            serviceCodeInfo.getServiceCode(), serviceCodeInfo.getPaymentType());

                    // 统计该serviceCode的数据
                    int processedCount = processServiceCodeStats(serviceCodeInfo, startTime, endTime);
                    totalProcessed += processedCount;
                    successCount++;

                    log.info("serviceCode: {} 统计完成，处理记录数: {}",
                            serviceCodeInfo.getServiceCode(), processedCount);
                } catch (Exception e) {
                    failureCount++;
                    log.error("serviceCode: {} 统计失败", serviceCodeInfo.getServiceCode(), e);
                }
            }

            log.info("供应商每日使用量统计完成，总处理serviceCode: {}, 成功: {}, 失败: {}, 总记录数: {}", 
                    serviceCodes.size(), successCount, failureCount, totalProcessed);

        } catch (Exception e) {
            log.error("供应商每日使用量统计执行异常", e);
            throw e;
        }
    }

    /**
     * 查询所有存在的serviceCode
     */
    private List<ServiceCodeInfo> queryAllServiceCodes() {
        log.debug("从数据服务获取所有活跃的供应商服务编码信息");

        try {
            List<ServiceCodeInfo> serviceCodeInfos = resourcePurchaseDataService.getAllActiveServiceCodes();
            log.debug("从数据服务获取到{}个供应商服务编码信息", serviceCodeInfos.size());
            return serviceCodeInfos;
        } catch (Exception e) {
            log.error("从数据服务获取供应商服务编码信息异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 处理单个serviceCode的统计
     */
    private int processServiceCodeStats(String serviceCode, long startTime, long endTime) {
        HitBusinessTimeDTO businessTimeDTO = HitBusinessTimeDTO.build(List.of(startTime, endTime), List.of(serviceCode));

        int savedCount = 0;

        // 统计预付费成本账单数据
        try (HintManager hintManager = HintManager.getInstance()) {
            hintManager.addTableShardingValue(LogicTableConstant.PREPAID_COST_BILL_DETAIL, businessTimeDTO);
            List<SupplierDailyUsageBillDO> prepaidStats = supplierDailyUsageBillMapper.statsPrepaidCostUsage(
                    serviceCode, startTime, endTime);
            savedCount += saveStatsData(prepaidStats);
        }

        // 统计后付费成本账单数据
        try (HintManager hintManager = HintManager.getInstance()) {
            hintManager.addTableShardingValue(LogicTableConstant.POSTPAID_COST_BILL_DETAIL, businessTimeDTO);
            List<SupplierDailyUsageBillDO> postpaidStats = supplierDailyUsageBillMapper.statsPostpaidCostUsage(
                    serviceCode, startTime, endTime);
            savedCount += saveStatsData(postpaidStats);
        }

        return savedCount;
    }

    /**
     * 保存统计数据
     */
    private int saveStatsData(List<SupplierDailyUsageBillDO> statsData) {
        if (statsData.isEmpty()) {
            return 0;
        }

        int savedCount = 0;
        for (SupplierDailyUsageBillDO data : statsData) {
            try {
                // 检查是否已存在
                if (checkDataExists(data)) {
                    log.debug("数据已存在，跳过保存: billDate={}, accountId={}, resourceServiceId={}", 
                            data.getBillDate(), data.getAccountId(), data.getResourceServiceId());
                    continue;
                }

                // 设置ID和创建时间
                data.setId(IdUtil.getSnowflakeNextId());
                data.setCreateTime(System.currentTimeMillis());

                // 保存数据
                supplierDailyUsageBillMapper.insert(data);
                savedCount++;
                
                log.debug("保存供应商使用量统计数据成功: {}", data);
            } catch (Exception e) {
                log.error("保存供应商使用量统计数据失败: {}", data, e);
            }
        }

        return savedCount;
    }

    /**
     * 检查数据是否已存在
     * 根据 billDate + accountId + resourceServiceId 判断
     */
    private boolean checkDataExists(SupplierDailyUsageBillDO data) {
        return supplierDailyUsageBillMapper.lambdaQuery()
                .eq(SupplierDailyUsageBillDO::getBillDate, data.getBillDate())
                .eq(SupplierDailyUsageBillDO::getAccountId, data.getAccountId())
                .eq(SupplierDailyUsageBillDO::getResourceServiceId, data.getResourceServiceId())
                .exists();
    }
}
