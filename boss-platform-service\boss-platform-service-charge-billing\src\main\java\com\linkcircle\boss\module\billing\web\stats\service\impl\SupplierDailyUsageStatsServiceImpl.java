package com.linkcircle.boss.module.billing.web.stats.service.impl;

import cn.hutool.core.util.IdUtil;
import com.linkcircle.boss.framework.sharding.algorithms.dto.HitBusinessTimeDTO;
import com.linkcircle.boss.framework.sharding.constants.LogicTableConstant;
import com.linkcircle.boss.module.billing.api.stats.supplier.model.entity.SupplierDailyUsageBillDO;
import com.linkcircle.boss.module.crm.api.productservice.vo.ServiceCodeInfo;
import com.linkcircle.boss.module.billing.web.data.service.ResourcePurchaseDataService;
import com.linkcircle.boss.module.billing.web.stats.mapper.SupplierDailyUsageBillMapper;
import com.linkcircle.boss.module.billing.web.stats.service.SupplierDailyUsageStatsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-01 11:06
 * @description 供应商每日使用量统计服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierDailyUsageStatsServiceImpl implements SupplierDailyUsageStatsService {

    private final SupplierDailyUsageBillMapper supplierDailyUsageBillMapper;
    private final ResourcePurchaseDataService resourcePurchaseDataService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executeHourlyStats(long startTime, long endTime) {
        log.info("开始执行供应商每日使用量统计, 时间范围: {} - {}", startTime, endTime);

        try {
            // 获取所有服务编码信息
            List<ServiceCodeInfo> serviceCodeInfos = queryAllServiceCodes();
            if (serviceCodeInfos.isEmpty()) {
                log.info("未找到任何serviceCode，跳过统计");
                return;
            }

            log.info("找到{}个服务编码", serviceCodeInfos.size());

            int totalProcessed = 0;
            int successCount = 0;
            int failureCount = 0;

            // 遍历每个服务编码进行统计
            for (ServiceCodeInfo serviceCodeInfo : serviceCodeInfos) {
                try {
                    log.info("开始统计productId: {}, serviceId: {}, serviceCode: {}, paymentType: {}, chargeType: {}",
                            serviceCodeInfo.getProductId(), serviceCodeInfo.getServiceId(), serviceCodeInfo.getServiceCode(),
                            serviceCodeInfo.getPaymentType(), serviceCodeInfo.getChargeType());

                    // 统计该serviceCode的数据
                    int processedCount = processServiceCodeStats(serviceCodeInfo, startTime, endTime);
                    totalProcessed += processedCount;
                    successCount++;

                    log.info("serviceCode: {} 统计完成，处理记录数: {}",
                            serviceCodeInfo.getServiceCode(), processedCount);
                } catch (Exception e) {
                    failureCount++;
                    log.error("serviceCode: {} 统计失败", serviceCodeInfo.getServiceCode(), e);
                }
            }

            log.info("供应商每日使用量统计完成，成功: {}, 失败: {}, 总记录数: {}",
                    successCount, failureCount, totalProcessed);

        } catch (Exception e) {
            log.error("供应商每日使用量统计执行异常", e);
            throw e;
        }
    }

    /**
     * 查询所有存在的serviceCode
     */
    private List<ServiceCodeInfo> queryAllServiceCodes() {
        log.debug("从数据服务获取所有活跃的供应商服务编码信息");

        try {
            List<ServiceCodeInfo> serviceCodeInfos = resourcePurchaseDataService.getAllActiveServiceCodes();
            log.debug("从数据服务获取到{}个供应商服务编码信息", serviceCodeInfos.size());
            return serviceCodeInfos;
        } catch (Exception e) {
            log.error("从数据服务获取供应商服务编码信息异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 处理单个serviceCode的统计
     */
    private int processServiceCodeStats(ServiceCodeInfo serviceCodeInfo, long startTime, long endTime) {
        Long serviceId = serviceCodeInfo.getServiceId();
        Long productId = serviceCodeInfo.getProductId();
        Integer paymentType = serviceCodeInfo.getPaymentType();

        // 根据计费类型决定使用哪种serviceCode
        String serviceCode = determineServiceCode(serviceCodeInfo);
        HitBusinessTimeDTO businessTimeDTO = HitBusinessTimeDTO.build(List.of(startTime, endTime), List.of(serviceCode));

        // 预付费处理
        if (paymentType == 0) {
            return processPrepaidCostStats(serviceCode, serviceId, productId, startTime, endTime, businessTimeDTO);
        }

        // 后付费处理
        if (paymentType == 1) {
            return processPostpaidCostStats(serviceCode, serviceId, productId, startTime, endTime, businessTimeDTO);
        }

        // 未知支付类型
        log.warn("未知的支付类型: {}, serviceCode: {}", paymentType, serviceCode);
        return 0;
    }

    /**
     * 根据计费类型决定使用哪种serviceCode
     */
    private String determineServiceCode(ServiceCodeInfo serviceCodeInfo) {
        Integer chargeType = serviceCodeInfo.getChargeType();

        if (chargeType != null && chargeType == 0) {
            return "fixed";
        }

        if (chargeType != null && chargeType == 1) {
            return "tiered";
        }

        return serviceCodeInfo.getServiceCode();
    }

    /**
     * 处理预付费成本统计
     */
    private int processPrepaidCostStats(String serviceCode, Long serviceId, Long productId,
                                       long startTime, long endTime, HitBusinessTimeDTO businessTimeDTO) {
        try (HintManager hintManager = HintManager.getInstance()) {
            hintManager.addTableShardingValue(LogicTableConstant.PREPAID_COST_BILL_DETAIL, businessTimeDTO);

            SupplierDailyUsageBillDO sumResult = supplierDailyUsageBillMapper.sumPrepaidCostUsage(
                    serviceCode, serviceId, productId, null, startTime, endTime);

            if (sumResult == null || sumResult.getUsage() == null ||
                sumResult.getUsage().compareTo(java.math.BigDecimal.ZERO) <= 0) {
                return 0;
            }

            int savedCount = saveSingleStatsData(sumResult);
            log.debug("预付费serviceCode: {} 统计完成，使用量: {}", serviceCode, sumResult.getUsage());
            return savedCount;
        }
    }

    /**
     * 处理后付费成本统计
     */
    private int processPostpaidCostStats(String serviceCode, Long serviceId, Long productId,
                                        long startTime, long endTime, HitBusinessTimeDTO businessTimeDTO) {
        try (HintManager hintManager = HintManager.getInstance()) {
            hintManager.addTableShardingValue(LogicTableConstant.POSTPAID_COST_BILL_DETAIL, businessTimeDTO);

            SupplierDailyUsageBillDO sumResult = supplierDailyUsageBillMapper.sumPostpaidCostUsage(
                    serviceCode, serviceId, productId, null, startTime, endTime);

            if (sumResult == null || sumResult.getUsage() == null ||
                sumResult.getUsage().compareTo(java.math.BigDecimal.ZERO) <= 0) {
                return 0;
            }

            int savedCount = saveSingleStatsData(sumResult);
            log.debug("后付费serviceCode: {} 统计完成，使用量: {}", serviceCode, sumResult.getUsage());
            return savedCount;
        }
    }

    /**
     * 保存单个统计数据
     */
    private int saveSingleStatsData(SupplierDailyUsageBillDO data) {
        if (data == null) {
            return 0;
        }

        try {
            // 检查是否已存在
            if (checkDataExists(data)) {
                log.debug("数据已存在，跳过保存: billDate={}, accountId={}, resourceServiceId={}",
                        data.getBillDate(), data.getAccountId(), data.getResourceServiceId());
                return 0;
            }

            // 设置ID和创建时间
            data.setId(IdUtil.getSnowflakeNextId());
            data.setCreateTime(System.currentTimeMillis());

            // 保存数据
            supplierDailyUsageBillMapper.insert(data);

            log.debug("保存供应商使用量统计数据成功: {}", data);
            return 1;
        } catch (Exception e) {
            log.error("保存供应商使用量统计数据失败: {}", data, e);
            return 0;
        }
    }

    /**
     * 保存统计数据
     */
    private int saveStatsData(List<SupplierDailyUsageBillDO> statsData) {
        if (statsData.isEmpty()) {
            return 0;
        }

        int savedCount = 0;
        for (SupplierDailyUsageBillDO data : statsData) {
            try {
                // 检查是否已存在
                if (checkDataExists(data)) {
                    log.debug("数据已存在，跳过保存: billDate={}, accountId={}, resourceServiceId={}", 
                            data.getBillDate(), data.getAccountId(), data.getResourceServiceId());
                    continue;
                }

                // 设置ID和创建时间
                data.setId(IdUtil.getSnowflakeNextId());
                data.setCreateTime(System.currentTimeMillis());

                // 保存数据
                supplierDailyUsageBillMapper.insert(data);
                savedCount++;
                
                log.debug("保存供应商使用量统计数据成功: {}", data);
            } catch (Exception e) {
                log.error("保存供应商使用量统计数据失败: {}", data, e);
            }
        }

        return savedCount;
    }

    /**
     * 检查数据是否已存在
     * 根据 billDate + accountId + resourceServiceId 判断
     */
    private boolean checkDataExists(SupplierDailyUsageBillDO data) {
        return supplierDailyUsageBillMapper.lambdaQuery()
                .eq(SupplierDailyUsageBillDO::getBillDate, data.getBillDate())
                .eq(SupplierDailyUsageBillDO::getAccountId, data.getAccountId())
                .eq(SupplierDailyUsageBillDO::getResourceServiceId, data.getResourceServiceId())
                .exists();
    }
}
