package com.linkcircle.boss.module.billing.web.stats.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.linkcircle.boss.module.billing.api.stats.supplier.model.entity.SupplierDailyUsageBillDO;
import com.linkcircle.boss.module.billing.web.data.service.ResourcePurchaseDataService;
import com.linkcircle.boss.module.billing.web.stats.mapper.SupplierDailyUsageBillMapper;
import com.linkcircle.boss.module.billing.web.stats.model.vo.DailyUsageStatsSummaryVO;
import com.linkcircle.boss.module.billing.web.stats.service.RedisUsageStatsService;
import com.linkcircle.boss.module.billing.web.stats.service.SupplierDailyUsageStatsService;
import com.linkcircle.boss.module.crm.api.productservice.vo.ServiceCodeInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.github.cloud.framework.common.constant.TimeConstants;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-01-03 10:00
 * @description 供应商每日使用量统计服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierDailyUsageStatsServiceImpl extends AbstractDailyUsageStatsService implements SupplierDailyUsageStatsService {

    private final SupplierDailyUsageBillMapper supplierDailyUsageBillMapper;
    private final ResourcePurchaseDataService resourcePurchaseDataService;
    private final RedisUsageStatsService redisUsageStatsService;

    @Override
    protected String getServiceType() {
        return "供应商";
    }

    @Override
    protected List<ServiceCodeInfo> queryAllServiceCodes() {
        return resourcePurchaseDataService.getAllActiveServiceCodes();
    }

    @Override
    protected DailyUsageStatsSummaryVO queryStats(ServiceCodeInfo serviceCodeInfo) {
        return redisUsageStatsService.getSupplierUsageStats(serviceCodeInfo);
    }

    @Override
    protected boolean checkDataExists(ServiceCodeInfo serviceCodeInfo) {
        LambdaQueryWrapper<SupplierDailyUsageBillDO> queryWrapper = Wrappers.lambdaQuery(SupplierDailyUsageBillDO.class)
                .eq(SupplierDailyUsageBillDO::getBillDate, serviceCodeInfo.getBillDate())
                .eq(SupplierDailyUsageBillDO::getResourceServiceId, serviceCodeInfo.getServiceId())
                .eq(SupplierDailyUsageBillDO::getAccountId, serviceCodeInfo.getAccountId());
        return supplierDailyUsageBillMapper.exists(queryWrapper);
    }

    @Override
    protected ZoneId getTimeZone(Long accountId) {
        // 供应商固定使用UTC时区
        return ZoneId.of(TimeConstants.TIMEZONE_UTC);
    }

    @Override
    protected int saveSingleStatsData(DailyUsageStatsSummaryVO summaryVO) {
        SupplierDailyUsageBillDO data = convertToSupplierDO(summaryVO);
        int insert = supplierDailyUsageBillMapper.insert(data);

        log.debug("保存供应商使用量统计数据成功: {}", data);
        return insert;
    }

    /**
     * 转换为SupplierDailyUsageBillDO
     */
    private SupplierDailyUsageBillDO convertToSupplierDO(DailyUsageStatsSummaryVO summaryVO) {
        SupplierDailyUsageBillDO data = new SupplierDailyUsageBillDO();
        data.setBillDate(summaryVO.getBillDate());
        data.setAccountId(summaryVO.getAccountId());
        data.setSupplierId(summaryVO.getSupplierId());
        data.setResourceServiceId(summaryVO.getResourceServiceId());
        data.setUsage(summaryVO.getUsage());
        data.setCashAmount(summaryVO.getCashAmount());
        data.setCreateTime(System.currentTimeMillis());
        data.setUsageUnit(summaryVO.getUsageUnit());
        return data;
    }
}
