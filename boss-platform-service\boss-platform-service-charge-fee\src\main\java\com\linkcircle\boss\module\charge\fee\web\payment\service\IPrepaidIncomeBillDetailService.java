package com.linkcircle.boss.module.charge.fee.web.payment.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.linkcircle.boss.framework.sharding.algorithms.dto.HitBusinessTimeDTO;
import com.linkcircle.boss.module.billing.api.detail.income.model.entity.PrepaidIncomeBillDetailDO;

/**
 * <p>
 * 预付费-收入账单-明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
public interface IPrepaidIncomeBillDetailService extends IService<PrepaidIncomeBillDetailDO> {


    /**
     * 扣费成功后开始更新业务账单状态
     * <p>
     * 预付费只有一张表， 根据 billDetailId 更新订单状态
     * prepaid_income_bill_detail.billDetailId 预付费 ID
     *
     * @param billId          主账单 ID
     * @param billStatus      支付类型，0：预付费，1后付费
     * @param businessTimeDTO 分页规则
     * @return
     * @throws Exception
     */
    boolean updateBillPaidStatus(Long billId, Integer billStatus, Integer walletsStatus,
                                 HitBusinessTimeDTO businessTimeDTO, JSONObject billJson) throws Exception;

    boolean unSettledBillPaidStatus(Long billId, Integer billStatus, Integer walletsStatus,
                                    HitBusinessTimeDTO businessTimeDTO, JSONObject billJson) throws Exception;

    boolean updateBillNotSufficientFundsStatus(Long billId, Integer billStatus,
                                               HitBusinessTimeDTO businessTimeDTO, JSONObject billJSON) throws Exception;


    PrepaidIncomeBillDetailDO getPrepaidBillByID(Long billId, HitBusinessTimeDTO businessTimeDTO) throws Exception;

    boolean updateBillCallbackStatus(Long billId, Integer callbackStatus, HitBusinessTimeDTO businessTimeDTO) throws Exception;

}
