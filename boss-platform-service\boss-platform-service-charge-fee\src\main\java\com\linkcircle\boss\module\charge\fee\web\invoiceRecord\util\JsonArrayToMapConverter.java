package com.linkcircle.boss.module.charge.fee.web.invoiceRecord.util;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zyuan
 * @data : 2025-08-01
 */
public class JsonArrayToMapConverter {

    /**
     * 将JSON数组转换为List<Map>结构（支持递归children层级）
     *
     * @param jsonArray 输入的JSON数组（每个元素是Map）
     * @return 转换后的List<Map>结构
     */
    public static List<Map<String, Object>> convertArray(List<Map<String, Object>> jsonArray) {
        return jsonArray.stream()
                .map(JsonArrayToMapConverter::convertItem)
                .collect(Collectors.toList());
    }

    /**
     * 转换单个项目（含递归处理children）
     */
    private static Map<String, Object> convertItem(Map<String, Object> item) {
        Map<String, Object> result = new LinkedHashMap<>();  // 保持字段顺序

        // 复制所有基本字段（排除children）
        item.entrySet().stream()
                .filter(entry -> !"children".equals(entry.getKey()))
                .forEach(entry -> result.put(entry.getKey(), entry.getValue()));

        // 递归处理children
        if (item.containsKey("children")) {
            List<Map<String, Object>> children = (List<Map<String, Object>>) item.get("children");
            result.put("children", convertArray(children));  // 递归调用
        }

        return result;
    }
}
