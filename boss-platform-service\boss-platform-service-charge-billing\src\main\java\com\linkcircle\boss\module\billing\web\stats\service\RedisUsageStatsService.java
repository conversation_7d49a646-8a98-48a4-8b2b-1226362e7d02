package com.linkcircle.boss.module.billing.web.stats.service;

import cn.hutool.core.collection.CollUtil;
import com.linkcircle.boss.framework.common.util.cache.UsageStatsCacheUtils;
import com.linkcircle.boss.module.billing.web.stats.model.vo.DailyUsageStatsSummaryVO;
import com.linkcircle.boss.module.crm.api.productservice.vo.ServiceCodeInfo;
import io.github.kk01001.redis.RedissonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-01-03 15:00
 * @description Redis 使用量统计查询服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RedisUsageStatsService {

    private final RedissonUtil redissonUtil;

    /**
     * 获取客户使用量统计数据
     *
     * @return 统计数据
     */
    public DailyUsageStatsSummaryVO getCustomerUsageStats(ServiceCodeInfo serviceCodeInfo) {
        Long accountId = serviceCodeInfo.getAccountId();
        Long productId = serviceCodeInfo.getProductId();
        Long serviceId = serviceCodeInfo.getServiceId();
        String billDate = serviceCodeInfo.getBillDate();
        String cacheKey = UsageStatsCacheUtils.buildCustomerUsageStatsKey(accountId, productId, serviceId, billDate);

        Map<String, String> statsData = redissonUtil.hgetAll(cacheKey);
        if (CollUtil.isEmpty(statsData)) {
            log.debug("Redis中未找到客户使用量统计数据: key={}", cacheKey);
            return null;
        }

        return buildCustomerSummaryVO(accountId, productId, serviceId, billDate, statsData);
    }

    /**
     * 获取供应商使用量统计数据
     *
     * @return 统计数据
     */
    public DailyUsageStatsSummaryVO getSupplierUsageStats(ServiceCodeInfo serviceCodeInfo) {
        Long accountId = serviceCodeInfo.getAccountId();
        Long serviceId = serviceCodeInfo.getServiceId();
        String billDate = serviceCodeInfo.getBillDate();
        String cacheKey = UsageStatsCacheUtils.buildSupplierUsageStatsKey(accountId, serviceId, billDate);

        Map<String, String> statsData = redissonUtil.hgetAll(cacheKey);
        if (CollUtil.isEmpty(statsData)) {
            log.debug("Redis中未找到供应商使用量统计数据: key={}", cacheKey);
            return null;
        }

        return buildSupplierSummaryVO(accountId, serviceId, billDate, statsData);
    }

    /**
     * 构建客户统计汇总VO
     */
    private DailyUsageStatsSummaryVO buildCustomerSummaryVO(Long accountId, Long productId, Long serviceId,
                                                            String billDate, Map<String, String> statsData) {
        return DailyUsageStatsSummaryVO.builder()
                .billDate(billDate)
                .accountId(accountId)
                .productId(productId)
                .serviceId(serviceId)
                .usage(parseBigDecimal(statsData.get(UsageStatsCacheUtils.FIELD_USAGE_COUNT)))
                .chargeUsageCount(parseBigDecimal(statsData.get(UsageStatsCacheUtils.FIELD_CHARGE_USAGE_COUNT)))
                .cashAmount(parseBigDecimal(statsData.get(UsageStatsCacheUtils.FIELD_CASH_AMOUNT)))
                .pointAmount(parseBigDecimal(statsData.get(UsageStatsCacheUtils.FIELD_POINT_AMOUNT)))
                .usageUnit(statsData.get(UsageStatsCacheUtils.FIELD_USAGE_UNIT))
                .build();
    }

    /**
     * 构建供应商统计汇总VO
     */
    private DailyUsageStatsSummaryVO buildSupplierSummaryVO(Long accountId, Long serviceId,
                                                            String billDate, Map<String, String> statsData) {
        return DailyUsageStatsSummaryVO.builder()
                .billDate(billDate)
                .accountId(accountId)
                .resourceServiceId(serviceId)
                .usage(parseBigDecimal(statsData.get(UsageStatsCacheUtils.FIELD_USAGE_COUNT)))
                .chargeUsageCount(parseBigDecimal(statsData.get(UsageStatsCacheUtils.FIELD_CHARGE_USAGE_COUNT)))
                .usage(parseBigDecimal(statsData.get(UsageStatsCacheUtils.FIELD_USAGE_COUNT)))
                .cashAmount(parseBigDecimal(statsData.get(UsageStatsCacheUtils.FIELD_CASH_AMOUNT)))
                .pointAmount(parseBigDecimal(statsData.get(UsageStatsCacheUtils.FIELD_POINT_AMOUNT)))
                .usageUnit(statsData.get(UsageStatsCacheUtils.FIELD_USAGE_UNIT))
                .build();
    }

    /**
     * 解析BigDecimal
     */
    private BigDecimal parseBigDecimal(String value) {
        if (value == null || value.trim().isEmpty()) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            log.warn("解析BigDecimal失败: value={}", value);
            return BigDecimal.ZERO;
        }
    }
}
