package com.linkcircle.boss.module.billing.web.stats.service;

import com.linkcircle.boss.framework.redis.core.util.RedissonUtil;
import com.linkcircle.boss.module.billing.web.stats.cache.UsageStatsCacheUtils;
import com.linkcircle.boss.module.billing.web.stats.model.vo.DailyUsageStatsSummaryVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025-01-03 15:00
 * @description Redis 使用量统计查询服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RedisUsageStatsService {

    private final RedissonUtil redissonUtil;

    /**
     * 获取客户使用量统计数据
     *
     * @param accountId 账户ID
     * @param productId 产品ID
     * @param serviceId 服务ID
     * @param billDate  账单日期
     * @return 统计数据
     */
    public DailyUsageStatsSummaryVO getCustomerUsageStats(Long accountId, Long productId, Long serviceId, String billDate) {
        try {
            String cacheKey = UsageStatsCacheUtils.buildCustomerUsageStatsKey(accountId, productId, serviceId, billDate);
            
            Map<String, String> statsData = redissonUtil.hgetAll(cacheKey);
            if (statsData == null || statsData.isEmpty()) {
                log.debug("Redis中未找到客户使用量统计数据: key={}", cacheKey);
                return null;
            }

            return buildCustomerSummaryVO(accountId, productId, serviceId, billDate, statsData);
            
        } catch (Exception e) {
            log.error("从Redis获取客户使用量统计数据失败: accountId={}, productId={}, serviceId={}, billDate={}", 
                    accountId, productId, serviceId, billDate, e);
            return null;
        }
    }

    /**
     * 获取供应商使用量统计数据
     *
     * @param accountId 账户ID
     * @param serviceId 服务ID
     * @param billDate  账单日期
     * @return 统计数据
     */
    public DailyUsageStatsSummaryVO getSupplierUsageStats(Long accountId, Long serviceId, String billDate) {
        try {
            String cacheKey = UsageStatsCacheUtils.buildSupplierUsageStatsKey(accountId, serviceId, billDate);
            
            Map<String, String> statsData = redissonUtil.hgetAll(cacheKey);
            if (statsData == null || statsData.isEmpty()) {
                log.debug("Redis中未找到供应商使用量统计数据: key={}", cacheKey);
                return null;
            }

            return buildSupplierSummaryVO(accountId, serviceId, billDate, statsData);
            
        } catch (Exception e) {
            log.error("从Redis获取供应商使用量统计数据失败: accountId={}, serviceId={}, billDate={}", 
                    accountId, serviceId, billDate, e);
            return null;
        }
    }

    /**
     * 批量获取客户使用量统计数据
     *
     * @param accountId 账户ID
     * @param productId 产品ID
     * @param serviceId 服务ID
     * @return 统计数据列表
     */
    public List<DailyUsageStatsSummaryVO> getCustomerUsageStatsBatch(Long accountId, Long productId, Long serviceId) {
        try {
            String pattern = UsageStatsCacheUtils.buildCustomerUsageStatsPattern(accountId, productId, serviceId);
            Set<String> keys = redissonUtil.keys(pattern);
            
            List<DailyUsageStatsSummaryVO> results = new ArrayList<>();
            
            for (String key : keys) {
                Map<String, String> statsData = redissonUtil.hgetAll(key);
                if (statsData != null && !statsData.isEmpty()) {
                    // 从key中提取billDate
                    String billDate = extractBillDateFromKey(key);
                    DailyUsageStatsSummaryVO summaryVO = buildCustomerSummaryVO(accountId, productId, serviceId, billDate, statsData);
                    if (summaryVO != null) {
                        results.add(summaryVO);
                    }
                }
            }
            
            log.debug("批量获取客户使用量统计数据: accountId={}, productId={}, serviceId={}, 结果数量={}", 
                    accountId, productId, serviceId, results.size());
            
            return results;
            
        } catch (Exception e) {
            log.error("批量获取客户使用量统计数据失败: accountId={}, productId={}, serviceId={}", 
                    accountId, productId, serviceId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 批量获取供应商使用量统计数据
     *
     * @param accountId 账户ID
     * @param serviceId 服务ID
     * @return 统计数据列表
     */
    public List<DailyUsageStatsSummaryVO> getSupplierUsageStatsBatch(Long accountId, Long serviceId) {
        try {
            String pattern = UsageStatsCacheUtils.buildSupplierUsageStatsPattern(accountId, serviceId);
            Set<String> keys = redissonUtil.keys(pattern);
            
            List<DailyUsageStatsSummaryVO> results = new ArrayList<>();
            
            for (String key : keys) {
                Map<String, String> statsData = redissonUtil.hgetAll(key);
                if (statsData != null && !statsData.isEmpty()) {
                    // 从key中提取billDate
                    String billDate = extractBillDateFromKey(key);
                    DailyUsageStatsSummaryVO summaryVO = buildSupplierSummaryVO(accountId, serviceId, billDate, statsData);
                    if (summaryVO != null) {
                        results.add(summaryVO);
                    }
                }
            }
            
            log.debug("批量获取供应商使用量统计数据: accountId={}, serviceId={}, 结果数量={}", 
                    accountId, serviceId, results.size());
            
            return results;
            
        } catch (Exception e) {
            log.error("批量获取供应商使用量统计数据失败: accountId={}, serviceId={}", 
                    accountId, serviceId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建客户统计汇总VO
     */
    private DailyUsageStatsSummaryVO buildCustomerSummaryVO(Long accountId, Long productId, Long serviceId, 
                                                           String billDate, Map<String, String> statsData) {
        return DailyUsageStatsSummaryVO.builder()
                .billDate(billDate)
                .accountId(String.valueOf(accountId))
                .productId(String.valueOf(productId))
                .serviceId(String.valueOf(serviceId))
                .usage(parseBigDecimal(statsData.get(UsageStatsCacheUtils.FIELD_USAGE_COUNT)))
                .cashAmount(parseBigDecimal(statsData.get(UsageStatsCacheUtils.FIELD_CASH_AMOUNT)))
                .pointAmount(parseBigDecimal(statsData.get(UsageStatsCacheUtils.FIELD_POINT_AMOUNT)))
                .build();
    }

    /**
     * 构建供应商统计汇总VO
     */
    private DailyUsageStatsSummaryVO buildSupplierSummaryVO(Long accountId, Long serviceId, 
                                                           String billDate, Map<String, String> statsData) {
        return DailyUsageStatsSummaryVO.builder()
                .billDate(billDate)
                .accountId(String.valueOf(accountId))
                .resourceServiceId(String.valueOf(serviceId))
                .usage(parseBigDecimal(statsData.get(UsageStatsCacheUtils.FIELD_USAGE_COUNT)))
                .cashAmount(parseBigDecimal(statsData.get(UsageStatsCacheUtils.FIELD_CASH_AMOUNT)))
                .build();
    }

    /**
     * 从缓存键中提取billDate
     */
    private String extractBillDateFromKey(String key) {
        String[] parts = key.split(":");
        return parts[parts.length - 1];
    }

    /**
     * 解析BigDecimal
     */
    private BigDecimal parseBigDecimal(String value) {
        if (value == null || value.trim().isEmpty()) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            log.warn("解析BigDecimal失败: value={}", value);
            return BigDecimal.ZERO;
        }
    }
}
