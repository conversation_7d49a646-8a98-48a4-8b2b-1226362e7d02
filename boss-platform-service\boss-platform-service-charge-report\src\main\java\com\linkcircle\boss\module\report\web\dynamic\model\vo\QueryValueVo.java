package com.linkcircle.boss.module.report.web.dynamic.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/1 16:32
 */
@Data
@Schema(description = "查询条件的值")
public class QueryValueVo {

    @Schema(description = "展示的值")
    private Object showValue;
    @Schema(description = "实际的值")
    private Object value;

    @Schema(description = "关联的子对象的值")
    private List<QueryValueVo> children;
}
