package com.linkcircle.boss.module.charge.fee.web.payment.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductIncomeBillDO;

public interface IPostpaidProductIncomeBillService extends IService<PostpaidProductIncomeBillDO> {

    /**
     * 扣费成功后开始更新业务账单状态
     * <p>
     * 后付费是有两张表， 根据bill_id 在主表和子表中更新订单状态
     * postpaid_product_service_income_bill_2025.bill_id 后付费 子表关联主表的字段
     *
     * @param billId     主账单 ID
     * @param billStatus 支付状态
     * @return
     * @throws Exception
     */
    boolean updateBillStatus(Long billId, Integer billStatus, Integer walletsStatus, JSONObject billJSON) throws Exception;

    boolean unSettledBillStatus(Long billId, Integer billStatus, Integer walletsStatus, JSONObject billJSON) throws Exception;

    boolean updateBillNotSufficientFundsStatus(Long billId, Integer billStatus, JSONObject billJSON) throws Exception;

    PostpaidProductIncomeBillDO getBillByID(Long billId) throws Exception;

    boolean updateBillCallbackStatus(Long billId, Integer callbackStatus) throws Exception;

}
