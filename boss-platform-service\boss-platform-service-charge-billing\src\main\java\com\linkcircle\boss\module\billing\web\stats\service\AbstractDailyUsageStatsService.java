package com.linkcircle.boss.module.billing.web.stats.service;

import com.linkcircle.boss.framework.mybatis.core.util.HitBusinessTimeDTO;
import com.linkcircle.boss.module.billing.web.stats.model.vo.DailyUsageStatsSummaryVO;
import com.linkcircle.boss.module.crm.api.productservice.vo.ServiceCodeInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-01-03 10:00
 * @description 每日使用量统计服务抽象基类
 */
@Slf4j
public abstract class AbstractDailyUsageStatsService {

    /**
     * 执行小时统计 - 模板方法
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeHourlyStats(long startTime, long endTime) {
        log.info("开始执行{}每日使用量统计, 时间范围: {} - {}", getServiceType(), startTime, endTime);

        try {
            // 获取所有服务编码信息
            List<ServiceCodeInfo> serviceCodeInfos = queryAllServiceCodes();
            if (serviceCodeInfos.isEmpty()) {
                log.info("未找到任何serviceCode，跳过统计");
                return;
            }

            log.info("找到{}个服务编码", serviceCodeInfos.size());

            int totalProcessed = 0;
            int successCount = 0;
            int failureCount = 0;

            // 遍历每个服务编码进行统计
            for (ServiceCodeInfo serviceCodeInfo : serviceCodeInfos) {
                try {
                    log.info("开始统计productId: {}, serviceId: {}, serviceCode: {}, paymentType: {}, chargeType: {}", 
                            serviceCodeInfo.getProductId(), serviceCodeInfo.getServiceId(), serviceCodeInfo.getServiceCode(), 
                            serviceCodeInfo.getPaymentType(), serviceCodeInfo.getChargeType());
                    
                    // 统计该serviceCode的数据
                    int processedCount = processServiceCodeStats(serviceCodeInfo, startTime, endTime);
                    totalProcessed += processedCount;
                    successCount++;
                    
                    log.info("serviceCode: {} 统计完成，处理记录数: {}", 
                            serviceCodeInfo.getServiceCode(), processedCount);
                } catch (Exception e) {
                    failureCount++;
                    log.error("serviceCode: {} 统计失败", serviceCodeInfo.getServiceCode(), e);
                }
            }

            log.info("{}每日使用量统计完成，成功: {}, 失败: {}, 总记录数: {}", 
                    getServiceType(), successCount, failureCount, totalProcessed);

        } catch (Exception e) {
            log.error("{}每日使用量统计执行异常", getServiceType(), e);
            throw e;
        }
    }

    /**
     * 处理单个serviceCode的统计
     */
    private int processServiceCodeStats(ServiceCodeInfo serviceCodeInfo, long startTime, long endTime) {
        Long serviceId = serviceCodeInfo.getServiceId();
        Long productId = serviceCodeInfo.getProductId();
        Integer paymentType = serviceCodeInfo.getPaymentType();

        // 根据计费类型决定使用哪种serviceCode
        String serviceCode = determineServiceCode(serviceCodeInfo);
        HitBusinessTimeDTO businessTimeDTO = HitBusinessTimeDTO.build(List.of(startTime, endTime), List.of(serviceCode));

        // 预付费处理
        if (paymentType == 0) {
            return processPrepaidStats(serviceCode, serviceId, productId, startTime, endTime, businessTimeDTO);
        }

        // 后付费处理
        if (paymentType == 1) {
            return processPostpaidStats(serviceCode, serviceId, productId, startTime, endTime, businessTimeDTO);
        }

        // 未知支付类型
        log.warn("未知的支付类型: {}, serviceCode: {}", paymentType, serviceCode);
        return 0;
    }

    /**
     * 根据计费类型决定使用哪种serviceCode
     */
    protected String determineServiceCode(ServiceCodeInfo serviceCodeInfo) {
        Integer chargeType = serviceCodeInfo.getChargeType();
        
        if (chargeType != null && chargeType == 0) {
            return "fixed";
        }
        
        if (chargeType != null && chargeType == 1) {
            return "tiered";
        }
        
        return serviceCodeInfo.getServiceCode();
    }

    /**
     * 生成账单日期
     *
     * @param startTime 开始时间戳
     * @param accountId 账户ID（用于查询时区，可为null）
     * @return 格式化的账单日期 yyyyMMddHH
     */
    protected String generateBillDate(long startTime, Long accountId) {
        ZoneId timeZone = getTimeZone(accountId);
        return DateTimeFormatter.ofPattern("yyyyMMddHH")
                .format(java.time.LocalDateTime.ofInstant(
                        java.time.Instant.ofEpochMilli(startTime),
                        timeZone));
    }

    /**
     * 处理预付费统计的通用逻辑
     */
    private int processPrepaidStats(String serviceCode, Long serviceId, Long productId,
                                   long startTime, long endTime, HitBusinessTimeDTO businessTimeDTO) {
        try (HintManager hintManager = HintManager.getInstance()) {
            hintManager.addTableShardingValue(getLogicTableConstantPrepaid(), businessTimeDTO);

            // 先检查数据是否已存在，避免不必要的查询
            String billDate = generateBillDate(startTime, null);
            if (checkDataExists(billDate, serviceId, productId, null, true)) {
                log.debug("预付费数据已存在，跳过统计: billDate={}, serviceId={}, productId={}",
                        billDate, serviceId, productId);
                return 0;
            }

            DailyUsageStatsSummaryVO sumResult = queryPrepaidStats(serviceCode, serviceId, productId, startTime, endTime);

            if (sumResult == null || sumResult.getUsage() == null ||
                sumResult.getUsage().compareTo(BigDecimal.ZERO) <= 0) {
                return 0;
            }

            int savedCount = saveSingleStatsData(sumResult);
            log.debug("预付费serviceCode: {} 统计完成，使用量: {}", serviceCode, sumResult.getUsage());
            return savedCount;
        }
    }

    /**
     * 处理后付费统计的通用逻辑
     */
    private int processPostpaidStats(String serviceCode, Long serviceId, Long productId,
                                    long startTime, long endTime, HitBusinessTimeDTO businessTimeDTO) {
        try (HintManager hintManager = HintManager.getInstance()) {
            hintManager.addTableShardingValue(getLogicTableConstantPostpaid(), businessTimeDTO);

            // 先检查数据是否已存在，避免不必要的查询
            String billDate = generateBillDate(startTime, null);
            if (checkDataExists(billDate, serviceId, productId, null, false)) {
                log.debug("后付费数据已存在，跳过统计: billDate={}, serviceId={}, productId={}",
                        billDate, serviceId, productId);
                return 0;
            }

            DailyUsageStatsSummaryVO sumResult = queryPostpaidStats(serviceCode, serviceId, productId, startTime, endTime);

            if (sumResult == null || sumResult.getUsage() == null ||
                sumResult.getUsage().compareTo(BigDecimal.ZERO) <= 0) {
                return 0;
            }

            int savedCount = saveSingleStatsData(sumResult);
            log.debug("后付费serviceCode: {} 统计完成，使用量: {}", serviceCode, sumResult.getUsage());
            return savedCount;
        }
    }

    // ========== 抽象方法，由子类实现 ==========

    /**
     * 获取服务类型名称（用于日志）
     */
    protected abstract String getServiceType();

    /**
     * 查询所有服务编码信息
     */
    protected abstract List<ServiceCodeInfo> queryAllServiceCodes();

    /**
     * 查询预付费统计数据
     */
    protected abstract DailyUsageStatsSummaryVO queryPrepaidStats(String serviceCode, Long serviceId, Long productId, 
                                                                  long startTime, long endTime);

    /**
     * 查询后付费统计数据
     */
    protected abstract DailyUsageStatsSummaryVO queryPostpaidStats(String serviceCode, Long serviceId, Long productId, 
                                                                   long startTime, long endTime);

    /**
     * 保存单个统计数据
     */
    protected abstract int saveSingleStatsData(DailyUsageStatsSummaryVO summaryVO);

    /**
     * 检查数据是否已存在
     *
     * @param billDate  账单日期
     * @param serviceId 服务ID
     * @param productId 产品ID
     * @param accountId 账户ID
     * @param isPrepaid 是否为预付费
     * @return true-已存在，false-不存在
     */
    protected abstract boolean checkDataExists(String billDate, Long serviceId, Long productId,
                                               Long accountId, boolean isPrepaid);

    /**
     * 获取时区
     *
     * @param accountId 账户ID（可为null）
     * @return 时区
     */
    protected abstract ZoneId getTimeZone(Long accountId);

    /**
     * 获取预付费表常量
     */
    protected abstract String getLogicTableConstantPrepaid();

    /**
     * 获取后付费表常量
     */
    protected abstract String getLogicTableConstantPostpaid();
}
