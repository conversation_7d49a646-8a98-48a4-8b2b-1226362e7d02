package com.linkcircle.boss.module.billing.api.detail.income.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.JdbcType;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-16 17:06
 * @description 预付费收入账单明细表
 */
@TableName("prepaid_income_bill_detail")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "预付费收入账单明细表")
public class PrepaidIncomeBillDetailDO {

    /**
     * 收入账单明细id
     */
    @TableId(type = IdType.INPUT)
    @Schema(description = "收入账单明细id")
    private Long billDetailId;


    /**
     * 账单号
     */
//    @TableField("bill_no")
    @TableField(exist = false)
    @Schema(description = "账单号")
    private String billNo;

    /**
     * 开票退款金额
     */
//    @TableField("refund_invoice_amount")
    @TableField(exist = false)
    @Schema(description = "开票退款金额")
    private BigDecimal refundInvoiceAmount;

    /**
     * 业务唯一id
     */
    @TableField("business_id")
    @Schema(description = "业务唯一id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String businessId;

    /**
     * 业务产生的时间戳
     */
    @TableField("business_time")
    @Schema(description = "业务产生的时间戳", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long businessTime;

    /**
     * 服务id
     */
    @TableField("service_id")
    @Schema(description = "服务id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long serviceId;

    /**
     * 服务编码
     */
    @TableField("service_code")
    @Schema(description = "服务编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String serviceCode;

    /**
     * 客户id
     */
    @TableField("customer_id")
    @Schema(description = "客户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long customerId;

    /**
     * 账户id
     */
    @TableField("account_id")
    @Schema(description = "账户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long accountId;

    /**
     * 产品id
     */
    @TableField("product_id")
    @Schema(description = "产品id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long productId;

    /**
     * 主体id
     */
    @TableField("entity_id")
    @Schema(description = "主体id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long entityId;

    /**
     * 合同id
     */
    @TableField("contract_id")
    @Schema(description = "合同id")
    private Long contractId;

    /**
     * 钱包id
     */
    @TableField("wallet_id")
    @Schema(description = "钱包id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long walletId;

    /**
     * 计划id
     */
    @TableField("plan_id")
    @Schema(description = "计划id")
    private Long planId;

    /**
     * 优惠id
     */
    @TableField("discount_id")
    @Schema(description = "优惠id")
    private String discountId;

    /**
     * 订阅id
     */
    @TableField("subscribe_id")
    @Schema(description = "订阅id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long subscribeId;

    /**
     * 消耗量
     */
    @TableField("usage_count")
    @Schema(description = "消耗量", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal usageCount;

    /**
     * 消耗量单位
     */
    @TableField("usage_unit")
    @Schema(description = "消耗量单位", requiredMode = Schema.RequiredMode.REQUIRED)
    private String usageUnit;

    /**
     * 支付方式 0-现金, 1-积分
     */
    @TableField("payment_method")
    @Schema(description = "支付方式 0-现金, 1-积分", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer paymentMethod;

    /**
     * 计费类型  0-固定费率, 1-阶梯费率, 2-套餐计费, 3-按量计费
     */
    @TableField("billing_type")
    @Schema(description = "计费类型  0-固定费率, 1-阶梯费率, 2-套餐计费, 3-按量计费", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer billingType;

    /**
     * 是否在试用期内
     */
    @TableField("in_trial")
    @Schema(description = "是否在试用期内")
    private Boolean inTrial;

    /**
     * 账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清, 4-余额不足
     */
    @TableField("bill_status")
    @Schema(description = "账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清, 4-余额不足", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer billStatus;

    /**
     * 钱包扣款状态（0-未扣款、1-成功、2-失败、3-余额不足, 4-未结清）
     */
    @TableField("wallet_deduct_status")
    @Schema(description = "钱包扣款状态（0-未扣款、1-成功、2-失败、3-余额不足, 4-未结清）")
    private Integer walletDeductStatus;

    /**
     * 积分
     */
    @TableField("point_amount")
    @Schema(description = "积分")
    private BigDecimal pointAmount;

    /**
     * 现金
     */
    @TableField("cash_amount")
    @Schema(description = "现金")
    private BigDecimal cashAmount;

    /**
     * 税率
     */
    @TableField("tax_rate")
    @Schema(description = "税率")
    private BigDecimal taxRate;

    @Schema(description = "计费单位数")
    @TableField("charge_unit_count")
    private BigDecimal chargeUnitCount;

    @Schema(description = "计费消耗量(measure*charge_unit_count)")
    @TableField("charge_usage_count")
    private BigDecimal chargeUsageCount;

    @Schema(description = "计费单位数量")
    @TableField("charge_measure")
    private BigDecimal chargeMeasure;

    @Schema(description = "计费计量单位")
    @TableField("charge_measure_unit")
    private String chargeMeasureUnit;

    @Schema(description = "计量单位是否向上取整 0-不向上取整, 1-向上取整")
    @TableField("charge_measure_ceil")
    private Integer chargeMeasureCeil;

    @Schema(description = "订阅单价")
    @TableField("discounted_unit_price")
    private BigDecimal discountedUnitPrice;

    @Schema(description = "订阅总价(优惠的目录价)")
    @TableField("discounted_price")
    private BigDecimal discountedPrice;

    @Schema(description = "目录单价")
    @TableField("original_unit_price")
    private BigDecimal originalUnitPrice;

    @Schema(description = "目录总价(单价原价)")
    @TableField("original_price")
    private BigDecimal originalPrice;

    /**
     * 优惠的金额
     */
    @TableField("discount_amount")
    @Schema(description = "优惠的金额/积分")
    private BigDecimal discountAmount;

    /**
     * 已开票金额
     */
    @TableField("invoiced_amount")
    @Schema(description = "已开票金额")
    private BigDecimal invoicedAmount;

    /**
     * 可开票金额(=优惠价-已开票金额)
     */
    @TableField("available_invoice_amount")
    @Schema(description = "可开票金额(=优惠价-已开票金额)")
    private BigDecimal availableInvoiceAmount;

    /**
     * 含税总金额
     */
    @TableField("amount_with_tax")
    @Schema(description = "含税总金额")
    private BigDecimal amountWithTax;

    /**
     * 不含税金额
     */
    @TableField("amount_without_tax")
    @Schema(description = "不含税金额")
    private BigDecimal amountWithoutTax;

    /**
     * 货币单位 CNY USD
     */
    @TableField("currency")
    @Schema(description = "货币单位 CNY USD")
    private String currency;

    /**
     * 实际支付时间戳
     */
    @TableField("payment_time")
    @Schema(description = "实际支付时间戳")
    private Long paymentTime;

    /**
     * 出账时间戳
     */
    @TableField("billing_time")
    @Schema(description = "出账时间戳")
    private Long billingTime;

    /**
     * 数据创建时间戳
     */
    @TableField("create_time")
    @Schema(description = "数据创建时间戳")
    private Long createTime;

    /**
     * 是否删除
     */
    @TableField("deleted")
    @Schema(description = "是否删除")
    private Boolean deleted;

    /**
     * 接口请求id
     */
    @TableField("request_id")
    @Schema(description = "接口请求id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String requestId;

    @Schema(description = "出账开始时间戳（毫秒）")
    @TableField("billing_start_time")
    private Long billingStartTime;

    @Schema(description = "出账结束时间戳（毫秒）")
    @TableField("billing_end_time")
    private Long billingEndTime;

    @Schema(description = "间隔时长单位, 0：一次性, 1:日，2：周，3：月，4：季度，5：年")
    @TableField("billing_cycle_type")
    private Integer billingCycleType;

    @Schema(description = "间隔时长")
    @TableField("billing_day")
    private Integer billingDay;

    @Schema(description = "出账周期标识（如202507-202509）")
    @TableField("billing_cycle")
    private String billingCycle;

    /**
     * 费用详情json 见 billing-api com.linkcircle.boss.module.billing.api.rate.model.dto.*
     */
    @TableField(value = "rate_details", jdbcType = JdbcType.OTHER)
    @Schema(description = "费用详情json 见 billing-api com.linkcircle.boss.module.billing.api.rate.model.dto.*")
    private String rateDetails;

    /**
     * 服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>
     */
    @Schema(description = "服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>")
    @TableField(value = "discount_details", jdbcType = JdbcType.OTHER)
    private String discountDetails;

    @Schema(description = "已支付金额")
    @TableField("paid_amount")
    private BigDecimal paidAmount;

    @Schema(description = "未支付金额")
    @TableField("unpaid_amount")
    private BigDecimal unpaidAmount;

    /**
     * 回调地址
     */
    @Schema(description = "回调地址")
    @TableField("callback_url")
    private String callbackUrl;

    /**
     * 回调状态（0-成功）
     */
    @Schema(description = "回调状态（0-成功）")
    @TableField("callback_status")
    private Integer callbackStatus;

}
