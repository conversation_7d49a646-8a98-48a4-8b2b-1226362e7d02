package com.linkcircle.boss.framework.datapermission.core.user;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
public class UserDataPermissionVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1445227854213545961L;

    private Integer dataScope;
    private Integer dataScopeCustomer;
    private Integer dataScopeSupplier;
    private Integer dataScopeContract;
    private Integer dataScopeProject;
    private List<Long> customerIds;
    private List<Long> supplierIds;
    private List<Long> contractIds;
    private List<Long> projectIds;
}
