package com.linkcircle.boss.module.charge.fee.web.payment.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.linkcircle.boss.framework.common.constants.ChargeTopicConstant;
import com.linkcircle.boss.module.charge.fee.web.payment.service.IChargeCustomerAccountWalletsService;
import com.linkcircle.boss.module.charge.fee.web.payment.service.IThirdPartyNotifyService;
import com.linkcircle.boss.module.charge.fee.web.payment.service.IWalletPaymentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 重放支付流程
 */
@Slf4j
@Service
@RocketMQMessageListener(
        topic = "%DLQ%" + ChargeTopicConstant.GROUP_WALLET_PAYMENT_REPLAY,
        consumerGroup = ChargeTopicConstant.GROUP_WALLET_PAYMENT_REPLAY_DLQ
)
public class WalletDeductionReplayDLQConsumer implements RocketMQListener<MessageExt> {

    @Autowired
    private IWalletPaymentService walletPaymentService;
    @Autowired
    private IChargeCustomerAccountWalletsService walletsService;
    @Autowired
    private IThirdPartyNotifyService thirdPartyNotifyService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    @Transactional
    public void onMessage(MessageExt messageExt) {
        String message = new String(messageExt.getBody());
        try {
            // 先记录异常消息到本地
            walletPaymentService.writeToLocalFile(message);

            // 开始处理重播后仍然无法支付的账单记录， 通知业务接口未扣款
            /*WalletDeductionMqDTO walletDeductionMqDTO = objectMapper.readValue(message, WalletDeductionMqDTO.class);

            Long businessTime = walletDeductionMqDTO.getBusinessTime();
            String serviceCode = walletDeductionMqDTO.getServiceCode();

            Integer paymentType = walletDeductionMqDTO.getPaymentType();
            Long walletsId = walletDeductionMqDTO.getWalletsId();

            ThirdPartyNotifyDTO thirdPartyNotifyDTO = ThirdPartyNotifyDTO.builder()
                    .transactionId(1L)
                    .transactionTime(businessTime)
                    .billId(Long.valueOf(walletDeductionMqDTO.getBillId()))
                    .billType(paymentType)
                    .billStatus(InvoiceEnum.BillStatus.WAIT_PAYMENT.getCode())
                    .build();

            ChargeCustomerAccountWalletsDO walletsDO = walletsService.getById(walletsId);
            if (walletsDO != null) {
                thirdPartyNotifyDTO.setCashBalance(walletsDO.getCashBalance());
                thirdPartyNotifyDTO.setPointBalance(walletsDO.getPointsBalance());
            }

            HitBusinessTimeDTO businessTimeDTO = HitBusinessTimeDTO.build(businessTime, List.of(serviceCode));
            thirdPartyNotifyService.notifyThirdParty(walletDeductionMqDTO.getCallbackUrl(), businessTimeDTO, thirdPartyNotifyDTO);*/
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

}
