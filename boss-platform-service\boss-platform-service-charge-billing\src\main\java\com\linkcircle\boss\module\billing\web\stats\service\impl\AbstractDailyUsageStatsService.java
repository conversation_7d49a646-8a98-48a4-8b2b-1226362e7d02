package com.linkcircle.boss.module.billing.web.stats.service.impl;

import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.module.billing.web.stats.model.vo.DailyUsageStatsSummaryVO;
import com.linkcircle.boss.module.crm.api.productservice.vo.ServiceCodeInfo;
import lombok.extern.slf4j.Slf4j;
import org.github.cloud.framework.common.constant.TimeConstants;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-01-03 10:00
 * @description 每日使用量统计服务抽象基类
 */
@Slf4j
public abstract class AbstractDailyUsageStatsService {

    /**
     * 执行小时统计 - 模板方法
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeHourlyStats(long startTime, long endTime) {
        log.info("开始执行{}每日使用量统计, 时间范围: {} - {}", getServiceType(), startTime, endTime);

        try {
            // 获取所有服务编码信息
            List<ServiceCodeInfo> serviceCodeInfos = queryAllServiceCodes();
            if (serviceCodeInfos.isEmpty()) {
                log.info("未找到任何serviceCode，跳过统计");
                return;
            }

            log.info("找到{}个服务编码", serviceCodeInfos.size());

            int totalProcessed = 0;
            int successCount = 0;
            int failureCount = 0;

            // 遍历每个服务编码进行统计
            for (ServiceCodeInfo serviceCodeInfo : serviceCodeInfos) {
                try {
                    log.info("开始统计, serviceInfo: {}", JsonUtils.toJsonString(serviceCodeInfo));

                    // 统计该serviceCode的数据
                    int processedCount = processServiceCodeStats(serviceCodeInfo, startTime, endTime);
                    totalProcessed += processedCount;
                    successCount++;

                    log.info("serviceCode: {} 统计完成，处理记录数: {}",
                            serviceCodeInfo.getServiceCode(), processedCount);
                } catch (Exception e) {
                    failureCount++;
                    log.error("serviceCode: {} 统计失败", serviceCodeInfo.getServiceCode(), e);
                }
            }

            log.info("{}每日使用量统计完成，成功: {}, 失败: {}, 总记录数: {}",
                    getServiceType(), successCount, failureCount, totalProcessed);

        } catch (Exception e) {
            log.error("{}每日使用量统计执行异常", getServiceType(), e);
            throw e;
        }
    }

    /**
     * 处理单个serviceCode的统计
     */
    private int processServiceCodeStats(ServiceCodeInfo serviceCodeInfo, long startTime, long endTime) {
        serviceCodeInfo.setStartTime(startTime);
        serviceCodeInfo.setEndTime(endTime);
        generateBillDate(serviceCodeInfo);

        // 根据计费类型决定使用哪种serviceCode
        determineServiceCode(serviceCodeInfo);

        return processStats(serviceCodeInfo);
    }

    /**
     * 根据计费类型决定使用哪种serviceCode
     */
    protected void determineServiceCode(ServiceCodeInfo serviceCodeInfo) {
        Integer chargeType = serviceCodeInfo.getChargeType();

        if (chargeType != null && chargeType == 0) {
            serviceCodeInfo.setServiceCode("fixed");
        }

        if (chargeType != null && chargeType == 1) {
            serviceCodeInfo.setServiceCode("tiered");
        }

    }

    /**
     * 生成账单日期
     */
    protected void generateBillDate(ServiceCodeInfo serviceCodeInfo) {
        ZoneId timeZone = getTimeZone(serviceCodeInfo.getAccountId());
        String billDate = DateTimeFormatter.ofPattern(TimeConstants.DATE_FORMAT_YYYYMMDDHH)
                .format(LocalDateTime.ofInstant(Instant.ofEpochMilli(serviceCodeInfo.getStartTime()), timeZone));
        serviceCodeInfo.setBillDate(billDate);
        serviceCodeInfo.setTimezone(timeZone.getId());
    }

    /**
     * 处理统计的通用逻辑
     */
    private int processStats(ServiceCodeInfo serviceCodeInfo) {
        // 先检查数据是否已存在，避免不必要的查询
        if (checkDataExists(serviceCodeInfo)) {
            return 0;
        }
        DailyUsageStatsSummaryVO sumResult = queryStats(serviceCodeInfo);
        if (Objects.isNull(sumResult) || Objects.isNull(sumResult.getUsage())) {
            return 0;
        }

        sumResult.setSupplierId(serviceCodeInfo.getSupplierId());
        sumResult.setTimezone(serviceCodeInfo.getTimezone());
        sumResult.setCustomerId(serviceCodeInfo.getCustomerId());
        return saveSingleStatsData(sumResult);
    }

    // ========== 抽象方法，由子类实现 ==========

    /**
     * 获取服务类型名称（用于日志）
     */
    protected abstract String getServiceType();

    /**
     * 查询所有服务编码信息
     */
    protected abstract List<ServiceCodeInfo> queryAllServiceCodes();

    /**
     * 查询统计数据
     */
    protected abstract DailyUsageStatsSummaryVO queryStats(ServiceCodeInfo serviceCodeInfo);

    /**
     * 保存单个统计数据
     */
    protected abstract int saveSingleStatsData(DailyUsageStatsSummaryVO summaryVO);

    /**
     * 检查数据是否已存在
     *
     * @return true-已存在，false-不存在
     */
    protected abstract boolean checkDataExists(ServiceCodeInfo serviceCodeInfo);

    /**
     * 获取时区
     *
     * @param accountId 账户ID（可为null）
     * @return 时区
     */
    protected abstract ZoneId getTimeZone(Long accountId);

}
