package com.linkcircle.boss.module.system.api.user.vo;

import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Set;

@Schema(description = "RPC 服务 - Admin 用户 Response VO")
@Data
public class AdminUserRespVO implements VO {

    @Schema(description = "用户 ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "用户昵称", requiredMode = Schema.RequiredMode.REQUIRED, example = "小王")
    private String nickname;

    @Schema(description = "帐号状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status; // 参见 CommonStatusEnum 枚举

    @Schema(description = "部门编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long deptId;

    @Schema(description = "岗位编号数组", requiredMode = Schema.RequiredMode.REQUIRED, example = "[1, 3]")
    private Set<Long> postIds;

    @Schema(description = "手机号码", requiredMode = Schema.RequiredMode.REQUIRED, example = "15601691300")
    private String mobile;

    @Schema(description = "用户头像", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.xxxx.cn/1.png")
    private String avatar;

    /**
     * 用户类型
     */
    private Integer userType;

    private Long tenantId;

    @Schema(description = "时区", example = "Asia/Shanghai")
    private String timezone;


    /**
     * 用户数据权限范围 UserDataScopeEnum 枚举
     * <p>
     */
    @Schema(description = "用户数据权限范围", example = "1")
    private Integer dataScope;

    @Schema(description = "客户数据范围,0:全选 1:自定义")
    private Integer dataScopeCustomer;

    @Schema(description = "供应商数据范围,0:全选 1:自定义")
    private Integer dataScopeSupplier;

    @Schema(description = "合同数据范围,0:全选 1:自定义")
    private Integer dataScopeContract;

    @Schema(description = "项目数据范围,0:全选 1:自定义")
    private Integer dataScopeProject;

    /**
     * 数据范围(指定客户数组)
     * <p>
     */
    @Schema(description = "可查看的客户数组", example = "[1, 3]")
    private Set<Long> dataScopeCustomerIds;

    /**
     * 数据范围(指定供应商数组)
     * <p>
     */
    @Schema(description = "可查看的供应商数组", example = "[1, 3]")
    private Set<Long> dataScopeSupplierIds;

    /**
     * 数据范围(指定合同数组)
     * <p>
     */
    @Schema(description = "可查看的合同数组", example = "[1, 3]")
    private Set<Long> dataScopeContractIds;

    /**
     * 数据范围(指定项目数组)
     * <p>
     */
    @Schema(description = "可查看的项目数组", example = "[1, 3]")
    private Set<Long> dataScopeProjectIds;

}
