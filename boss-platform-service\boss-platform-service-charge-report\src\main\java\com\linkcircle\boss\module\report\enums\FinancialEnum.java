package com.linkcircle.boss.module.report.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/24 19:29
 */
public interface FinancialEnum {
    @Getter
    enum QueryCondition {
        CUSTOMER_ID("customer_id", "客户id"),
        ACCOUNT_ID("account_id", "账号id"),
        SUBSCRIBE_ID("subscribe_id", "订阅id"),
        BILLING_TIME("billing_time", "账单生成时间");
        private final String code;
        private final String name;

        QueryCondition(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }


    @Getter
    enum QueryConditionAssociateTable {
        CUSTOMER_ID("boss-platform-charge","charge_customer_info", "id","customer_name"),
        ACCOUNT_ID("boss-platform-charge","charge_customer_accounts_info", "id","account_name");
        private final String actualName;
        private final String tableName;
        private final String associateName;
        private final String columnName;

        QueryConditionAssociateTable(String actualName, String tableName, String associateName, String columnName) {
            this.actualName = actualName;
            this.tableName = tableName;
            this.associateName = associateName;
            this.columnName = columnName;
        }
    }

    @Getter
    enum TableCode {
        /**
         * 后付费账单详情表
         */
        POST_PAID("postpaid_income_bill_detail"),
        /**
         * 预付费账单详情表
         */
        PRE_PAID("prepaid_income_bill_detail");
        private final String code;

        TableCode(String code) {
            this.code = code;
        }

        public static TableCode of(String tableCode) {
            if (PRE_PAID.getCode().equals(tableCode)) {
                return PRE_PAID;
            }
            return POST_PAID;
        }

        public boolean isPostPaid() {
            return code.equals(POST_PAID.getCode());
        }
    }


    @Getter
    enum BillColumn {
        ACCOUNT("accountId", "account_id", "accountName", "账号ID-账单表中字段名"),
        SERVICE("serviceId", "service_id", "serviceName", "服务id-账单表中字段名"),
        CUSTOMER("customerId", "customer_id", "customerName", "客户id-账单表中字段名"),
        PRODUCT("productId", "product_id", "productName", "产品id-账单表中字段名"),
        ENTITY("entityId", "entity_id", "entityName", "主体id-账单表中字段名"),
        CONTRACT("contractId", "contract_id", "contractName", "合同id-账单表中字段名"),
        WALLET("walletId", "wallet_id", "walletName", "钱包id-账单表中字段名"),
        PLAN("planId", "plan_id", "planName", "计划id-账单表中字段名"),
        DISCOUNT("discountId", "discount_id", "discountName", "优惠id-账单表中字段名"),
        RESOURCE("resourceId", "resource_id", "resourceName", "资源id"),
        RESOURCE_SERVICE("resourceServiceId", "resource_service_id", "resourceServiceName", "资源服务id");;
        //SUBSCRIPTION("subscriptionId","subscription_id","subscriptionName","订阅id-账单表中字段名");
        private final String javaName;
        private final String columnName;
        private final String showName;
        private final String description;

        BillColumn(String javaName, String columnName, String showName, String description) {
            this.columnName = columnName;
            this.description = description;
            this.javaName = javaName;
            this.showName = showName;
        }
    }
}
