package com.linkcircle.boss.module.billing.web.stats.event;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-01-03 15:00
 * @description 客户使用量统计事件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerUsageStatsEvent extends UsageStatsEvent {

    /**
     * 服务编码
     */
    private String serviceCode;

    /**
     * 支付类型 (0-预付费, 1-后付费)
     */
    private Integer paymentType;

    /**
     * 计费类型 (0-固定费率, 1-阶梯费率, 2-套餐计费, 3-按量计费)
     */
    private Integer chargeType;

    public CustomerUsageStatsEvent(Object source) {
        super(source);
    }

    @Override
    public String getEventType() {
        return "CUSTOMER_USAGE_STATS";
    }

    /**
     * 构建客户使用量统计事件
     */
    @Builder
    public static CustomerUsageStatsEvent create(Object source, Long accountId, Long productId, Long serviceId,
                                                String billDate, String serviceCode, Integer paymentType, Integer chargeType,
                                                BigDecimal chargeUsageCount, BigDecimal usageCount, 
                                                BigDecimal cashAmount, BigDecimal pointAmount) {
        CustomerUsageStatsEvent event = new CustomerUsageStatsEvent(source);
        event.setAccountId(accountId);
        event.setProductId(productId);
        event.setServiceId(serviceId);
        event.setBillDate(billDate);
        event.setServiceCode(serviceCode);
        event.setPaymentType(paymentType);
        event.setChargeType(chargeType);
        event.setChargeUsageCount(chargeUsageCount);
        event.setUsageCount(usageCount);
        event.setCashAmount(cashAmount);
        event.setPointAmount(pointAmount);
        return event;
    }
}
