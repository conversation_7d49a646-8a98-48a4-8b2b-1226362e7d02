package com.linkcircle.boss.module.report.web.financial.model.dto;

import com.linkcircle.boss.framework.common.model.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/20 11:22
 * @description 客户账单查询请求DTO
 */
@Schema(description = "财务 - 明细查询 Request DTO")
@Data
public class ChargeFinancialReqDTO extends PageParam {


    @Schema(description = "模板ID", example = "********")
    @NotNull(message = "模板ID不能为空")
    private Long templateId;

    @Schema(description = "客户id")
    private Long customerId;

    @Schema(description = "账户id")
    private Long accountId;

    @Schema(description = "订阅id")
    private Long subscribeId;

    @Schema(description = "开始时间")
    private Long billingStartTime;

    @Schema(description = "结束时间")
    private Long billingEndTime;


}
