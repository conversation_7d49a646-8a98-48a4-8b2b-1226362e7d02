package com.linkcircle.boss.module.charge.crm.web.purchase.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ResourcePurchaseDetailVO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 供应商id
     */
    private Long suppliersId;

    /**
     * 账户ID
     */
    private Long accountId;

    /**
     * 合同ID
     */
    private Long contractId;

    /**
     * 主体ID
     */
    private Long entityId;

    /**
     * 支付类型，0：预付费，1后付费
     */
    private Integer paymentType;

    /**
     * 采购开始时间
     */
    private Long startTime;

    /**
     * 采购结束时间
     */
    private Long endTime;

    /**
     * 开单主体ID
     */
    private Long billingEntityId;

    /**
     * 是否含税，0：不含税，1：含税
     */
    private Boolean isTaxInclusive;

    /**
     * 税率百分比（示例：9.00）
     */
    private BigDecimal rate;

    /**
     * 出账周期类型（数据字典）
     */
    private Integer cycleType;

    /**
     * 天数
     */
    private Integer days;

    /**
     * 状态，0：已结束，1：已生效，2：试用中，3：已取消
     */
    private Integer status;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 采购详情
     */
    private List<Detail> details;

    @Data
    public static class Detail {
        /**
         * 主键id
         */
        private Long id;

        /**
         * 供应商id
         */
        private Long supplierId;

        /**
         * 账户ID
         */
        private Long accountId;

        /**
         * 供应商资源采购信息表id
         */
        private Long purchaseId;

        /**
         * 资源服务id
         */
        private Long resourceServiceId;

        /**
         * 服务编码
         */
        private String serviceName;

        /**
         * 服务编码
         */
        private String resourceServiceCode;

        /**
         * 量表表结构id
         */
        private Long scaleId;

        /**
         * 服务描述
         */
        private String description;

        /**
         * 价格配置json
         */
        private String currencyPriceJson;

        /**
         * 计费类型：
         * 0: 固定费率
         * 1: 阶梯费率
         * 2: 套餐计费
         * 3: 按量计费
         */
        private Integer chargeType;

        /**
         * 支付方式：
         * 0: 现金
         * 1: 积分
         */
        private Integer paymentOptions;

        /**
         * 固定费率，阶梯费率 单位标签
         */
        private String unitLabel;

        /**
         * 固定费率，阶梯费率可以选择购买的数量
         */
        private Integer purchaseCount;

        /**
         * 是否含套餐外：
         * 0: 不包含
         * 1: 包含
         */
        private Integer inPackage;

        /**
         * 间隔时长
         */
        private Integer period;

        /**
         * 间隔时长单位, 0：一次性, 1:日，2：周，3：月，4：季度，5：年
         */
        private Integer unitPeriod;

        /**
         * 间隔时长单位, 0：一次性, 1:日，2：周，3：月，4：季度，5：年
         */
        private Integer count;

    }

    public boolean hasValidPurchase() {
        return status != null && (status == 1 || status == 2);
    }
}
