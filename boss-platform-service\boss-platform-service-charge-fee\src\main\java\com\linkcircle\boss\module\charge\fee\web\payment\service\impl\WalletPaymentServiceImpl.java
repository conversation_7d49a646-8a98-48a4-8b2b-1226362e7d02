package com.linkcircle.boss.module.charge.fee.web.payment.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.linkcircle.boss.framework.common.constants.ChargeTopicConstant;
import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.util.cache.ChargeCacheUtils;
import com.linkcircle.boss.framework.sharding.algorithms.dto.HitBusinessTimeDTO;
import com.linkcircle.boss.framework.sharding.constants.LogicTableConstant;
import com.linkcircle.boss.framework.tanant.TenantIgnore;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductIncomeBillDO;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO;
import com.linkcircle.boss.module.billing.api.detail.income.model.entity.PrepaidIncomeBillDetailDO;
import com.linkcircle.boss.module.billing.api.wallet.model.dto.WalletDeductionMqDTO;
import com.linkcircle.boss.module.charge.fee.enums.InvoiceEnum;
import com.linkcircle.boss.module.charge.fee.enums.WalletsDeductStatusEnum;
import com.linkcircle.boss.module.charge.fee.enums.WalletsEventEnum;
import com.linkcircle.boss.module.charge.fee.web.bill.mapper.MakeupBillMapper;
import com.linkcircle.boss.module.charge.fee.web.bill.model.entity.MakeupIncomeBillDO;
import com.linkcircle.boss.module.charge.fee.web.bill.service.MakeupBillService;
import com.linkcircle.boss.module.charge.fee.web.payment.model.dto.ThirdPartyNotifyDTO;
import com.linkcircle.boss.module.charge.fee.web.payment.service.*;
import com.linkcircle.boss.module.fee.api.wallets.model.dto.WalletBalanceEditDTO;
import com.linkcircle.boss.module.fee.api.wallets.model.dto.WalletBalanceToPayDTO;
import com.linkcircle.boss.module.fee.api.wallets.model.entity.ChargeCustomerAccountWalletsDO;
import com.linkcircle.boss.module.fee.api.wallets.model.entity.ChargeCustomerAccountWalletsTransactionsDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.baomidou.mybatisplus.core.toolkit.Wrappers.lambdaUpdate;


@Service
@Slf4j
public class WalletPaymentServiceImpl implements IWalletPaymentService {

    /**
     * 后付费账单
     */
    @Autowired
    private IPostpaidProductIncomeBillService postpaidProductIncomeBillService;
    @Autowired
    private IPostpaidProductServiceIncomeBillService postpaidProductServiceIncomeBillService;
    /**
     * 预付费账单详情
     */
    @Autowired
    private IPrepaidIncomeBillDetailService prepaidIncomeBillDetailService;
    /**
     * 钱包信息
     */
    @Autowired
    private IChargeCustomerAccountWalletsService chargeCustomerAccountWalletsService;
    /**
     * 钱包交易明细
     */
    @Autowired
    private IChargeCustomerAccountWalletsTransactionsService chargeCustomerAccountWalletsTransactionsService;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private IWalletPaymentService walletPaymentService;
    /**
     * 手工账单
     */
    @Autowired
    private MakeupBillMapper makeupBillMapper;
    @Autowired
    private MakeupBillService makeupBillService;
    /**
     * 回调 服务
     */
    @Autowired
    private IThirdPartyNotifyService thirdPartyNotifyService;

    @Override
    public CommonResult<Long> walletBalanceToPay(WalletBalanceToPayDTO walletBalanceToPayDTO) throws Exception {
        WalletDeductionMqDTO walletDeductionMqDTO = new WalletDeductionMqDTO();
//        walletDeductionMqDTO.setWalletsId(walletBalanceToPayDTO.getWalletsId());
        walletDeductionMqDTO.setBillId(walletBalanceToPayDTO.getBillId() + "");
        walletDeductionMqDTO.setBusinessTime(walletBalanceToPayDTO.getBusinessTime());
        walletDeductionMqDTO.setServiceCode(walletBalanceToPayDTO.getServiceCode());
        walletDeductionMqDTO.setPaymentMethod(walletBalanceToPayDTO.getPaymentMethod());
        walletDeductionMqDTO.setPaymentType(walletBalanceToPayDTO.getPaymentType());

        Long accountId = null;
        RLock rLock = null;
        boolean locked = false;

        Integer paymentType = walletBalanceToPayDTO.getPaymentType();
        switch (paymentType) {
            case 0:
//                prepaidIncomeBillDetailService
                HitBusinessTimeDTO businessTimeDTO = new HitBusinessTimeDTO();
                businessTimeDTO.setTimestamp(walletBalanceToPayDTO.getBusinessTime());
                businessTimeDTO.setServiceCodes(List.of(walletBalanceToPayDTO.getServiceCode()));
                try (HintManager hintManager = HintManager.getInstance();) {
                    hintManager.addTableShardingValue(LogicTableConstant.PREPAID_INCOME_BILL_DETAIL, businessTimeDTO);
                    PrepaidIncomeBillDetailDO prepaidIncomeBillDetailDO = prepaidIncomeBillDetailService.getById(walletBalanceToPayDTO.getBillId());
                    walletDeductionMqDTO.setWalletsId(prepaidIncomeBillDetailDO.getWalletId());
                    accountId = prepaidIncomeBillDetailDO.getAccountId();
                    walletDeductionMqDTO.setAccountId(accountId);
                }
                break;
            case 1:
//                postpaidProductIncomeBillService
                PostpaidProductServiceIncomeBillDO postpaidProductServiceIncomeBillDO = postpaidProductServiceIncomeBillService.getById(walletBalanceToPayDTO.getBillId());
                accountId = postpaidProductServiceIncomeBillDO.getAccountId();
                walletDeductionMqDTO.setWalletsId(postpaidProductServiceIncomeBillDO.getWalletId());
                walletDeductionMqDTO.setAccountId(accountId);
                break;
            default:
                throw new ServiceException(500, "[钱包余额支付]支付类型错误");
        }

        if (ObjectUtil.isNotNull(accountId)) {
            // 开始对账户上锁
            rLock = redissonClient.getLock(ChargeCacheUtils.getConsumerPaymentLockKey(walletDeductionMqDTO.getAccountId()));
            // 尝试获取锁，等待10秒，锁自动过期30秒
            locked = rLock.tryLock(10, TimeUnit.SECONDS);
            if (!locked) {
                log.error("[支付流程]账户{}获取锁失败, 钱包: {}", walletDeductionMqDTO.getAccountId(), walletDeductionMqDTO.getWalletsId());
                throw new RuntimeException("获取锁失败，触发重试");
            }
            // 开始支付流程
            boolean result = walletPaymentService.dealPaymentBusiness(walletDeductionMqDTO);
            return result ? CommonResult.success() : CommonResult.error(500, "支付失败");
        }

        return CommonResult.error(500, "支付失败,缺少账户等信息");
    }


    /**
     * 支付流程
     */
    @Override
    @Transactional
    @TenantIgnore
    public boolean dealPaymentBusiness(WalletDeductionMqDTO walletDeductionMqDTO) throws Exception {
        ChargeCustomerAccountWalletsDO walletsDO = chargeCustomerAccountWalletsService.getById(walletDeductionMqDTO.getWalletsId());
        if (ObjectUtil.isEmpty(walletsDO)) {
            log.error("[支付流程]钱包不存在, id:{}", walletDeductionMqDTO.getWalletsId());
            return false;
        }

        // 支付方式，0：现金，1积分
        Integer paymentMethod = walletDeductionMqDTO.getPaymentMethod();
        // 支付类型，0：预付费，1后付费 2手工账单
        Integer paymentType = walletDeductionMqDTO.getPaymentType();

        if (paymentType == 2 && paymentMethod == 1) {
            log.error("[支付流程]手工账单不能使用积分支付, id:{}", walletDeductionMqDTO.getBillId());
            return false;
        }

        Long businessTime = walletDeductionMqDTO.getBusinessTime();
        String serviceCode = walletDeductionMqDTO.getServiceCode();
        HitBusinessTimeDTO businessTimeDTO = HitBusinessTimeDTO.build(businessTime, List.of(serviceCode));
        LambdaUpdateWrapper<ChargeCustomerAccountWalletsDO> updateWrapper = lambdaUpdate();

        /*
         * 处理幂等性, 检查推送的订单号是否已经支付
         * 通过 billId 校验账单是否已经处理过, 只有待支付、未结清才处理
         * 返回 cashAmount、pointAmount 是预付或者后付的现金或者积分
         */
        JSONObject billStatusRs = this.checkBillStatus(paymentType, Long.valueOf(walletDeductionMqDTO.getBillId()), businessTimeDTO);
        Boolean isNull = billStatusRs.getBoolean("isNull");
        if (isNull) {
            log.info("[支付流程]订单不存在, 不处理; 账单类型: {}, 订单id: {}", paymentType, walletDeductionMqDTO.getBillId());
            return false;
        }

        BigDecimal cashAmount = billStatusRs.getBigDecimal("cashAmount");
        BigDecimal pointAmount = billStatusRs.getBigDecimal("pointAmount");
        if (paymentMethod == 0 && cashAmount == null) {
            log.info("[支付流程]订单缺少金额信息, 不处理; 账单类型: {}, 订单id: {}", paymentType, walletDeductionMqDTO.getBillId());
            return false;
        }
        if (paymentMethod == 1 && pointAmount == null) {
            log.info("[支付流程]订单缺少金额信息, 不处理; 账单类型: {}, 订单id: {}", paymentType, walletDeductionMqDTO.getBillId());
            return false;
        }

        billStatusRs.put("paymentMethod", paymentMethod);
        Boolean checkBillStatus = billStatusRs.getBoolean("checkBillStatus");
        if (!checkBillStatus) {
            log.info("[支付流程]订单已支付或者不存在, 不处理; 账单类型: {}, 订单id: {}", paymentType, walletDeductionMqDTO.getBillId());
            return false;
        }
        int billStatus = InvoiceEnum.BillStatus.WAIT_PAYMENT.getCode();

        switch (paymentMethod) {
            case 0:
                // 钱包余额
                BigDecimal cashBalance = walletsDO.getCashBalance();
                // 待扣 含税金额
                BigDecimal amountWithTax = billStatusRs.getBigDecimal("cashAmount");
                // 比较扣费金额(含税总金额) 是否超过钱包余额
                // 如果钱包余额不足 开始配置订单状态为金额不足无法付款 等待后续手动结清账单
                int comparedCash = BigDecimal.ZERO.compareTo(cashBalance);
                if (comparedCash >= 0) {
                    boolean updated = this.updateBillNotSufficientFundsStatus(paymentType, Long.valueOf(walletDeductionMqDTO.getBillId()),
                            InvoiceEnum.BillStatus.NOTSUFFICIENTFUNDS.getCode(), businessTimeDTO, billStatusRs);
                    if (!updated) {
                        log.error("[支付流程]现金余额不足更新订单状态失败, 钱包:{}, 账单:{}", walletsDO.getId(), walletDeductionMqDTO.getBillId());
                        throw new ServiceException(500, "[余额不足]更新订单状态失败");
                    }
                }

                /*
                 * 开始现金扣费逻辑 更新钱包余额 cashBalance(余额) - amountWithTax(带税金额|积分)
                 * 扣款后如果是负数 表示未结清
                 * 扣款后如果是正数 已支付
                 */
                BigDecimal subtract = cashBalance.subtract(amountWithTax);
                updateWrapper.eq(ChargeCustomerAccountWalletsDO::getId, walletsDO.getId())
                        .set(ChargeCustomerAccountWalletsDO::getCashBalance, subtract);
                boolean deductionRS = chargeCustomerAccountWalletsService.update(updateWrapper);
                if (!deductionRS) {
                    log.error("[支付流程]现金扣款失败, 账单:{}, 钱包:{}, 余额:{}, 待扣:{}", walletDeductionMqDTO.getBillId(), walletsDO.getId(), cashBalance, amountWithTax);
                    throw new ServiceException(500, "钱包扣款失败");
                }

                // 未结清方式支付
                if (subtract.compareTo(BigDecimal.ZERO) < 0) {
                    billStatusRs.put("paidAmount", cashBalance);
                    // 负数转正数 记录未支付金额
                    BigDecimal unPaid = subtract.multiply(BigDecimal.valueOf(-1));
                    billStatusRs.put("unPaidAmount", unPaid);
                    // 余额不足 未结清
                    boolean updatedCashWalletsStatus = this.updateAccountBalanceBelowStatus(paymentType, Long.valueOf(walletDeductionMqDTO.getBillId()),
                            InvoiceEnum.BillStatus.UNSETTLED.getCode(), WalletsDeductStatusEnum.UNSETTLED.getCode(), businessTimeDTO, billStatusRs);
                    if (!updatedCashWalletsStatus) {
                        log.error("[支付流程]现金账单扣款状态修改, 钱包:{}, 账单:{}", walletsDO.getId(), walletDeductionMqDTO.getBillId());
                        throw new ServiceException(500, "[扣款后更新账单状态]更新订单状态失败");
                    }
                    billStatus = InvoiceEnum.BillStatus.UNSETTLED.getCode();
                } else {
                    // 可以结清 已支付金额是带税金额 未支付金额则是 0
                    billStatusRs.put("paidAmount", amountWithTax);
                    billStatusRs.put("unPaidAmount", BigDecimal.ZERO);
                    // 结清更新
                    boolean updatedCashWalletsStatus = this.updateAccountBalanceBelowStatus(paymentType, Long.valueOf(walletDeductionMqDTO.getBillId()),
                            InvoiceEnum.BillStatus.PAID.getCode(), WalletsDeductStatusEnum.SUCCESS.getCode(), businessTimeDTO, billStatusRs);
                    if (!updatedCashWalletsStatus) {
                        log.error("[支付流程]现金账单扣款状态修改, 钱包:{}, 账单:{}", walletsDO.getId(), walletDeductionMqDTO.getBillId());
                        throw new ServiceException(500, "[扣款后更新账单状态]更新订单状态失败");
                    }
                    billStatus = InvoiceEnum.BillStatus.PAID.getCode();
                }

                ChargeCustomerAccountWalletsTransactionsDO transactionsDO = ChargeCustomerAccountWalletsTransactionsDO.builder()
                        .accountId(walletsDO.getAccountId())
                        .walletsId(walletsDO.getId())
                        .walletsEven(WalletsEventEnum.DEDUCTION.getCode())
                        .customerId(walletDeductionMqDTO.getCustomerId())
                        .cashAmount(amountWithTax)
                        .balanceCash(subtract)
                        .pointsAmount(BigDecimal.ZERO) // 现金扣款不涉及积分
                        .balancePoints(walletsDO.getPointsBalance()) // 积分余额不变
                        .transactionTime(walletDeductionMqDTO.getBusinessTime())
                        .billId(Long.valueOf(walletDeductionMqDTO.getBillId()))
                        .billType(paymentType)
                        .tenantId(walletsDO.getTenantId())
                        .build();
//                long recordTime = LocalDateTime.now().atZone(ZoneId.of("UTC")).toInstant().toEpochMilli();
                long recordTime = Calendar.getInstance().getTimeInMillis();
                transactionsDO.setCreateTime(recordTime);
                boolean savedWalletOperation = chargeCustomerAccountWalletsTransactionsService.recordHistory(transactionsDO);
                if (!savedWalletOperation) {
                    log.error("[支付流程]现金钱包交易记录保存失败, 钱包:{}, 账单:{}", walletsDO.getId(), walletDeductionMqDTO.getBillId());
                    throw new ServiceException(500, "[支付流程]钱包交易记录保存失败");
                }

                // 前面步骤均无异常开始调用通知第三方接口
                if (paymentType == 1 || paymentType == 0) {
                    ThirdPartyNotifyDTO thirdPartyNotifyDTO = ThirdPartyNotifyDTO.builder()
                            .transactionId(transactionsDO.getId())
                            .transactionTime(transactionsDO.getTransactionTime())
                            .billId(transactionsDO.getBillId())
                            .billType(paymentType)
                            .billStatus(billStatus)
                            .pointBalance(transactionsDO.getBalancePoints())
                            .cashBalance(transactionsDO.getBalanceCash())
                            .build();
                    thirdPartyNotifyService.notifyThirdParty(walletDeductionMqDTO.getCallbackUrl(), businessTimeDTO, thirdPartyNotifyDTO);
                }

                break;
            case 1:
                // 待扣积分
                BigDecimal discountedPrice = billStatusRs.getBigDecimal("pointAmount");
                // 钱包积分余额
                BigDecimal pointsBalance = walletsDO.getPointsBalance();
                // 比较积分余额 是否不足; 如果积分不足 配置账单状态余额不足
                int comparedPoints = BigDecimal.ZERO.compareTo(pointsBalance);
                if (comparedPoints >= 0) {
                    boolean updated = this.updateBillNotSufficientFundsStatus(paymentType, Long.valueOf(walletDeductionMqDTO.getBillId()),
                            InvoiceEnum.BillStatus.NOTSUFFICIENTFUNDS.getCode(), businessTimeDTO, billStatusRs);
                    if (!updated) {
                        log.error("[支付流程]积分不足, 更新订单状态失败, 钱包:{}, 账单:{}", walletsDO.getId(), walletDeductionMqDTO.getBillId());
                        throw new ServiceException(500, "[积分不足]更新订单状态失败");
                    }
                }

                /*
                 * 开始积分扣费逻辑
                 */
                BigDecimal pointDiff = pointsBalance.subtract(discountedPrice);
                updateWrapper.eq(ChargeCustomerAccountWalletsDO::getId, walletsDO.getId())
                        .set(ChargeCustomerAccountWalletsDO::getPointsBalance, pointDiff);
                boolean deductionPointRS = chargeCustomerAccountWalletsService.update(updateWrapper);
                if (!deductionPointRS) {
                    log.error("[支付流程]积分扣款失败, 账单:{}, 钱包:{}, 余额:{}, 待扣:{}", walletDeductionMqDTO.getBillId(), walletsDO.getId(), pointsBalance, discountedPrice);
                    throw new ServiceException(500, "钱包扣款失败");
                }

                // 未结清方式支付
                if (pointDiff.compareTo(BigDecimal.ZERO) < 0) {
                    billStatusRs.put("paidAmount", pointsBalance);
                    // 负数转正数 记录未支付金额
                    BigDecimal unPaid = pointDiff.multiply(BigDecimal.valueOf(-1));
                    billStatusRs.put("unPaidAmount", unPaid);

                    // 扣费后更新账单未结清状态 只有部分账单可以支付完成
                    boolean updatedPointWalletsStatus = this.updateAccountBalanceBelowStatus(paymentType, Long.valueOf(walletDeductionMqDTO.getBillId()),
                            InvoiceEnum.BillStatus.UNSETTLED.getCode(), WalletsDeductStatusEnum.UNSETTLED.getCode(), businessTimeDTO, billStatusRs);
                    if (!updatedPointWalletsStatus) {
                        log.error("[支付流程]积分账单扣款状态修改, 钱包:{}, 账单:{}", walletsDO.getId(), walletDeductionMqDTO.getBillId());
                        throw new ServiceException(500, "[扣款后更新积分账单状态]更新订单状态失败");
                    }
                    billStatus = InvoiceEnum.BillStatus.UNSETTLED.getCode();
                } else {
                    // 积分余额充足
                    billStatusRs.put("paidAmount", discountedPrice);
                    billStatusRs.put("unPaidAmount", BigDecimal.ZERO);
                    boolean updatedPointWalletsStatus = this.updateAccountBalanceBelowStatus(paymentType, Long.valueOf(walletDeductionMqDTO.getBillId()),
                            InvoiceEnum.BillStatus.PAID.getCode(), WalletsDeductStatusEnum.SUCCESS.getCode(), businessTimeDTO, billStatusRs);
                    if (!updatedPointWalletsStatus) {
                        log.error("[支付流程]积分账单扣款状态修改, 钱包:{}, 账单:{}", walletsDO.getId(), walletDeductionMqDTO.getBillId());
                        throw new ServiceException(500, "[扣款后更新积分账单状态]更新订单状态失败");
                    }
                    billStatus = InvoiceEnum.BillStatus.PAID.getCode();
                }


                ChargeCustomerAccountWalletsTransactionsDO transactionsPointDO = ChargeCustomerAccountWalletsTransactionsDO.builder()
                        .accountId(walletsDO.getAccountId())
                        .walletsId(walletsDO.getId())
                        .walletsEven(WalletsEventEnum.DEDUCTION.getCode())
                        .customerId(walletDeductionMqDTO.getCustomerId())
                        .pointsAmount(discountedPrice)
                        .balancePoints(pointDiff)
                        .cashAmount(BigDecimal.ZERO) // 积分扣款不涉及现金
                        .balanceCash(walletsDO.getCashBalance()) // 现金余额不变
                        .transactionTime(walletDeductionMqDTO.getBusinessTime())
                        .billType(paymentType)
                        .billId(Long.valueOf(walletDeductionMqDTO.getBillId()))
                        .tenantId(walletsDO.getTenantId())
                        .build();
                long recordPayTime = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                transactionsPointDO.setCreateTime(recordPayTime);
                boolean savedPointWalletOperation = chargeCustomerAccountWalletsTransactionsService.recordHistory(transactionsPointDO);
                if (!savedPointWalletOperation) {
                    log.error("[支付流程]积分钱包交易记录保存失败, 钱包:{}, 账单:{}", walletsDO.getId(), walletDeductionMqDTO.getBillId());
                    throw new ServiceException(500, "积分钱包交易记录保存失败");
                }

                // 前面步骤均无异常开始调用通知第三方接口
                if (paymentType == 1 || paymentType == 0) {
                    ThirdPartyNotifyDTO thirdPartyNotifyDTO = ThirdPartyNotifyDTO.builder()
                            .transactionId(transactionsPointDO.getId())
                            .transactionTime(transactionsPointDO.getTransactionTime())
                            .billId(transactionsPointDO.getBillId())
                            .billType(paymentType)
                            .billStatus(billStatus)
                            .pointBalance(transactionsPointDO.getBalancePoints())
                            .cashBalance(transactionsPointDO.getBalanceCash())
                            .build();
                    thirdPartyNotifyService.notifyThirdParty(walletDeductionMqDTO.getCallbackUrl(), businessTimeDTO, thirdPartyNotifyDTO);
                }

                break;
            default:
                log.error("[支付流程]付款类型错误, 钱包:{}, 账单:{}, paymentMethod:{}", walletsDO.getId(), walletDeductionMqDTO.getBillId(), paymentMethod);
                return false;
        }
        return true;
    }

    /**
     * 钱包扣款或者充值
     *
     * @param walletBalanceEditDTO
     * @return
     */
    @Override
    @Transactional
    public boolean walletBalanceEdit(WalletBalanceEditDTO walletBalanceEditDTO) {
        // 钱包类型，0-现金，1-积分
        Integer walletType = walletBalanceEditDTO.getWalletType();
        // 钱包操作类型：1-充值，2-扣除
        Integer editType = walletBalanceEditDTO.getEditType();
        BigDecimal editNumBD = new BigDecimal(walletBalanceEditDTO.getEditNum());
        Long walletId = walletBalanceEditDTO.getWalletId();

        boolean updated = false;
        boolean recorded = false;

        try {
            LambdaUpdateWrapper<ChargeCustomerAccountWalletsDO> updateWrapper = lambdaUpdate();

            ChargeCustomerAccountWalletsDO walletsDO = chargeCustomerAccountWalletsService.getById(walletId);
            BigDecimal pointsBalance = walletsDO.getPointsBalance();
            BigDecimal cashBalance = walletsDO.getCashBalance();
            // 积分充值
            if (walletType == 1 && editType == 1) {
                BigDecimal added = pointsBalance.add(editNumBD);
                updateWrapper.eq(ChargeCustomerAccountWalletsDO::getId, walletId)
                        .set(ChargeCustomerAccountWalletsDO::getPointsBalance, added);
                updated = chargeCustomerAccountWalletsService.update(updateWrapper);

                ChargeCustomerAccountWalletsTransactionsDO transactionsDO = ChargeCustomerAccountWalletsTransactionsDO.builder()
                        .accountId(walletsDO.getAccountId())
                        .walletsId(walletsDO.getId())
                        .walletsEven(WalletsEventEnum.RECHARGE.getCode())
                        .customerId(walletBalanceEditDTO.getCustomerId())
                        .cashAmount(BigDecimal.ZERO)// 积分扣款不涉及现金
                        .balanceCash(walletsDO.getCashBalance())
                        .pointsAmount(editNumBD)
                        .balancePoints(added)
                        .transactionTime(walletBalanceEditDTO.getTransactionTime())
                        .build();
                recorded = chargeCustomerAccountWalletsTransactionsService.recordHistory(transactionsDO);
            }
            // 积分扣费
            if (walletType == 1 && editType == 2) {
                BigDecimal subtracted = pointsBalance.subtract(editNumBD);
                updateWrapper.eq(ChargeCustomerAccountWalletsDO::getId, walletId)
                        .set(ChargeCustomerAccountWalletsDO::getPointsBalance, subtracted);
                updated = chargeCustomerAccountWalletsService.update(updateWrapper);

                ChargeCustomerAccountWalletsTransactionsDO transactionsDO = ChargeCustomerAccountWalletsTransactionsDO.builder()
                        .accountId(walletsDO.getAccountId())
                        .walletsId(walletsDO.getId())
                        .walletsEven(WalletsEventEnum.DEDUCTION.getCode())
                        .customerId(walletBalanceEditDTO.getCustomerId())
                        .cashAmount(BigDecimal.ZERO)// 积分扣款不涉及现金
                        .balanceCash(walletsDO.getCashBalance())
                        .pointsAmount(editNumBD)
                        .balancePoints(subtracted)
                        .transactionTime(walletBalanceEditDTO.getTransactionTime())
                        .build();
                recorded = chargeCustomerAccountWalletsTransactionsService.recordHistory(transactionsDO);
            }

            // 现金充值
            if (walletType == 0 && editType == 1) {
                if (walletBalanceEditDTO.getUnifiedRechargeId() == null) {
                    return false;
                }

                BigDecimal added = cashBalance.add(editNumBD);
                updateWrapper.eq(ChargeCustomerAccountWalletsDO::getId, walletId)
                        .set(ChargeCustomerAccountWalletsDO::getCashBalance, added);
                updated = chargeCustomerAccountWalletsService.update(updateWrapper);

                ChargeCustomerAccountWalletsTransactionsDO transactionsDO = ChargeCustomerAccountWalletsTransactionsDO.builder()
                        .accountId(walletsDO.getAccountId())
                        .walletsId(walletsDO.getId())
                        .walletsEven(WalletsEventEnum.DEDUCTION.getCode())
                        .customerId(walletBalanceEditDTO.getCustomerId())
                        .cashAmount(editNumBD)
                        .balanceCash(added)
                        .pointsAmount(BigDecimal.ZERO) // 现金扣款不涉及积分
                        .balancePoints(walletsDO.getPointsBalance()) // 积分余额不变
                        .transactionTime(walletBalanceEditDTO.getTransactionTime())
                        .unifiedRechargeId(walletBalanceEditDTO.getUnifiedRechargeId())
                        .build();
                recorded = chargeCustomerAccountWalletsTransactionsService.recordHistory(transactionsDO);
            }
            // 现金扣费
            if (walletType == 0 && editType == 2) {
                BigDecimal subtracted = cashBalance.subtract(editNumBD);
                updateWrapper.eq(ChargeCustomerAccountWalletsDO::getId, walletId)
                        .set(ChargeCustomerAccountWalletsDO::getCashBalance, subtracted);
                updated = chargeCustomerAccountWalletsService.update(updateWrapper);

                ChargeCustomerAccountWalletsTransactionsDO transactionsDO = ChargeCustomerAccountWalletsTransactionsDO.builder()
                        .accountId(walletsDO.getAccountId())
                        .walletsId(walletsDO.getId())
                        .walletsEven(WalletsEventEnum.DEDUCTION.getCode())
                        .customerId(walletBalanceEditDTO.getCustomerId())
                        .cashAmount(editNumBD)
                        .balanceCash(subtracted)
                        .pointsAmount(BigDecimal.ZERO) // 现金扣款不涉及积分
                        .balancePoints(walletsDO.getPointsBalance()) // 积分余额不变
                        .transactionTime(walletBalanceEditDTO.getTransactionTime())
                        .build();
                recorded = chargeCustomerAccountWalletsTransactionsService.recordHistory(transactionsDO);
            }
        } catch (Exception e) {
            log.error("更新钱包余额失败; " + e.getMessage(), e);
            throw new ServiceException(500, "更新钱包余额失败;");
        }

        if (!updated || !recorded) {
            throw new ServiceException(500, "更新钱包失败;");
        }

        return true;
    }

    /**
     * 代充值
     *
     * @param walletId        充值钱包id
     * @param amount          充值量
     * @param walletType      充值类型 0-现金，1-积分
     * @param customerId      充值客户id
     * @param fromCustomerId  代充客户id
     * @param transactionTime 交易时间
     * @return
     */
    @Override
    @Transactional
    public boolean walletBalanceProxyPay(Long walletId, BigDecimal amount, Integer walletType,
                                         Long customerId, Long fromCustomerId, Long transactionTime) {
        boolean updated = false;
        boolean recorded = false;
        try {
            LambdaUpdateWrapper<ChargeCustomerAccountWalletsDO> updateWrapper = lambdaUpdate();

            ChargeCustomerAccountWalletsDO walletsDO = chargeCustomerAccountWalletsService.getById(walletId);
            BigDecimal pointsBalance = walletsDO.getPointsBalance();
            BigDecimal cashBalance = walletsDO.getCashBalance();

            switch (walletType) {
                case 0:
                    BigDecimal added = cashBalance.add(amount);
                    updateWrapper.eq(ChargeCustomerAccountWalletsDO::getId, walletId)
                            .set(ChargeCustomerAccountWalletsDO::getCashBalance, added);
                    updated = chargeCustomerAccountWalletsService.update(updateWrapper);

                    ChargeCustomerAccountWalletsTransactionsDO transactionsDO = ChargeCustomerAccountWalletsTransactionsDO.builder()
                            .accountId(walletsDO.getAccountId())
                            .walletsId(walletsDO.getId())
                            .walletsEven(WalletsEventEnum.AGENT_RECHARGE.getCode())
                            .customerId(customerId)
                            .cashAmount(amount)
                            .balanceCash(added)
                            .pointsAmount(BigDecimal.ZERO) // 现金扣款不涉及积分
                            .balancePoints(walletsDO.getPointsBalance()) // 积分余额不变
                            .transactionTime(transactionTime)
                            .fromCustomerId(fromCustomerId)
                            .tenantId(walletsDO.getTenantId())
                            .build();
                    recorded = chargeCustomerAccountWalletsTransactionsService.recordHistory(transactionsDO);
                    break;
                case 1:
                    BigDecimal addPointed = pointsBalance.add(amount);
                    updateWrapper.eq(ChargeCustomerAccountWalletsDO::getId, walletId)
                            .set(ChargeCustomerAccountWalletsDO::getPointsBalance, addPointed);
                    updated = chargeCustomerAccountWalletsService.update(updateWrapper);

                    ChargeCustomerAccountWalletsTransactionsDO transactionsPointDO = ChargeCustomerAccountWalletsTransactionsDO.builder()
                            .accountId(walletsDO.getAccountId())
                            .walletsId(walletsDO.getId())
                            .walletsEven(WalletsEventEnum.AGENT_RECHARGE.getCode())
                            .customerId(customerId)
                            .cashAmount(BigDecimal.ZERO)// 积分扣款不涉及现金
                            .balanceCash(walletsDO.getCashBalance())
                            .pointsAmount(amount)
                            .balancePoints(addPointed)
                            .transactionTime(transactionTime)
                            .fromCustomerId(fromCustomerId)
                            .tenantId(walletsDO.getTenantId())
                            .build();
                    recorded = chargeCustomerAccountWalletsTransactionsService.recordHistory(transactionsPointDO);
                    break;
            }

        } catch (Exception e) {
            log.error("代充值失败; " + e.getMessage(), e);
            throw new ServiceException(500, "代充值失败;");
        }
        if (!updated || !recorded) {
            throw new ServiceException(500, "更新钱包失败;");
        }
        return true;
    }

    /**
     * 钱包划转
     *
     * @param toWalletsId     划入的钱包id
     * @param amount          划转量
     * @param walletType      划转类型 0-现金，1-积分
     * @param customerId      客户id
     * @param fromWalletsId   来源钱包id
     * @param transactionTime 交易时间
     * @return
     */
    @Override
    @Transactional
    public boolean transferChargeOperation(Long fromWalletsId, Long toWalletsId, BigDecimal amount, Integer walletType, Long customerId, Long transactionTime) {
        boolean updated = false;
        boolean updated2 = false;
        boolean recorded = false;
        boolean recorded2 = false;
        try {
            List<Long> walletIds = List.of(fromWalletsId, toWalletsId);

            LambdaUpdateWrapper<ChargeCustomerAccountWalletsDO> updateWrapper = lambdaUpdate();
            LambdaQueryWrapper<ChargeCustomerAccountWalletsDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(ChargeCustomerAccountWalletsDO::getId, walletIds);
            List<ChargeCustomerAccountWalletsDO> walletsDOs = chargeCustomerAccountWalletsService.list(queryWrapper);
            BigDecimal fromWalletCashBalance = BigDecimal.ZERO;
            BigDecimal fromWalletPointBalance = BigDecimal.ZERO;
            Long fromAccountId = null;
            BigDecimal toWalletCashBalance = BigDecimal.ZERO;
            BigDecimal toWalletPointBalance = BigDecimal.ZERO;
            Long toAccountId = null;
            for (ChargeCustomerAccountWalletsDO walletsDO : walletsDOs) {
                Long id = walletsDO.getId();
                if (id.equals(fromWalletsId)) {
                    fromWalletCashBalance = walletsDO.getCashBalance();
                    fromWalletPointBalance = walletsDO.getPointsBalance();
                    fromAccountId = walletsDO.getAccountId();
                } else {
                    toWalletCashBalance = walletsDO.getCashBalance();
                    toWalletPointBalance = walletsDO.getPointsBalance();
                    toAccountId = walletsDO.getAccountId();
                }
            }

            switch (walletType) {
                case 0:
                    // 现金 先处理划转入的账户 即 toWallet
                    BigDecimal added = toWalletCashBalance.add(amount);
                    updateWrapper.eq(ChargeCustomerAccountWalletsDO::getId, toWalletsId)
                            .set(ChargeCustomerAccountWalletsDO::getCashBalance, added);
                    updated = chargeCustomerAccountWalletsService.update(updateWrapper);

                    ChargeCustomerAccountWalletsTransactionsDO transactionsDO = ChargeCustomerAccountWalletsTransactionsDO.builder()
                            .accountId(toAccountId)
                            .walletsId(toWalletsId)
                            .walletsEven(WalletsEventEnum.TRANSFER_IN.getCode())
                            .customerId(customerId)
                            .cashAmount(amount)
                            .balanceCash(added)
                            .pointsAmount(BigDecimal.ZERO) // 现金扣款不涉及积分
                            .balancePoints(toWalletPointBalance) // 积分余额不变
                            .transactionTime(transactionTime)
                            .fromWalletsId(fromWalletsId)
                            .build();
                    recorded = chargeCustomerAccountWalletsTransactionsService.recordHistory(transactionsDO);

                    // 现金 处理划转出的钱包 即 fromWallet
                    updateWrapper.clear();
                    BigDecimal cashSub = fromWalletCashBalance.subtract(amount);
                    updateWrapper.eq(ChargeCustomerAccountWalletsDO::getId, fromWalletsId)
                            .set(ChargeCustomerAccountWalletsDO::getCashBalance, cashSub);
                    updated2 = chargeCustomerAccountWalletsService.update(updateWrapper);

                    ChargeCustomerAccountWalletsTransactionsDO fromTransDO = ChargeCustomerAccountWalletsTransactionsDO.builder()
                            .accountId(fromAccountId)
                            .walletsId(fromWalletsId)
                            .walletsEven(WalletsEventEnum.TRANSFER_OUT.getCode())
                            .customerId(customerId)
                            .cashAmount(amount)
                            .balanceCash(cashSub)
                            .pointsAmount(BigDecimal.ZERO) // 现金扣款不涉及积分
                            .balancePoints(fromWalletPointBalance) // 积分余额不变
                            .transactionTime(transactionTime)
                            .toWalletsId(toWalletsId)
                            .build();
                    recorded2 = chargeCustomerAccountWalletsTransactionsService.recordHistory(fromTransDO);
                    break;

                case 1:
                    BigDecimal addPointed = toWalletPointBalance.add(amount);
                    updateWrapper.eq(ChargeCustomerAccountWalletsDO::getId, toWalletsId)
                            .set(ChargeCustomerAccountWalletsDO::getPointsBalance, addPointed);
                    updated = chargeCustomerAccountWalletsService.update(updateWrapper);

                    ChargeCustomerAccountWalletsTransactionsDO transactionsPointDO = ChargeCustomerAccountWalletsTransactionsDO.builder()
                            .accountId(toAccountId)
                            .walletsId(toWalletsId)
                            .walletsEven(WalletsEventEnum.TRANSFER_IN.getCode())
                            .customerId(customerId)
                            .cashAmount(BigDecimal.ZERO)// 积分扣款不涉及现金
                            .balanceCash(toWalletCashBalance)
                            .pointsAmount(amount)
                            .balancePoints(addPointed)
                            .transactionTime(transactionTime)
                            .fromWalletsId(fromWalletsId)
                            .build();
                    recorded = chargeCustomerAccountWalletsTransactionsService.recordHistory(transactionsPointDO);

                    updateWrapper.clear();
                    BigDecimal pointSub = fromWalletPointBalance.subtract(amount);
                    updateWrapper.eq(ChargeCustomerAccountWalletsDO::getId, fromWalletsId)
                            .set(ChargeCustomerAccountWalletsDO::getPointsBalance, pointSub);
                    updated2 = chargeCustomerAccountWalletsService.update(updateWrapper);

                    ChargeCustomerAccountWalletsTransactionsDO transPointDO = ChargeCustomerAccountWalletsTransactionsDO.builder()
                            .accountId(fromAccountId)
                            .walletsId(fromWalletsId)
                            .walletsEven(WalletsEventEnum.TRANSFER_OUT.getCode())
                            .customerId(customerId)
                            .cashAmount(BigDecimal.ZERO)// 积分扣款不涉及现金
                            .balanceCash(fromWalletCashBalance)
                            .pointsAmount(amount)
                            .balancePoints(pointSub)
                            .transactionTime(transactionTime)
                            .toWalletsId(toWalletsId)
                            .build();
                    recorded2 = chargeCustomerAccountWalletsTransactionsService.recordHistory(transPointDO);

                    break;
            }

        } catch (Exception e) {
            log.error("划转异常: " + e.getMessage(), e);
            throw new ServiceException(500, "划转失败;");
        }

        if (!updated || !recorded || !updated2 || !recorded2) {
            throw new ServiceException(500, "更新钱包失败;");
        }
        return true;
    }

    /**
     * 钱包划转-出
     *
     * @param walletId        划转出的钱包id
     * @param amount          划转量
     * @param walletType      划转类型 0-现金，1-积分
     * @param customerId      客户id
     * @param toWalletsId     目标钱包id
     * @param transactionTime 交易时间
     * @return
     */
    @Override
    @Transactional
    public boolean transferTo(Long walletId, BigDecimal amount, Integer walletType, Long customerId, Long toWalletsId, Long transactionTime) {
        boolean updated = false;
        boolean recorded = false;
        try {
            LambdaUpdateWrapper<ChargeCustomerAccountWalletsDO> updateWrapper = lambdaUpdate();

            ChargeCustomerAccountWalletsDO walletsDO = chargeCustomerAccountWalletsService.getById(walletId);
            BigDecimal pointsBalance = walletsDO.getPointsBalance();
            BigDecimal cashBalance = walletsDO.getCashBalance();

            switch (walletType) {
                case 0:
                    BigDecimal cashSub = cashBalance.subtract(amount);
                    updateWrapper.eq(ChargeCustomerAccountWalletsDO::getId, walletId)
                            .set(ChargeCustomerAccountWalletsDO::getCashBalance, cashSub);
                    updated = chargeCustomerAccountWalletsService.update(updateWrapper);

                    ChargeCustomerAccountWalletsTransactionsDO transactionsDO = ChargeCustomerAccountWalletsTransactionsDO.builder()
                            .accountId(walletsDO.getAccountId())
                            .walletsId(walletsDO.getId())
                            .walletsEven(WalletsEventEnum.TRANSFER_OUT.getCode())
                            .customerId(customerId)
                            .cashAmount(amount)
                            .balanceCash(cashSub)
                            .pointsAmount(BigDecimal.ZERO) // 现金扣款不涉及积分
                            .balancePoints(walletsDO.getPointsBalance()) // 积分余额不变
                            .transactionTime(transactionTime)
                            .toWalletsId(toWalletsId)
                            .tenantId(walletsDO.getTenantId())
                            .build();
                    recorded = chargeCustomerAccountWalletsTransactionsService.recordHistory(transactionsDO);
                    break;
                case 1:
                    BigDecimal pointSub = pointsBalance.subtract(amount);
                    updateWrapper.eq(ChargeCustomerAccountWalletsDO::getId, walletId)
                            .set(ChargeCustomerAccountWalletsDO::getPointsBalance, pointSub);
                    updated = chargeCustomerAccountWalletsService.update(updateWrapper);

                    ChargeCustomerAccountWalletsTransactionsDO transactionsPointDO = ChargeCustomerAccountWalletsTransactionsDO.builder()
                            .accountId(walletsDO.getAccountId())
                            .walletsId(walletsDO.getId())
                            .walletsEven(WalletsEventEnum.TRANSFER_OUT.getCode())
                            .customerId(customerId)
                            .cashAmount(BigDecimal.ZERO)// 积分扣款不涉及现金
                            .balanceCash(walletsDO.getCashBalance())
                            .pointsAmount(amount)
                            .balancePoints(pointSub)
                            .transactionTime(transactionTime)
                            .toWalletsId(toWalletsId)
                            .tenantId(walletsDO.getTenantId())
                            .build();
                    recorded = chargeCustomerAccountWalletsTransactionsService.recordHistory(transactionsPointDO);
                    break;
            }

        } catch (Exception e) {
            log.error("划转出异常: " + e.getMessage(), e);
        }
        if (!updated || !recorded) {
            throw new ServiceException(500, "更新钱包失败;");
        }
        return true;
    }

    /**
     * 钱包划转-入
     *
     * @param walletId        划算入的钱包id
     * @param amount          划转量
     * @param walletType      划转类型 0-现金，1-积分
     * @param customerId      客户id
     * @param fromWalletsId   来源钱包id
     * @param transactionTime 交易时间
     * @return
     */
    @Override
    @Transactional
    public boolean transferFrom(Long walletId, BigDecimal amount, Integer walletType, Long customerId, Long fromWalletsId, Long transactionTime) {
        boolean updated = false;
        boolean recorded = false;
        try {
            LambdaUpdateWrapper<ChargeCustomerAccountWalletsDO> updateWrapper = lambdaUpdate();

            ChargeCustomerAccountWalletsDO walletsDO = chargeCustomerAccountWalletsService.getById(walletId);
            BigDecimal pointsBalance = walletsDO.getPointsBalance();
            BigDecimal cashBalance = walletsDO.getCashBalance();

            switch (walletType) {
                case 0:
                    BigDecimal added = cashBalance.add(amount);
                    updateWrapper.eq(ChargeCustomerAccountWalletsDO::getId, walletId)
                            .set(ChargeCustomerAccountWalletsDO::getCashBalance, added);
                    updated = chargeCustomerAccountWalletsService.update(updateWrapper);

                    ChargeCustomerAccountWalletsTransactionsDO transactionsDO = ChargeCustomerAccountWalletsTransactionsDO.builder()
                            .accountId(walletsDO.getAccountId())
                            .walletsId(walletsDO.getId())
                            .walletsEven(WalletsEventEnum.TRANSFER_IN.getCode())
                            .customerId(customerId)
                            .cashAmount(amount)
                            .balanceCash(added)
                            .pointsAmount(BigDecimal.ZERO) // 现金扣款不涉及积分
                            .balancePoints(walletsDO.getPointsBalance()) // 积分余额不变
                            .transactionTime(transactionTime)
                            .fromWalletsId(fromWalletsId)
                            .tenantId(walletsDO.getTenantId())
                            .build();
                    recorded = chargeCustomerAccountWalletsTransactionsService.recordHistory(transactionsDO);
                    break;
                case 1:
                    BigDecimal addPointed = pointsBalance.add(amount);
                    updateWrapper.eq(ChargeCustomerAccountWalletsDO::getId, walletId)
                            .set(ChargeCustomerAccountWalletsDO::getPointsBalance, addPointed);
                    updated = chargeCustomerAccountWalletsService.update(updateWrapper);

                    ChargeCustomerAccountWalletsTransactionsDO transactionsPointDO = ChargeCustomerAccountWalletsTransactionsDO.builder()
                            .accountId(walletsDO.getAccountId())
                            .walletsId(walletsDO.getId())
                            .walletsEven(WalletsEventEnum.TRANSFER_IN.getCode())
                            .customerId(customerId)
                            .cashAmount(BigDecimal.ZERO)// 积分扣款不涉及现金
                            .balanceCash(walletsDO.getCashBalance())
                            .pointsAmount(amount)
                            .balancePoints(addPointed)
                            .transactionTime(transactionTime)
                            .fromWalletsId(fromWalletsId)
                            .tenantId(walletsDO.getTenantId())
                            .build();
                    recorded = chargeCustomerAccountWalletsTransactionsService.recordHistory(transactionsPointDO);
                    break;
            }

        } catch (Exception e) {
            log.error("划转入异常: " + e.getMessage(), e);
        }
        if (!updated || !recorded) {
            throw new ServiceException(500, "更新钱包失败;");
        }
        return true;
    }


    /**
     * 更新账单状态
     * 对预付费或者后付费账单做金额记录和状态修改
     *
     * @param paymentType     支付类型，0：预付费，1后付费
     * @param billId          账单id
     * @param businessTimeDTO 分片规则
     */
    @Transactional
    public boolean updateAccountBalanceBelowStatus(Integer paymentType, Long billId, Integer billStatus, Integer walletsStatus,
                                                   HitBusinessTimeDTO businessTimeDTO, JSONObject billJSON) throws Exception {
        return switch (paymentType) {
            case 0 ->
                    prepaidIncomeBillDetailService.updateBillPaidStatus(billId, billStatus, walletsStatus, businessTimeDTO, billJSON);
            case 1 -> postpaidProductIncomeBillService.updateBillStatus(billId, billStatus, walletsStatus, billJSON);
            case 2 -> makeupBillService.updateBillStatus(billId, billStatus, walletsStatus, billJSON);
            default -> false;
        };
    }

    /**
     * 更新账单状态 - 余额不足（钱包已经是负数， 不等同未结清）
     *
     * @param paymentType
     * @param billId
     * @param billStatus
     * @param businessTimeDTO
     * @param billJSON
     * @return
     * @throws Exception
     */
    public boolean updateBillNotSufficientFundsStatus(Integer paymentType, Long billId, Integer billStatus,
                                                      HitBusinessTimeDTO businessTimeDTO, JSONObject billJSON) throws Exception {
        return switch (paymentType) {
            case 0 ->
                    prepaidIncomeBillDetailService.updateBillNotSufficientFundsStatus(billId, billStatus, businessTimeDTO, billJSON);
            case 1 -> postpaidProductIncomeBillService.updateBillNotSufficientFundsStatus(billId, billStatus, billJSON);
            case 2 -> makeupBillService.updateBillNotSufficientFundsStatus(billId, billStatus, billJSON);
            default -> false;
        };
    }

    /**
     * 校验账单幂等性 已支付不做处理
     *
     * @param paymentType
     * @param billId
     * @param businessTimeDTO
     * @return
     * @throws Exception
     */
    private JSONObject checkBillStatus(Integer paymentType, Long billId, HitBusinessTimeDTO businessTimeDTO) throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("isNull", false);
        switch (paymentType) {
            case 0:
                // 处理预付费账单 查看账单状态是否是待处理 即 1
                PrepaidIncomeBillDetailDO prepaidBill = prepaidIncomeBillDetailService.getPrepaidBillByID(billId, businessTimeDTO);
                if (ObjectUtil.isNotNull(prepaidBill)) {
                    boolean checkBillStatus = InvoiceEnum.BillStatus.WAIT_PAYMENT.getCode() == prepaidBill.getBillStatus();
                    jsonObject.put("checkBillStatus", checkBillStatus);
                    jsonObject.put("pointAmount", prepaidBill.getPointAmount());
                    jsonObject.put("cashAmount", prepaidBill.getCashAmount());
//                    jsonObject.put("callbackUrl", prepaidBill.getCallbackUrl());
                } else {
                    jsonObject.put("isNull", true);
                }
                break;
            case 1:
                // 处理后付费账单 查看账单状态是否是待处理 即 1
                PostpaidProductIncomeBillDO postpaidProductIncomeBillDO = postpaidProductIncomeBillService.getBillByID(billId);
                if (ObjectUtil.isNotNull(postpaidProductIncomeBillDO)) {
                    boolean checkBillStatus = InvoiceEnum.BillStatus.WAIT_PAYMENT.getCode() == postpaidProductIncomeBillDO.getBillStatus();
                    jsonObject.put("checkBillStatus", checkBillStatus);
                    // 通过 paymentType 区别积分还是现金, 但值字段一样
                    jsonObject.put("pointAmount", postpaidProductIncomeBillDO.getAmountWithTax());
                    jsonObject.put("cashAmount", postpaidProductIncomeBillDO.getAmountWithTax());
//                    jsonObject.put("callbackUrl", postpaidProductIncomeBillDO.getCallbackUrl());
                } else {
                    jsonObject.put("isNull", true);
                }

                break;
            case 2:
                // 手工账单 只有现金的情况
                MakeupIncomeBillDO makeupIncomeBillDO = makeupBillMapper.selectById(billId);
                if (ObjectUtil.isNotNull(makeupIncomeBillDO)) {
                    boolean checkBillStatus = InvoiceEnum.BillStatus.WAIT_PAYMENT.getCode() == makeupIncomeBillDO.getBillStatus();
                    jsonObject.put("checkBillStatus", checkBillStatus);
                    jsonObject.put("cashAmount", makeupIncomeBillDO.getAmountWithTax());
//                    jsonObject.put("callbackUrl", makeupIncomeBillDO.getCallbackUrl());
                } else {
                    jsonObject.put("isNull", true);
                }
                break;
            default:
                jsonObject.put("isNull", true);
                break;
        }
        return jsonObject;
    }

    /**
     * 推送到延迟队列 做消息重放
     *
     * @param walletDeductionMqDTO
     */
    public void sendReplayOrder(WalletDeductionMqDTO walletDeductionMqDTO) {
        String topic = ChargeTopicConstant.CHARGE_DEDUCTION_REPLAY_TOPIC;
        String tag = ChargeTopicConstant.TAG_WALLET_DEDUCTION_NOTICE;
        String destination = topic + ":" + tag;
        Message<WalletDeductionMqDTO> message = MessageBuilder.withPayload(walletDeductionMqDTO)
                .build();
        // 1小时后重播
        SendResult sendResult = rocketMQTemplate.syncSend(destination, message, 2000, 17);
//        SendResult sendResult = rocketMQTemplate.syncSend(destination, message, 2000, 3);
        SendStatus sendStatus = sendResult.getSendStatus();
        if (SendStatus.SEND_OK.equals(sendStatus)) {
            log.info("[支付重放推送] 推送消息成功：{}", walletDeductionMqDTO);
        } else {
            log.error("[支付重放推送] 推送消息失败：{}", walletDeductionMqDTO);
            // 开始存到本地 txt 文件后续解析处理
        }
    }

    @Override
    public void writeToLocalFile(String message) {
        // 保存到本地文件txt
        String date = java.time.LocalDate.now().toString().replace("-", "");
        String fileName = "dlq_error_backup_" + date + ".txt";

        try (java.io.FileWriter fw = new java.io.FileWriter(fileName, true);
             java.io.BufferedWriter bw = new java.io.BufferedWriter(fw)) {
            bw.write(message);
            bw.newLine();
        } catch (Exception e) {
            log.error("[DLQ队列记录落盘]异常: {}", message, e);
        }
    }

}
