package com.linkcircle.boss.module.charge.fee.web.payment.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 账单对应业务接口回调
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ThirdPartyNotifyDTO {

    @Schema(description = "账单id")
    private Long billId;

    @Schema(description = "账单类型")
    private Integer billType;

    @Schema(description = "账单状态")
    private Integer billStatus;

    @Schema(description = "钱包类型")
    private BigDecimal cashBalance;

    @Schema(description = "积分余额")
    private BigDecimal pointBalance;

    @Schema(description = "交易时间")
    private Long transactionTime;

    @Schema(description = "钱包交易id")
    private Long transactionId;

}
