package com.linkcircle.boss.module.charge.crm.web.subscribe.api;

import com.linkcircle.boss.framework.common.enums.RpcConstants;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.charge.crm.web.subscribe.service.ChargeSubscriptionsService;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.ChargeSubscriptionsApi;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon;
import com.linkcircle.boss.module.crm.api.productservice.vo.ServiceCodeInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/26 13:46
 */
@RestController
@RequestMapping(RpcConstants.RPC_API_PREFIX + "/subscriptions")
@RequiredArgsConstructor
public class ChargeSubscriptionsApiController implements ChargeSubscriptionsApi {

    private final ChargeSubscriptionsService chargeSubscriptionsService;

    @Override
    public CommonResult<List<AccountSubscriptionsVO>> getAccountSubscriptionsList(Long accountId,
                                                                                  @RequestParam(required = false) Integer paymentType) {
        return CommonResult.success(chargeSubscriptionsService.getAccountSubscriptionsList(accountId, paymentType));
    }

    @Override
    public CommonResult<AccountSubscriptionsVO> getSubscriptionDetail(Long subscriptionId) {
        return CommonResult.success(chargeSubscriptionsService.getSubscriptionDetail(subscriptionId));
    }

    @Override
    public CommonResult<List<Long>> getAllSubscriptionAccountIds(Integer paymentType) {
        return CommonResult.success(chargeSubscriptionsService.getAllSubscriptionAccountIds(paymentType));
    }

    @Override
    public CommonResult<List<AccountSubscriptionsVO>> getSubscriptionsListByBillingType(Integer billingType) {
        return CommonResult.success(chargeSubscriptionsService.getSubscriptionsListByBillingType(billingType));
    }

    @Override
    public CommonResult<Boolean> changeSubscriptionStatus(Long id, Integer status) {
        return CommonResult.success(chargeSubscriptionsService.changeStatus(id, status));
    }

    @Override
    public CommonResult<List<Coupon>> getSubscriptionCouponList(Long subscriptionId, Long serviceId) {
        return CommonResult.success(chargeSubscriptionsService.getSubscriptionCouponList(subscriptionId, serviceId));
    }

    @Override
    public CommonResult<List<ServiceCodeInfo>> getAllActiveServiceCodes() {
        return null;
    }
}
