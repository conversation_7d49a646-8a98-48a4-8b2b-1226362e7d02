<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linkcircle.boss.module.billing.web.stats.mapper.CustomerDailyUsageBillMapper">

    <!-- 查询预付费收入账单表中的所有serviceCode -->
    <select id="queryServiceCodesFromPrepaidIncome" resultType="java.lang.String">
        SELECT DISTINCT service_code
        FROM prepaid_income_bill_detail
        WHERE deleted = false
          AND business_time >= #{startTime}
          AND business_time &lt; #{endTime}
          AND service_code IS NOT NULL
          AND service_code != ''
    </select>

    <!-- 查询后付费收入账单表中的所有serviceCode -->
    <select id="queryServiceCodesFromPostpaidIncome" resultType="java.lang.String">
        SELECT DISTINCT service_code
        FROM postpaid_income_bill_detail
        WHERE deleted = false
          AND business_time >= #{startTime}
          AND business_time &lt; #{endTime}
          AND service_code IS NOT NULL
          AND service_code != ''
    </select>

    <!-- 统计预付费收入账单使用量数据 -->
    <select id="statsPrepaidIncomeUsage" resultType="com.linkcircle.boss.module.billing.api.stats.customer.model.entity.CustomerDailyUsageBillDO">
        SELECT
            DATE_FORMAT(FROM_UNIXTIME(business_time / 1000), '%Y%m%d%H') AS billDate,
            timezone,
            CAST(customer_id AS CHAR) AS customerId,
            CAST(account_id AS CHAR) AS accountId,
            CAST(product_id AS CHAR) AS productId,
            CAST(service_id AS CHAR) AS serviceId,
            SUM(COALESCE(usage_count, 0)) AS usage,
            usage_unit AS usageUnit,
            SUM(COALESCE(cash_amount, 0)) AS cashAmount,
            currency AS cashAmountCurrency,
            SUM(COALESCE(point_amount, 0)) AS pointAmount
        FROM prepaid_income_bill_detail
        WHERE deleted = false
          AND service_code = #{serviceCode}
          AND business_time >= #{startTime}
          AND business_time &lt; #{endTime}
        GROUP BY
            DATE_FORMAT(FROM_UNIXTIME(business_time / 1000), '%Y%m%d%H'),
            timezone,
            customer_id,
            account_id,
            product_id,
            service_id,
            usage_unit,
            currency
        HAVING SUM(COALESCE(usage_count, 0)) > 0
    </select>

    <!-- 统计后付费收入账单使用量数据 -->
    <select id="statsPostpaidIncomeUsage" resultType="com.linkcircle.boss.module.billing.api.stats.customer.model.entity.CustomerDailyUsageBillDO">
        SELECT
            DATE_FORMAT(FROM_UNIXTIME(business_time / 1000), '%Y%m%d%H') AS billDate,
            timezone,
            CAST(customer_id AS CHAR) AS customerId,
            CAST(account_id AS CHAR) AS accountId,
            CAST(product_id AS CHAR) AS productId,
            CAST(service_id AS CHAR) AS serviceId,
            SUM(COALESCE(usage_count, 0)) AS usage,
            usage_unit AS usageUnit,
            SUM(COALESCE(cash_amount, 0)) AS cashAmount,
            currency AS cashAmountCurrency,
            SUM(COALESCE(point_amount, 0)) AS pointAmount
        FROM postpaid_income_bill_detail
        WHERE deleted = false
          AND service_code = #{serviceCode}
          AND business_time >= #{startTime}
          AND business_time &lt; #{endTime}
        GROUP BY
            DATE_FORMAT(FROM_UNIXTIME(business_time / 1000), '%Y%m%d%H'),
            timezone,
            customer_id,
            account_id,
            product_id,
            service_id,
            usage_unit,
            currency
        HAVING SUM(COALESCE(usage_count, 0)) > 0
    </select>

</mapper>
