package com.linkcircle.boss.module.charge.fee.web.invoiceRecord.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> zyuan
 * @data : 2025-08-01
 */
public class Test {
    public static List<Map<String, Object>> generateSampleData() {
        List<Map<String, Object>> dataList = new ArrayList<>();

        // 一级数据 - 按量计费
        Map<String, Object> level1Item1 = createItem(
                "按量计费", "", "", "", "", "", "CNY",
                createChildren(
                        // 二级数据 - 按量计费服务
                        createItem(
                                "按量计费服务(15个/5周)", "180", "15", "2700", "9.000000%", "2943.000000", "CNY",
                                createChildren(
                                        // 三级数据 - 服务明细
                                        createItem("服务明细-技术咨询", "120", "10", "1200", "6.000000%", "1272.000000", "CNY", null),
                                        createItem("服务明细-系统维护", "60", "5", "300", "3.000000%", "309.000000", "CNY", null)
                                )
                        )
                )
        );
        dataList.add(level1Item1);

        // 一级数据 - 包年包月
        Map<String, Object> level1Item2 = createItem(
                "包年包月", "", "", "", "", "", "USD",
                createChildren(
                        // 二级数据 - 基础套餐
                        createItem(
                                "基础套餐(12个月)", "200", "12", "2400", "0.000000%", "2400.000000", "USD",
                                createChildren(
                                        // 三级数据 - 套餐内容
                                        createItem("基础计算资源", "100", "12", "1200", "0.000000%", "1200.000000", "USD", null),
                                        createItem("基础存储资源", "80", "12", "960", "0.000000%", "960.000000", "USD", null),
                                        createItem("技术支持服务", "20", "12", "240", "0.000000%", "240.000000", "USD", null)
                                )
                        ),
                        // 二级数据 - 高级套餐
                        createItem(
                                "高级套餐(6个月)", "350", "6", "2100", "5.000000%", "2205.000000", "USD",
                                null
                        )
                )
        );
        dataList.add(level1Item2);

        return dataList;
    }

    private static Map<String, Object> createItem(
            String description, String discountUnitPrice, String usageCount,
            String discountPrice, String taxRate, String totalAmount,
            String currency, List<Map<String, Object>> children) {

        Map<String, Object> item = new HashMap<>();
        item.put("description", description);
        item.put("discountUnitPrice", discountUnitPrice);
        item.put("usageCount", usageCount);
        item.put("discountPrice", discountPrice);
        item.put("taxRate", taxRate);
        item.put("totalAmount", totalAmount);
        item.put("currency", currency);
        item.put("children", children != null ? children : new ArrayList<>());
        return item;
    }

    private static List<Map<String, Object>> createChildren(Map<String, Object>... items) {
        List<Map<String, Object>> children = new ArrayList<>();
        for (Map<String, Object> item : items) {
            children.add(item);
        }
        return children;
    }

    public static void main(String[] args) {
        List<Map<String, Object>> virtualData = generateSampleData();
        System.out.println(virtualData);
    }
}
