package com.linkcircle.boss.module.report.web.financial.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.common.util.topic.ChargeTopicUtils;
import com.linkcircle.boss.framework.tanant.TenantUtils;
import com.linkcircle.boss.module.report.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.report.enums.DownLoadEnum;
import com.linkcircle.boss.module.report.enums.FinancialEnum;
import com.linkcircle.boss.module.report.web.download.model.dto.DownloadTaskDTO;
import com.linkcircle.boss.module.report.web.download.service.DownloadTaskService;
import com.linkcircle.boss.module.report.web.dynamic.model.dto.FieldMetaDTO;
import com.linkcircle.boss.module.report.web.dynamic.model.dto.TableMetaDTO;
import com.linkcircle.boss.module.report.web.dynamic.model.vo.ShowVo;
import com.linkcircle.boss.module.report.web.dynamic.model.vo.TableMetaVO;
import com.linkcircle.boss.module.report.web.dynamic.service.DynamicJdbcTemplate;
import com.linkcircle.boss.module.report.web.financial.mapper.ChargeDetailTemplateMapper;
import com.linkcircle.boss.module.report.web.financial.mapper.FinancialDetailMapper;
import com.linkcircle.boss.module.report.web.financial.model.dto.*;
import com.linkcircle.boss.module.report.web.financial.model.entity.ChargeDetailTemplateDO;
import com.linkcircle.boss.module.report.web.financial.model.vo.ChargeDetailTemplateColumnVO;
import com.linkcircle.boss.module.report.web.financial.service.ChargeDetailService;
import com.linkcircle.boss.module.report.web.financial.service.FunctionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/20 11:28
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChargeDetailServiceImpl implements ChargeDetailService {


    private final ChargeDetailTemplateMapper chargeDetailTemplateMapper;

    private final FinancialDetailMapper financialDetailMapper;


    private final DownloadTaskService downloadTaskService;

    private final DynamicJdbcTemplate dynamicJdbcTemplate;
    private final RocketMQTemplate rocketMQTemplate;
    /**
     * 分页查询收费明细
     *
     * @param detailReqDTO 明细请求对象，包含查询条件，如模板ID、开始时间、结束时间等
     * @return 分页查询结果，包含查询到的收费明细数据
     * @throws ServiceException 如果查询过程中出现异常，如模板不存在、模板字段名为空、找不到对应的财务表、找不到对应的财务表字段等，会抛出该异常
     */
    @Override
    public PageResult<Map<String, Object>> pageQuery(ChargeDynamicReqDTO detailReqDTO, boolean isExport) {
        // 获取模板ID
        Long templateId = detailReqDTO.getTemplateId();
        // 根据模板ID查询模板对象
        ChargeDetailTemplateDO templateDO = isExport? TenantUtils.executeIgnore(()->chargeDetailTemplateMapper.selectById(templateId)) :chargeDetailTemplateMapper.selectById(templateId);
        // 如果模板对象为空，则抛出异常
        if (templateDO == null) {
            log.error("模板不存在,templateId:{}", templateId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_NOT_EXISTS);
        }

        // 需要查询的字段名称和顺序
        String exportColumns = templateDO.getExportColumns();
        // 如果模板字段名为空，则抛出异常
        if (StringUtils.isBlank(exportColumns)) {
            log.error("模板字段名为空,templateId:{}", templateId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_COLUMN_NAME_IS_BLANK);
        }
        log.info("模板字段名:{}", exportColumns);
        List<ChargeDetailTemplateColumnVO> exportList = JSONUtil.toList(exportColumns, ChargeDetailTemplateColumnVO.class);
        String logicName = templateDO.getLogicName();
        String tableName = templateDO.getTableName();
        List<ChargeDynamicColumnDTO> conditions = detailReqDTO.getConditions();

        //过滤无效关联条件
        if (CollectionUtils.isNotEmpty(exportList)) {
            exportList = exportList.stream()
                    .peek(t ->{
                        if(t.getAssociation()!= null && StringUtils.isBlank(t.getAssociation().getColumnName())){
                            t.setAssociation(null);
                        }
                    })
                    .toList();
        }
        return dynamicJdbcTemplate.queryPage(logicName, tableName, exportList, conditions, detailReqDTO);
    }


    @Override
    public void submitTask(ChargeDynamicReqDTO detailReqDTO) {
        Long templateId = detailReqDTO.getTemplateId();
        ChargeDetailTemplateDO templateDO = chargeDetailTemplateMapper.selectById(templateId);
        if (templateDO == null) {
            log.error("模板不存在,templateId:{}", templateId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_NOT_EXISTS);
        }
        String tableName = templateDO.getTableName();
        List<TableMetaVO> tables = dynamicJdbcTemplate.table(templateDO.getLogicName());
        if (CollectionUtils.isEmpty(tables)) {
            log.error("找不到对应的表,tableName:{}", tableName);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TABLE_NOT_EXISTS);
        }
        TableMetaVO table = null;
        for (TableMetaVO tableMetaVO : tables) {
            if (tableMetaVO.getTableName().equals(tableName)) {
                table = tableMetaVO;
                break;
            }
        }
        if (table == null) {
            log.error("找不到对应的表,tableName:{}", tableName);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TABLE_NOT_EXISTS);
        }
        String taskParams = JSONUtil.toJsonStr(detailReqDTO);
        DownloadTaskDTO taskDTO = DownloadTaskDTO.defaultTask(
                taskParams,
                tableName,1,
                DownLoadEnum.FileType.XLSX
        );
        boolean success = downloadTaskService.createDownloadTask(taskDTO);
        if (!success) {
            log.error("创建下载任务失败");
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_DETAIL_DOWN_TASK_FAILED);
        }
        rocketMQTemplate.convertAndSend(ChargeTopicUtils.getDownloadTaskFinanceDetailTopic(), taskDTO);
        log.info("创建下载任务成功");
    }

    @Override
    public List<ShowVo> commonQuery(ChargeColumnReqDTO detailReqDTO) {
        ChargeDetailTemplateDO templateDO = chargeDetailTemplateMapper.selectById(detailReqDTO.getTemplateId());
        String logicName = templateDO.getLogicName();
        String tableName = templateDO.getTableName();
        String exportColumns = templateDO.getExportColumns();
        List<ChargeDetailTemplateColumnVO> columnVOS = JSONUtil.toList(exportColumns, ChargeDetailTemplateColumnVO.class);
        ChargeDetailTemplateColumnVO columnVO = columnVOS.stream().filter(t -> t.getColumnName().equals(detailReqDTO.getColumnName())).findFirst().orElse(null);
        if (columnVO == null) {
            log.error("找不到对应的字段,columnName:{}", detailReqDTO.getColumnName());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_COLUMN_NOT_EXISTS);
        }
        ChargeDetailTemplateColumnVO associationColumnVO = null;
        if (StringUtils.isNotBlank(detailReqDTO.getAssociationName())) {
            associationColumnVO = columnVOS.stream().filter(t -> t.getColumnName().equals(detailReqDTO.getAssociationName())).findFirst().orElse(null);
            if (associationColumnVO == null) {
                log.error("找不到对应的字段,columnName:{}", detailReqDTO.getColumnName());
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_COLUMN_NOT_EXISTS);
            }
        }
        return dynamicJdbcTemplate.listValue(logicName, tableName, columnVO, associationColumnVO, columnVOS);
    }

    @Override
    public List<FieldMetaDTO> exportFields(ChargeDynamicReqDTO reqDTO) {
        // 获取模板ID
        Long templateId = reqDTO.getTemplateId();
        // 根据模板ID查询模板对象
        ChargeDetailTemplateDO templateDO = TenantUtils.executeIgnore(()->chargeDetailTemplateMapper.selectById(templateId));
        // 如果模板对象为空，则抛出异常
        if (templateDO == null) {
            log.error("模板不存在,templateId:{}", templateId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_NOT_EXISTS);
        }
        // 需要查询的字段名称和顺序
        String exportColumns = templateDO.getExportColumns();
        // 如果模板字段名为空，则抛出异常
        if (StringUtils.isBlank(exportColumns)) {
            log.error("模板字段名为空,templateId:{}", templateId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_COLUMN_NAME_IS_BLANK);
        }
        log.info("模板字段名:{}", exportColumns);

        TableMetaDTO table = dynamicJdbcTemplate.table(templateDO.getLogicName(),templateDO.getTableName());
        if (table == null) {
            log.error("找不到对应的表,tableName:{}", templateDO.getTableName());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TABLE_NOT_EXISTS);
        }
        Map<String, FieldMetaDTO> fieldMap = table.getColumns().stream().collect(Collectors.toMap(FieldMetaDTO::getColumnName, t -> t));
        List<ChargeDetailTemplateColumnVO> exportList = JSONUtil.toList(exportColumns, ChargeDetailTemplateColumnVO.class);
        return exportList.stream().map(t -> fieldMap.get(t.getColumnName())).toList();
    }

    @Override
    public List<ChargeDetailTemplateColumnVO> exportHeader(ChargeDynamicReqDTO reqDTO) {
        // 获取模板ID
        Long templateId = reqDTO.getTemplateId();
        // 根据模板ID查询模板对象
        ChargeDetailTemplateDO templateDO = TenantUtils.executeIgnore(()->chargeDetailTemplateMapper.selectById(templateId));
        // 如果模板对象为空，则抛出异常
        if (templateDO == null) {
            log.error("模板不存在,templateId:{}", templateId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FINANCE_TEMPLATE_NOT_EXISTS);
        }
        return JSONUtil.toList(templateDO.getExportColumns(), ChargeDetailTemplateColumnVO.class);
    }

    @Override
    public PageResult<Map<String, Object>> pageFinancialQuery(ChargeFinancialReqDTO req, boolean isExport) {
        return pageQuery(convertReqToColumn(req),isExport);
    }

    public ChargeDynamicReqDTO convertReqToColumn(ChargeFinancialReqDTO req) {
        ChargeDynamicReqDTO dynamicReqDTO = new ChargeDynamicReqDTO();
        dynamicReqDTO.setTemplateId(req.getTemplateId());
        dynamicReqDTO.setConditions(new ArrayList<>());

        Long customerId = req.getCustomerId();
        if (customerId != null) {
            dynamicReqDTO.getConditions().add(valueQuery(FinancialEnum.QueryCondition.CUSTOMER_ID.getCode(), customerId));
        }
        Long accountId = req.getAccountId();
        if (accountId != null) {
            dynamicReqDTO.getConditions().add(valueQuery(FinancialEnum.QueryCondition.ACCOUNT_ID.getCode(), accountId));
        }
        Long subscriptionId = req.getSubscribeId();
        if (subscriptionId != null) {
            dynamicReqDTO.getConditions().add(valueQuery(FinancialEnum.QueryCondition.SUBSCRIBE_ID.getCode(), subscriptionId));
        }
        Long billingStartTime = req.getBillingStartTime();
        Long billingEndTime = req.getBillingEndTime();
        if(billingStartTime != null || billingEndTime != null){
            if(billingStartTime == null){
                billingStartTime =0L;
            }
            if(billingEndTime == null){
                billingEndTime = System.currentTimeMillis();
            }
            dynamicReqDTO.getConditions().add(rangeQuery(FinancialEnum.QueryCondition.BILLING_TIME.getCode(), billingStartTime,billingEndTime));
        }
        return dynamicReqDTO;
    }

    public ChargeDynamicColumnDTO valueQuery(String columnName,Object value) {
        ChargeDynamicColumnDTO columnDTO = new ChargeDynamicColumnDTO();
        columnDTO.setColumnName(columnName);
        columnDTO.setValue(value);
        columnDTO.setRangeType(0);
        return columnDTO;
    }

    public ChargeDynamicColumnDTO rangeQuery(String columnName,Object minValue,Object maxValue) {
        ChargeDynamicColumnDTO columnDTO = new ChargeDynamicColumnDTO();
        columnDTO.setColumnName(columnName);
        columnDTO.setMaxValue(maxValue);
        columnDTO.setMinValue(minValue);
        columnDTO.setRangeType(1);
        return columnDTO;
    }

    @Override
    public void submitFinancialTask(ChargeFinancialReqDTO detailReqDTO) {
         submitTask(convertReqToColumn(detailReqDTO));
    }

}
