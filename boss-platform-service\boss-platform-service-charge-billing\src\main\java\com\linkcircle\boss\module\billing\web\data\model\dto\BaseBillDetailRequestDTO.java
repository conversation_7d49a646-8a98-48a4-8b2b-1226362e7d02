package com.linkcircle.boss.module.billing.web.data.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-06-16 17:06
 * @description 账单明细请求基类DTO
 */
@Data
@Schema(description = "账单明细请求基类DTO")
public abstract class BaseBillDetailRequestDTO {

    private Long tenantId;

    @Schema(description = "账户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "acc123456")
    @NotNull(message = "账户ID不能为空")
    @JsonProperty("account_id")
    private Long accountId;

    @Schema(description = "业务产生的时间戳(毫秒)", requiredMode = Schema.RequiredMode.REQUIRED, example = "*************")
    @NotNull(message = "业务时间戳不能为空")
    @JsonProperty("business_time")
    private Long businessTime;

    @Schema(description = "消耗量", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @NotNull(message = "消耗量不能为空")
    @JsonProperty("usage")
    private BigDecimal usage;

    @Schema(description = "消耗量单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "s")
    @NotBlank(message = "消耗量单位不能为空")
    @JsonProperty("usage_unit")
    private String usageUnit;

    @Schema(description = "扣费回调URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://example.com/callback")
    @NotBlank(message = "回调URL不能为空")
    @JsonProperty("callback_url")
    private String callbackUrl;

    @Schema(description = "业务话单数据", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "业务数据不能为空")
    @JsonProperty("data")
    private Map<String, Object> data;

    @Schema(description = "请求ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "req123456789")
    @NotBlank(message = "请求ID不能为空")
    @JsonProperty("request_id")
    private String requestId;

    @Schema(description = "业务唯一ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "biz987654")
    @NotBlank(message = "业务唯一ID不能为空")
    @JsonProperty("business_id")
    private String businessId;
}
