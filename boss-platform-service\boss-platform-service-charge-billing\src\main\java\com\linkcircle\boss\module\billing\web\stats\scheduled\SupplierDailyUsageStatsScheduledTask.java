package com.linkcircle.boss.module.billing.web.stats.scheduled;

import cn.hutool.core.util.IdUtil;
import com.linkcircle.boss.module.billing.web.stats.service.SupplierDailyUsageStatsService;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.github.kk01001.util.TraceIdUtil;
import io.github.kk01001.xxljob.annotations.XxlJobRegister;
import io.github.kk01001.xxljob.enums.ExecutorRouteStrategyEnum;
import io.github.kk01001.xxljob.enums.MisfireStrategyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-08-01 11:06
 * @description 供应商每日使用量统计定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SupplierDailyUsageStatsScheduledTask {

    private final SupplierDailyUsageStatsService supplierDailyUsageStatsService;

    /**
     * 供应商每日使用量统计定时任务
     * 每小时执行一次，统计前一小时的供应商使用量数据
     */
    @XxlJob("supplierDailyUsageStatsHandler")
    @XxlJobRegister(
            cron = "0 20 * * * ?",
            jobDesc = "供应商每日使用量统计定时任务",
            author = "admin",
            triggerStatus = 1,
            executorRouteStrategy = ExecutorRouteStrategyEnum.LEAST_FREQUENTLY_USED,
            misfireStrategy = MisfireStrategyEnum.DO_NOTHING
    )
    public void supplierDailyUsageStatsHandler() {
        String traceId = IdUtil.fastSimpleUUID();
        TraceIdUtil.buildAndSetTraceId(" ", "供应商每日使用量统计", traceId);

        try {
            log.info("开始执行供应商每日使用量统计定时任务");
            
            // 计算统计时间范围（前一小时）
            long currentTime = System.currentTimeMillis();
            long endTime = currentTime - (currentTime % (60 * 60 * 1000)); // 整点时间
            long startTime = endTime - (60 * 60 * 1000); // 前一小时

            log.info("统计时间范围: {} - {}", startTime, endTime);

            // 执行统计
            supplierDailyUsageStatsService.executeHourlyStats(startTime, endTime);

            log.info("供应商每日使用量统计定时任务执行完成");
        } catch (Exception e) {
            log.error("供应商每日使用量统计定时任务执行异常", e);
        } finally {
            TraceIdUtil.remove();
        }
    }
}
