package com.linkcircle.boss.module.billing.web.stats.listener;

import com.linkcircle.boss.framework.common.util.cache.UsageStatsCacheUtils;
import com.linkcircle.boss.module.billing.web.stats.event.SupplierUsageStatsEvent;
import io.github.kk01001.redis.RedissonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Duration;

/**
 * <AUTHOR>
 * @date 2025-01-03 15:00
 * @description 供应商使用量统计事件监听器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SupplierUsageStatsEventListener {

    private final RedissonUtil redissonUtil;

    /**
     * 监听供应商使用量统计事件
     *
     * @param event 供应商使用量统计事件
     */
    @Async
    @EventListener
    public void handleSupplierUsageStatsEvent(SupplierUsageStatsEvent event) {
        try {
            log.debug("处理供应商使用量统计事件: accountId={}, serviceId={}, billDate={}, supplierId={}",
                    event.getAccountId(), event.getServiceId(), event.getBillDate(), event.getSupplierId());

            // 构建缓存键
            String cacheKey = UsageStatsCacheUtils.buildSupplierUsageStatsKey(
                    event.getAccountId(), event.getServiceId(), event.getBillDate());

            // 使用 hincrby 原子性地递增统计数据
            boolean hasUpdates = false;

            if (event.getChargeUsageCount() != null && event.getChargeUsageCount().compareTo(BigDecimal.ZERO) > 0) {
                redissonUtil.hincrby(cacheKey, UsageStatsCacheUtils.FIELD_CHARGE_USAGE_COUNT,
                        event.getChargeUsageCount().longValue());
                hasUpdates = true;
            }

            if (event.getUsageCount() != null && event.getUsageCount().compareTo(BigDecimal.ZERO) > 0) {
                redissonUtil.hincrby(cacheKey, UsageStatsCacheUtils.FIELD_USAGE_COUNT,
                        event.getUsageCount().longValue());
                hasUpdates = true;
            }

            if (event.getCashAmount() != null && event.getCashAmount().compareTo(BigDecimal.ZERO) > 0) {
                redissonUtil.hincrby(cacheKey, UsageStatsCacheUtils.FIELD_CASH_AMOUNT,
                        event.getCashAmount().longValue());
                hasUpdates = true;
            }

            if (event.getPointAmount() != null && event.getPointAmount().compareTo(BigDecimal.ZERO) > 0) {
                redissonUtil.hincrby(cacheKey, UsageStatsCacheUtils.FIELD_POINT_AMOUNT,
                        event.getPointAmount().longValue());
                hasUpdates = true;
            }

            if (hasUpdates) {
                // 设置1天过期时间
                redissonUtil.expire(cacheKey, Duration.ofDays(1));

                log.debug("供应商使用量统计数据已更新到Redis: key={}", cacheKey);
            }

        } catch (Exception e) {
            log.error("处理供应商使用量统计事件失败: {}", event, e);
        }
    }


}
