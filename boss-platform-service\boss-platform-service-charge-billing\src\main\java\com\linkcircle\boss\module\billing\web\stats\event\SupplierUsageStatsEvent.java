package com.linkcircle.boss.module.billing.web.stats.event;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-01-03 15:00
 * @description 供应商使用量统计事件
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class SupplierUsageStatsEvent extends UsageStatsEvent {

    /**
     * 资源服务编码
     */
    private String resourceServiceCode;

    /**
     * 计费类型 (0-固定费率, 1-阶梯费率, 2-套餐计费, 3-按量计费)
     */
    private Integer chargeType;

    /**
     * 供应商ID
     */
    private Long supplierId;

    public SupplierUsageStatsEvent(Object source) {
        super(source);
    }

    @Override
    public String getEventType() {
        return "SUPPLIER_USAGE_STATS";
    }

    /**
     * 构建供应商使用量统计事件
     */
    @Builder
    public static SupplierUsageStatsEvent create(Object source, Long accountId, Long serviceId,
                                                 String billDate, String resourceServiceCode, Integer chargeType,
                                                 Long supplierId, BigDecimal chargeUsageCount, BigDecimal usageCount,
                                                 BigDecimal cashAmount, BigDecimal pointAmount) {
        SupplierUsageStatsEvent event = new SupplierUsageStatsEvent(source);
        event.setAccountId(accountId);
        event.setServiceId(serviceId);
        event.setBillDate(billDate);
        event.setResourceServiceCode(resourceServiceCode);
        event.setChargeType(chargeType);
        event.setSupplierId(supplierId);
        event.setChargeUsageCount(chargeUsageCount);
        event.setUsageCount(usageCount);
        event.setCashAmount(cashAmount);
        event.setPointAmount(pointAmount);
        return event;
    }
}
