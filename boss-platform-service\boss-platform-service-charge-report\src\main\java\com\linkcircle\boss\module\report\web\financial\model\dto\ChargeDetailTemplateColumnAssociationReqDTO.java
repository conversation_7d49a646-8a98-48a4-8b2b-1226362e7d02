package com.linkcircle.boss.module.report.web.financial.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/14 10:57
 */
@Data
public class ChargeDetailTemplateColumnAssociationReqDTO {

    @Schema(description = "数据库逻辑名称")
    @NotEmpty(message = "数据库逻辑名称不能为空")
    private String logicName;
    @Schema(description = "表名称")
    @NotEmpty(message = "表名称不能为空")
    private String tableName;

    @Schema(description = "字段名称-真正需要的字段比如客户名称")
    @NotEmpty(message = "字段名称不能为空")
    private String columnName;

    @Schema(description = "关联字段名称 比如客户id")
    //@NotEmpty(message = "关联字段名称不能为空")
    private String associationName;
}
