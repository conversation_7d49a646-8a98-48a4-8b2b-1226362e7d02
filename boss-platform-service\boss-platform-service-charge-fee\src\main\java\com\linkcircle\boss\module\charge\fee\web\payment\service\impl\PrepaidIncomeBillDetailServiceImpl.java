package com.linkcircle.boss.module.charge.fee.web.payment.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkcircle.boss.framework.sharding.algorithms.dto.HitBusinessTimeDTO;
import com.linkcircle.boss.framework.sharding.constants.LogicTableConstant;
import com.linkcircle.boss.framework.tanant.TenantIgnore;
import com.linkcircle.boss.module.billing.api.detail.income.model.entity.PrepaidIncomeBillDetailDO;
import com.linkcircle.boss.module.charge.fee.enums.WalletsDeductStatusEnum;
import com.linkcircle.boss.module.charge.fee.web.payment.mapper.PrepaidIncomeBillDetailMapper;
import com.linkcircle.boss.module.charge.fee.web.payment.service.IPrepaidIncomeBillDetailService;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Calendar;

/**
 * <p>
 * 预付费-收入账单-明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Service
public class PrepaidIncomeBillDetailServiceImpl extends ServiceImpl<PrepaidIncomeBillDetailMapper, PrepaidIncomeBillDetailDO> implements IPrepaidIncomeBillDetailService {

    /**
     * 更新预付费账单状态
     *
     * @param billId     主账单 ID
     * @param billStatus 支付状态
     * @return
     * @throws Exception
     */
    @Override
    @TenantIgnore
    @Transactional
    public boolean updateBillPaidStatus(Long billId, Integer billStatus, Integer walletsStatus, HitBusinessTimeDTO businessTimeDTO, JSONObject billJson) throws Exception {
        BigDecimal paidAmount = billJson.getBigDecimal("paidAmount");
        BigDecimal unPaidAmount = billJson.getBigDecimal("unPaidAmount");

//        long payTime = LocalDateTime.now().atZone(ZoneId.of("UTC")).toInstant().toEpochMilli();
        long payTime = Calendar.getInstance().getTimeInMillis();

        try (HintManager hintManager = HintManager.getInstance();) {
            hintManager.addTableShardingValue(LogicTableConstant.PREPAID_INCOME_BILL_DETAIL, businessTimeDTO);
            return lambdaUpdate().eq(PrepaidIncomeBillDetailDO::getBillDetailId, billId)
                    .set(PrepaidIncomeBillDetailDO::getPaidAmount, paidAmount)
                    .set(PrepaidIncomeBillDetailDO::getUnpaidAmount, unPaidAmount)
                    .set(PrepaidIncomeBillDetailDO::getBillStatus, billStatus)
                    .set(PrepaidIncomeBillDetailDO::getWalletDeductStatus, walletsStatus)
                    .set(PrepaidIncomeBillDetailDO::getPaymentTime, payTime)
                    .update();
        }
    }

    @Override
    @TenantIgnore
    @Transactional
    public boolean unSettledBillPaidStatus(Long billId, Integer billStatus, Integer walletsStatus, HitBusinessTimeDTO businessTimeDTO, JSONObject billJson) throws Exception {
        // 根据本次账单计算的已支付和未支付金额
        BigDecimal paidAmount = billJson.getBigDecimal("paidAmount");
        BigDecimal unPaidAmount = billJson.getBigDecimal("unPaidAmount");
        // 上次已付款的金额
        BigDecimal lastPaidAmount = billJson.getBigDecimal("lastPaidAmount");

//        long payTime = LocalDateTime.now().atZone(ZoneId.of("UTC")).toInstant().toEpochMilli();
        long payTime = Calendar.getInstance().getTimeInMillis();

        try (HintManager hintManager = HintManager.getInstance();) {
            hintManager.addTableShardingValue(LogicTableConstant.PREPAID_INCOME_BILL_DETAIL, businessTimeDTO);
            return lambdaUpdate().eq(PrepaidIncomeBillDetailDO::getBillDetailId, billId)
                    .set(PrepaidIncomeBillDetailDO::getPaidAmount, paidAmount.add(lastPaidAmount))
                    .set(PrepaidIncomeBillDetailDO::getUnpaidAmount, unPaidAmount)
                    .set(PrepaidIncomeBillDetailDO::getBillStatus, billStatus)
                    .set(PrepaidIncomeBillDetailDO::getWalletDeductStatus, walletsStatus)
                    .set(PrepaidIncomeBillDetailDO::getPaymentTime, payTime)
                    .update();
        }
    }

    /**
     * 余额不足 钱包已经是0或者负数
     *
     * @param billId
     * @param billStatus
     * @param businessTimeDTO
     * @param billJSON
     * @return
     * @throws Exception
     */
    @Override
    @TenantIgnore
    @Transactional
    public boolean updateBillNotSufficientFundsStatus(Long billId, Integer billStatus, HitBusinessTimeDTO businessTimeDTO, JSONObject billJSON) throws Exception {
        Integer paymentMethod = billJSON.getInteger("paymentMethod");
        // 待支付金额 or 积分
        BigDecimal unpaidAmount = 0 == paymentMethod ? billJSON.getBigDecimal("cashAmount") : billJSON.getBigDecimal("pointAmount");

        try (HintManager hintManager = HintManager.getInstance();) {
            hintManager.addTableShardingValue(LogicTableConstant.PREPAID_INCOME_BILL_DETAIL, businessTimeDTO);
            // 手动发起还是无法支付的情况 paidAmount 不变
            return lambdaUpdate().eq(PrepaidIncomeBillDetailDO::getBillDetailId, billId)
                    .set(PrepaidIncomeBillDetailDO::getPaidAmount, BigDecimal.ZERO)
                    .set(PrepaidIncomeBillDetailDO::getUnpaidAmount, unpaidAmount)
                    .set(PrepaidIncomeBillDetailDO::getWalletDeductStatus, WalletsDeductStatusEnum.NOTSUFFICIENTFUNDS.getCode())
                    .set(PrepaidIncomeBillDetailDO::getBillStatus, billStatus)
                    .update();
        }
    }

    @Override
    public PrepaidIncomeBillDetailDO getPrepaidBillByID(Long billId, HitBusinessTimeDTO businessTimeDTO) throws Exception {
        try (HintManager hm = HintManager.getInstance()) {
            hm.addTableShardingValue(LogicTableConstant.PREPAID_INCOME_BILL_DETAIL, businessTimeDTO);
            return lambdaQuery()
                    .select(PrepaidIncomeBillDetailDO::getBillDetailId,
                            PrepaidIncomeBillDetailDO::getCashAmount,
                            PrepaidIncomeBillDetailDO::getPointAmount,
                            PrepaidIncomeBillDetailDO::getPaidAmount,
                            PrepaidIncomeBillDetailDO::getUnpaidAmount,
                            PrepaidIncomeBillDetailDO::getWalletDeductStatus,
                            PrepaidIncomeBillDetailDO::getBillStatus)
                    .eq(PrepaidIncomeBillDetailDO::getBillDetailId, billId)
                    .one();
        }
    }

    @Override
    @TenantIgnore
    @Transactional
    public boolean updateBillCallbackStatus(Long billId, Integer callbackStatus, HitBusinessTimeDTO businessTimeDTO) throws Exception {
        try (HintManager hintManager = HintManager.getInstance();) {
            hintManager.addTableShardingValue(LogicTableConstant.PREPAID_INCOME_BILL_DETAIL, businessTimeDTO);
            return lambdaUpdate().eq(PrepaidIncomeBillDetailDO::getBillDetailId, billId)
                    .set(PrepaidIncomeBillDetailDO::getCallbackStatus, callbackStatus)
                    .update();
        }
    }


}
