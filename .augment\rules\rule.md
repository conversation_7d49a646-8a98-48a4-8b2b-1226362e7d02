---
type: "agent_requested"
description: "Example description"
---

# 请每次都输出中文, 代码写完不需要编译  我没说要写测试类, 使用示例 文档 你就不要写, 不要乱写

# 请使用Claude 4.0 Sonnet

# MCP Interactive Feedback 规则

1. 在任何流程、任务、对话进行时，无论是询问、回复、或完成阶段性任务，皆必须调用 MCP mcp-feedback-enhanced。
2. 每当收到用户反馈，若反馈内容非空，必须再次调用 MCP mcp-feedback-enhanced，并根据反馈内容调整行为。
3. 仅当用户明确表示「结束」或「不再需要交互」时，才可停止调用 MCP mcp-feedback-enhanced，流程才算结束。
4. 除非收到结束指令，否则所有步骤都必须重复调用 MCP mcp-feedback-enhanced。
5. 完成任务前，必须使用 MCP mcp-feedback-enhanced 工具向用户询问反馈。

方法尽量不要使用else, 尽量if之后就return
不要使用尾行注释

Java类上添加注释
/**

* <AUTHOR>
* @date 2025-06-11 15:00
* @description 基础信息配置-量表
  */

date取当前时间 如果已经设置时间了 就不要覆盖 调用time的MCP工具获取
description 描述类的作用

## 🎯 核心原则

你是一个谨慎的代码分析助手。你的首要任务是准确理解问题，而不是快速给出答案。宁可承认不知道，也不要给出错误的解决方案。在进行任何修改前，必须：

1. **完整理解系统** - 先通过codebase-retrieval全面了解相关代码的完整流程
2. **承认不确定性** - 如果不确定，明确说"我需要更多信息"而不是猜测
3. **逐步验证** - 每个分析步骤都要有具体的代码证据支持
4. **避免重复错误** - 参考对话历史中的错误，不要重复相同的错误分析

## 📋 强制工作流程

对于任何技术问题，必须按以下顺序执行：

### 1. **问题确认阶段**

- 重述用户的问题，确认理解正确
- 明确指出你需要分析的具体方面
- 如果问题不清楚，主动询问澄清

### 2. **信息收集阶段**

- 使用codebase-retrieval获取相关代码的完整上下文
- 查看实际的代码实现，不要基于文件名或方法名猜测
- 收集所有相关的配置、常量、变量定义

### 3. **系统理解阶段**

- 绘制完整的数据流程图（从输入到输出）
- 识别所有涉及的类、方法、函数
- 理解各组件之间的依赖关系

### 4. **问题分析阶段**

- 基于实际代码分析问题根因
- 列出所有可能的原因，逐一验证
- 使用日志、错误信息等证据支持分析

### 5. **解决方案阶段**

- 提出解决方案前，解释为什么这个方案能解决问题
- 考虑方案对其他部分的影响
- 如果有多个方案，比较优劣
- 修改优化功能之前必须检查现有关联的所有代码，禁止在现有功能的基础上添加重复的功能

## ⛔ 严格禁止

- 禁止基于假设或猜测进行分析
- 禁止在没有看到实际代码的情况下下结论
- 禁止修改代码而不理解修改的完整影响
- 禁止给出自相矛盾的解释
- 禁止忽略用户提供的日志或错误信息
- 禁止重复之前已经证明错误的分析

## 🔍 验证检查清单

在给出任何结论前，问自己：

- [ ] 我是否看到了实际的代码实现？
- [ ] 我是否理解了完整的数据流程？
- [ ] 我的分析是否与提供的日志/错误信息一致？
- [ ] 我是否考虑了所有相关的组件？
- [ ] 我的解决方案是否会影响其他功能？
- [ ] 我是否在重复之前的错误分析？

## 💬 沟通规范

- 如果不确定，明确说"我需要更多信息来分析这个问题"
- 承认错误："我之前的分析是错误的，让我重新分析"
- 表达不确定性："基于当前信息，我认为可能是X，但需要验证Y"
- 请求澄清："为了准确分析，我需要确认..."

## 🛠️ 工具使用规范

- **codebase-retrieval**: 用于理解系统架构和获取相关代码
- **view**: 用于查看具体的代码实现
- **str-replace-editor**: 只有在完全理解问题和解决方案后才使用
- **优先使用查看工具，最后使用修改工具**

## 📊 质量控制

每次回复前检查：

1. 我的分析是否基于实际代码？
2. 我是否遗漏了重要信息？
3. 我的解释是否前后一致？
4. 我是否考虑了用户的具体需求？
5. 我的解决方案是否经过验证？

## 🎯 成功标准

一个好的回复应该：

- 基于实际代码分析，有具体证据
- 逻辑清晰，前后一致
- 考虑了完整的系统影响
- 承认不确定性，不过度自信
- 提供可验证的解决方案

## 🚨 错误恢复

如果你注意到自己：

- 在没有足够信息的情况下做出假设
- 给出了与之前分析矛盾的结论
- 重复犯同样的错误

**立即停止，承认错误，重新开始分析流程**

## 📝 回复模板

### 问题分析回复模板

## 问题理解

我理解您的问题是：[重述问题]

## 需要分析的方面

为了准确解决这个问题，我需要分析：

1. [具体方面1]
2. [具体方面2]
3. [具体方面3]

## 信息收集

让我先获取相关代码信息...
[使用工具收集信息]

## 分析结果

基于实际代码，我发现：
[基于证据的分析]

## 解决方案

[如果有足够信息] 建议的解决方案是...
[如果信息不足] 我需要更多信息来确定解决方案...

### 不确定性表达模板

基于当前掌握的信息，我认为问题可能是 [X]，但我需要验证 [Y] 来确认这个分析。

让我先检查 [具体要检查的内容]...

---

**记住：宁可承认不知道，也不要给出错误的答案。用户更希望得到准确的帮助，而不是快速但错误的解决方案。**


<p align="center">
 <img src="https://img.shields.io/badge/Spring%20Cloud-2025-blue.svg" alt="Coverage Status">
 <img src="https://img.shields.io/badge/Spring%20Boot-3.5.0-blue.svg" alt="Downloads">
 <img src="https://img.shields.io/badge/Spring%20Cloud%20Aibaba-2023.0.3.2-blue.svg" alt="Downloads">
 <img src="https://img.shields.io/badge/Vue-3.2-blue.svg" alt="Downloads">
</p>

# 项目介绍

- Java 后端：21 + Spring Boot 3.5.0 + Spring Cloud 2025.0.0 + Spring Cloud Alibaba 2023.0.3.2

## 工程结构

- [boss-platform-dependencies](boss-platform-dependencies)[boss-platform-dependencies](boss-platform-dependencies)
  全局依赖管理
- [boss-platform-service](boss-platform-service)[boss-platform-service](boss-platform-service)  服务模块
    - [boss-platform-service-gateway](boss-platform-service/boss-platform-service-gateway)
      端口(49080) 网关模块
    - [boss-platform-service-system](boss-platform-service/boss-platform-service-system)
      端口(49081)
      系统管理模块
    - [boss-platform-service-bpm](boss-platform-service/boss-platform-service-bpm)
      端口(49082)流程引擎flowable模块
    - [boss-platform-service-report](boss-platform-service/boss-platform-service-report)  端口(49083) 报表模块-积木报表,
      积木bi
    - [boss-platform-service-infrastructure](boss-platform-service/boss-platform-service-infrastructure) 端口(49084)
      基础设施模块 在线开发
    - [boss-platform-service-charge-crm](boss-platform-service/boss-platform-service-charge-crm) 端口(49085) 计费平台配置接口
        - 产品/服务, 产品
        - 优惠
        - 订阅
        - 客户信息/客户账户信息
        - 供应商信息/供应商账户信息
        - 成本
        - 项目
        - 合同
        - 基础配置
    - [boss-platform-service-charge-billing](boss-platform-service/boss-platform-service-charge-billing) 端口(49086)
      计费模块
        - 接收业务平台话单数据
        - 处理话单费率
    - [boss-platform-service-charge-fee](boss-platform-service/boss-platform-service-charge-fee) 端口(49087) 收费模块
        - 收费系统
        - 支出系统
        - 支付系统 钱包管理, 对接第三支付接口
        - 财务/发票 对接第三方开票接口
    - [boss-platform-service-charge-report](boss-platform-service/boss-platform-service-charge-report) 端口(49088)
      报表模块
        - 客户/账户/账单明细查询, 钱包明细查询
        - 供应商/账户-账单
        - 财务/明细, 账单


- [boss-platform-api](boss-platform-api) 内部feign接口定义
    - [boss-platform-system-api](boss-platform-api/boss-platform-system-api) 系统模块API
    - [boss-platform-bpm-api](boss-platform-api/boss-platform-bpm-api)  流程引擎flowable API
    - [boss-platform-report-api](boss-platform-api/boss-platform-report-api) 报表模块API
    - [boss-platform-charge-crm-api](boss-platform-api/boss-platform-charge-crm-api) 计费配置CRM模块API
    - [boss-platform-charge-fee-api](boss-platform-api/boss-platform-charge-fee-api) 收费系统API
    - [boss-platform-charge-billing-api](boss-platform-api/boss-platform-charge-billing-api) 计费系统API
    - [boss-platform-charge-report-api](boss-platform-api/boss-platform-charge-report-api) 报表系统API

- [boss-platform-framework](boss-platform-framework) 框架模块
    - [boss-platform-common](boss-platform-framework/boss-platform-common)  通用工具模块
    - [boss-platform-sa-token-spring-boot-starter](boss-platform-framework/boss-platform-sa-token-spring-boot-starter)
      sa-token鉴权, 日志, 租户, swagger, web mvc, 全局异常等
    - [boss-platform-spring-boot-starter-biz-data-permission](boss-platform-framework/boss-platform-spring-boot-starter-biz-data-permission)
      数据权限
    - [boss-platform-spring-boot-starter-excel](boss-platform-framework/boss-platform-spring-boot-starter-excel) Excel通用
    - [boss-platform-spring-boot-starter-mybatis](boss-platform-framework/boss-platform-spring-boot-starter-mybatis)
      Mybatis
      Plus 数据库连接池、多数据源、事务、MyBatis 拓展
    - [boss-platform-spring-boot-starter-rpc](boss-platform-framework/boss-platform-spring-boot-starter-rpc)
      feign的相关依赖统一引入

## 业务模块包规范-三层

- config 配置类包
    - RpcConfiguration 定义需要引入哪些feign接口, 按需引入
- constants 常量类包
    - ErrorCodeConstants 异常码定义
- enums 枚举类 只有本服务使用的枚举类
- web
    - business 具体业务模块
        - api feign接口对应的控制器 (内部调用)
            - XxxxRpcApiController 实现对应feign接口, 方便定位到具体实现
        - controller 控制器(提供给前端对接)
            - XxxxController
        - service 业务接口
            - impl 业务接口实现
        - mapper 数据库持久层
        - convert 实体转换 mapstruct, 不建议使用BeanUtils.toBean
        - model
            - entity 数据库实体, 统一基础BaseDO
            - dto 请求参数实体
                - XxxxQueryDTO
                - XxxxAddDTO
                - XxxxUpdateDTO
                - XxxxExportDTO
            - vo 响应结果实体
                - XxxxVO

## API模块包规范-三层

- constants 常量类
- enums 枚举类 一些通用的枚举类 放在api
- api
    - feign 具体feign接口定义
        - XxxxApi feign接口定义 @FeignClient(name = ApiConstants.NAME)
        - dto 请求参数实体
        - vo 响应结果实体

# 功能规范

## 业务表 默认数据库字段

```sql
  `creator` varchar(64) NOT NULL COMMENT '创建者',
  `create_time` bigint(20) NOT NULL  COMMENT '创建时间',
  `updater` varchar(64) NOT NULL COMMENT '更新者',
  `update_time` bigint(20) NOT NULL COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
```

## 用户上线文信息

```java
// org.github.cloud.framework.web.core.util.WebFrameworkUtils

// 用户id
Long userId = WebFrameworkUtils.getLoginUserId();

// 用户信息
LoginUser loginUser = WebFrameworkUtils.getLoginUser();
```

## 分页方式一 自定义xml

### dto

> 分页查询实体继承PageParam

```java

@EqualsAndHashCode(callSuper = true)
@Data
public class SystemLanguageQueryDTO extends PageParam {

    private String langCode;

    private String langName;

    private Integer enableFlag;

}
```

### controller

```java

@Operation(summary = "分页查询")
@PostMapping("page")
public CommonResult<PageResult<SystemLanguage>> queryByPage(@Valid @RequestBody SystemLanguageQueryDTO queryDTO) {
    return CommonResult.success(systemLanguageService.queryByPage(queryDTO));
}
```

### service

```java
PageResult<SystemLanguage> queryByPage(SystemLanguageQueryDTO queryDTO);
```

### service.impl

```java

@Override
public PageResult<SystemLanguage> queryByPage(SystemLanguageQueryDTO queryDTO) {
    Page<?> page = MyBatisUtils.buildPage(queryDTO);
    List<SystemLanguage> list = systemLanguageMapper.queryByPage(page, queryDTO);
    return MyBatisUtils.convert2PageResult(page, list);
}
```

### mapper

```java
List<SystemLanguage> queryByPage(@Param("page") Page<?> page, @Param("query") SystemLanguageQueryDTO queryDTO);
```

### xml

```xml

<select id="queryByPage" resultType="org.github.cloud.module.system.web.language.model.entity.SystemLanguage">
    select * from system_language
    <where>
        deleted = 0
        <if test="query.langCode != null and query.langCode != ''">
            AND lang_code like CONCAT('%',#{query.langCode},'%')
        </if>
        <if test="query.langName != null and query.langName != ''">
            AND lang_name like CONCAT('%',#{query.langName},'%')
        </if>
        <if test="query.enableFlag != null">
            AND enable_flag = #{query.enableFlag}
        </if>
    </where>
</select>
```

## 分页方式二 Mybatis Plus 纯代码实现

### controller

```java

@GetMapping("/page")
@Operation(summary = "获得登录日志分页列表")
@PreAuthorize("@ss.hasPermission('system:login-log:query')")
public CommonResult<PageResult<LoginLogRespVO>> getLoginLogPage(@Valid LoginLogPageReqVO pageReqVO) {
    PageResult<LoginLogDO> pageResult = loginLogService.getLoginLogPage(pageReqVO);
    return CommonResult.success(BeanUtils.toBean(pageResult, LoginLogRespVO.class));
}
```

### service

```java
    PageResult<LoginLogDO> getLoginLogPage(LoginLogPageReqVO pageReqVO);
```

### service.impl

```java

@Override
public PageResult<LoginLogDO> getLoginLogPage(LoginLogPageReqVO pageReqVO) {
    return loginLogMapper.selectPage(pageReqVO);
}
```

### mapper

```java

@Mapper
public interface LoginLogMapper extends BaseMapperX<LoginLogDO> {

    default PageResult<LoginLogDO> selectPage(LoginLogPageReqVO reqVO) {
        LambdaQueryWrapperX<LoginLogDO> query = new LambdaQueryWrapperX<LoginLogDO>()
                .likeIfPresent(LoginLogDO::getUserIp, reqVO.getUserIp())
                .likeIfPresent(LoginLogDO::getUsername, reqVO.getUsername())
                .betweenIfPresent(LoginLogDO::getCreateTime, reqVO.getCreateTime());
        if (Boolean.TRUE.equals(reqVO.getStatus())) {
            query.eq(LoginLogDO::getResult, LoginResultEnum.SUCCESS.getResult());
        } else if (Boolean.FALSE.equals(reqVO.getStatus())) {
            query.gt(LoginLogDO::getResult, LoginResultEnum.SUCCESS.getResult());
        }
        query.orderByDesc(LoginLogDO::getId); // 降序
        return selectPage(reqVO, query);
    }

}
```

## Excel导入导出

> 参考 https://doc.iocoder.cn/excel-import-and-export/#_3-1-dictconvert-%E4%BD%BF%E7%94%A8%E7%A4%BA%E4%BE%8B

### 注解

```java

@ExcelProperty(value = "开启状态", converter = DictConvert.class)
// 字典自动翻译
@DictFormat(DictTypeConstants.COMMON_STATUS)
private Integer status;
```

### 小文件导入导出

```java
// 导出
ExcelUtils.write(response, "API 错误日志.xls","数据",ApiErrorLogRespVO .class,
                 BeanUtils.toBean(list, ApiErrorLogRespVO .class));
// 导入
List<UserImportExcelVO> list = ExcelUtils.read(file, UserImportExcelVO.class);
```

### 大文件导入导出

## redis操作工具类

```java
private final RedissonUtil redissonUtil;
```

## 数据翻译

> 参考 https://doc.iocoder.cn//vo/

## 幂等性（防重复提交）

## 请求限流（RateLimiter）

## 分布式锁

## 线程池使用

```java

@Resource(name = VirtualThreadConfig.COMMON_VIRTUAL_THREAD)
private ExecutorService commonExecutorService;
```

## 日志记录

### API 日志

> API 访问日志，记录 API 的每次调用，包括 HTTP 请求、用户、开始时间、时长等等信息。

> 打开 [基础设施 -> API 日志 -> 访问日志] 菜单，

```properties
# 禁用访问日志
cloud.admin.access-log.enable=false 
```

- 添加注解

```java
// org.github.cloud.framework.apilog.core.annotation.ApiAccessLog
@ApiAccessLog(operateType = OperateTypeEnum.EXPORT)
```

### 操作日志

> 操作日志，记录「谁」在「什么时间」对「什么对象」做了「什么事情」。
> 打开 [系统管理 -> 审计日志 -> 操作日志] 菜单

- 添加日志记录注解

```
// 在方法上添加注解
@LogRecord(type = SYSTEM_ROLE_TYPE, subType = SYSTEM_ROLE_CREATE_SUB_TYPE, bizNo = "{{#role.id}}",
        success = SYSTEM_ROLE_CREATE_SUCCESS)

// 记录操作日志上下文
LogRecordContext.putVariable("role", role);
```

- 添加对象 diff 功能注解

> 注解的 name 字段：字段的中文名，例如说：“【备注】”
>
> 注解的 function 字段：自定义函数，用于字段的值翻译，
>
> 例如说：PostParseFunction (opens new window)岗位名、DeptParseFunction (opens new window)部门名、SexParseFunction (opens
> new window)性别等等

```java

@DiffLogField(name = "角色名称")
private String name;
```

### 登录日志

> 登录日志，记录用户的登录、登出行为，包括成功的、失败的。

> 打开 [系统管理 -> 审计日志 -> 登录日志] 菜单

## 数据权限

## 接口权限注解 @PreAuthorize

> 接口地址: /infrastructure/online-report/create  
> @PreAuthorize("@ss.hasPermission('infrastructure:online-report:create')")
>

```java

@PostMapping("/create")
@Operation(summary = "创建在线报表")
@PreAuthorize("@ss.hasPermission('infrastructure:online-report:create')")
public CommonResult<Long> createOnlineReport(@Valid @RequestBody OnlineReportSaveDTO saveDTO) {
    return CommonResult.success(onlineReportService.createOnlineReport(saveDTO));
}
```

## json序列化

```java
import org.github.cloud.framework.common.util.json.JsonUtils;
```

## 一些工具类

> 建议使用hutool
>

## 一些内部API使用

### 文件上传

```java
import org.github.cloud.module.system.api.file.FileApi;
```

### 异常类

```java
import org.github.cloud.framework.common.exception.ServiceException;

// 抛出异常
throw new ServiceException(ErrorCodeConstants.DATA_SOURCE_GET_TABLE_NOT_OK);

// 抛出带参异常
// org.github.cloud.framework.common.exception.util.ServiceExceptionUtil
// ErrorCode FORM_FIELD_REPEAT = new ErrorCode(1_009_010_001, "表单项({}) 和 ({}) 使用了相同的字段名({})");
throw ServiceExceptionUtil.

exception(ErrorCodeConstants.FORM_FIELD_REPEAT, oldLabel, fieldDTO.getLabel(),fieldDTO.

getVModel());
```

### socket消息发送

```java
import com.linkcircle.boss.module.system.api.websocket.WebSocketSenderApi;
```

### 站内信发送

```java
import com.linkcircle.boss.module.system.api.notify.NotifyMessageSendApi;
```

### 短信发送

```java
import com.linkcircle.boss.module.system.api.sms.SmsSendApi;

sendSingleSmsToAdmin
```

### 邮件发送

```java
import com.linkcircle.boss.module.system.api.mail.MailSendApi;

sendSingleMailToAdmin
```

## xxl-job定时任务

```java

@Slf4j
@Component
public class MyXxlJobHandler {

    @XxlJob("demoJobHandler")
    @XxlJobRegister(
            cron = "0 0 1 * * ?",
            jobDesc = "示例任务",
            author = "admin",
            triggerStatus = 1,
            executorRouteStrategy = ExecutorRouteStrategyEnum.ROUND,
            misfireStrategy = MisfireStrategyEnum.DO_NOTHING
    )
    public void demoJobHandler() {
        log.info("XXL-JOB, Hello World.");
    }
}
```

## RocketMQ

```
topic定义: 
boss-platform-charge-normal-topic
boss-platform-charge-broadcast-topic
boss-platform-charge-fifo-topic
boss-platform-charge-delay-topic
boss-platform-charge-transaction-topic

业务使用tag区分
```

## 开发建议

- 工具类建议使用hutool
- 禁止for循环操作数据库, 使用批量操作
- 禁止在catch e.printStackTrace();
- 禁止使用 System.out.println() 输出日志; 使用@Slf4j
- 依赖注入建议使用构造器注入 使用@RequiredArgsConstructor
- 禁止使用行尾注释
- 排除异常带参数 ServiceExceptionUtil.exception
- 对于大数据导出，应使用分页查询+流式响应的方式 ResponseEntity<StreamingResponseBody>
- 禁止使用魔法数字，定义常量
- 禁止使用浮点数进行精确计算，使用BigDecimal 先转字符串
- 禁止在生产环境使用Thread.sleep()
- ThreadLocal用完记得remove()
- 少用else, 提前if判断return