# BOSS Platform Service - Charge Billing

## 📋 项目概述

boss-platform-service-charge-billing 是 BOSS 平台的核心计费服务模块，负责处理业务平台的话单数据和费率计算。该服务支持多种计费模式，包括预付费、后付费、产品计费和资源成本计费等复杂业务场景。

### 核心功能
- 🧮 **多维度计费处理**：支持收入计费和成本计费
- 📊 **多种费率策略**：固定费率、阶梯费率、套餐费率、按量费率
- ⏰ **定时出账任务**：支持产品出账和资源出账的定时处理
- 🔄 **消息队列集成**：基于 RocketMQ 的异步计费处理
- 💾 **多级缓存策略**：本地缓存 + Redis 缓存优化性能
- 🔐 **安全认证**：基于签名的 API 鉴权机制

## 🏗️ 技术架构

### 技术栈
- **Java 21** - 现代 Java 特性支持
- **Spring Boot 3.x** - 微服务框架
- **Spring Cloud** - 微服务生态
- **MyBatis Plus** - ORM 框架
- **PostgreSQL** - 主数据库
- **Redis** - 缓存存储
- **RocketMQ** - 消息队列
- **Nacos** - 服务注册与配置中心
- **XXL-Job** - 分布式任务调度

### 架构模式
- **策略模式**：费率计算策略的灵活扩展
- **责任链模式**：计费处理流程的模块化
- **工厂模式**：策略实例的统一管理
- **观察者模式**：缓存更新的事件驱动

## 📁 项目结构

```
boss-platform-service-charge-billing/
├── src/main/java/com/linkcircle/boss/module/billing/
│   ├── ChargeBillingServerApplication.java          # 应用启动类
│   ├── aop/                                         # AOP 切面
│   │   └── BillingAuthAspect.java                  # 计费鉴权切面
│   ├── config/                                      # 配置类
│   │   └── MyBatisInterceptorConfig.java           # MyBatis 拦截器配置
│   ├── constants/                                   # 常量定义
│   │   ├── BillingConstant.java                    # 计费常量
│   │   ├── ErrorCodeConstants.java                 # 错误码常量
│   │   ├── RateTypeConstant.java                   # 费率类型常量
│   │   └── ResponsibilityChainGroupConstant.java   # 责任链组常量
│   ├── context/                                     # 上下文管理
│   │   └── BusinessParamsContext.java              # 业务参数上下文
│   ├── enums/                                       # 枚举类
│   │   ├── BillingProcessStatusEnum.java           # 计费处理状态枚举
│   │   ├── CostRateTypeEnum.java                   # 成本费率类型枚举
│   │   ├── IncomeRateTypeEnum.java                 # 收入费率类型枚举
│   │   └── OriginalPriceRateTypeEnum.java          # 原价费率类型枚举
│   ├── exception/                                   # 异常类
│   │   ├── BillingFailException.java               # 计费失败异常
│   │   ├── CostRateChargeFailException.java        # 成本费率计费失败异常
│   │   ├── IncomeRateChargeFailException.java      # 收入费率计费失败异常
│   │   ├── RepeatedBillingException.java           # 重复计费异常
│   │   ├── ScaleTableNotBindException.java         # 量表未绑定异常
│   │   └── ServiceConfigNotFoundException.java     # 服务配置未找到异常
│   ├── 
interceptor/                                 # 拦截器
│   │   └── DynamicBusinessParamsInterceptor.java   # 动态业务参数拦截器
│   └── web/                                         # Web 层
│       ├── bill/                                    # 账单处理
│       │   ├── product/                            # 产品账单
│       │   │   ├── controller/                     # 控制器
│       │   │   ├── mapper/                         # 数据访问层
│       │   │   └── scheduled/                      # 定时任务
│       │   │       ├── product/                    # 产品出账
│       │   │       └── service/                    # 服务出账
│       │   └── resource/                           # 资源账单
│       │       └── scheduled/                      # 资源出账定时任务
│       ├── cache/                                   # 缓存管理
│       │   ├── api/                                # 缓存 API
│       │   ├── consumer/                           # 缓存消费者
│       │   ├── controller/                         # 缓存控制器
│       │   ├── manager/                            # 缓存管理器
│       │   └── sender/                             # 缓存消息发送器
│       └── data/                                    # 数据服务
│           ├── cache/                              # 数据缓存
│           ├── model/                              # 数据模型
│           └── service/                            # 数据服务
├── src/main/resources/                              # 资源文件
│   ├── application.yaml                            # 应用配置
│   ├── bootstrap.yml                               # 启动配置
│   └── mapper/                                     # MyBatis 映射文件
├── docs/                                           # 文档目录
│   ├── 成本费率计算策略实现总结.md                    # 成本费率策略文档
│   └── tier-payment-status-design.md              # 阶梯付费状态设计
└── pom.xml                                         # Maven 配置
```

## 🚀 核心功能模块

### 1. 计费策略引擎

#### 收入费率策略
- **固定费率**：按固定价格计费
- **阶梯费率**：根据用量阶梯计费，支持全额支付和按量支付
- **套餐费率**：套餐内免费，超出部分按量计费
- **按量费率**：纯按量计费模式

#### 成本费率策略
- **成本固定费率**：供应商固定成本计费
- **成本阶梯费率**：基于采购量的阶梯成本
- **成本套餐费率**：采购套餐成本计算
- **成本按量费率**：按实际使用量的成本计费

### 2. 出账系统

#### 产品出账
- **服务出账**：按服务维度进行周期性出账
- **产品出账**：将服务账单聚合为产品账单
- **支持多种出账周期**：日、周、月、季、年

#### 资源出账
- **资源服务出账**：基于资源采购的成本出账
- **供应商账单**：面向供应商的成本结算

### 3. 缓存管理系统

#### 多级缓存架构
- **本地缓存**：基于 Caffeine 的高性能本地缓存
- **分布式缓存**：Redis 集群缓存
- **缓存更新机制**：基于消息队列的缓存同步

#### 缓存策略
- **费率用量缓存**：支持阶梯、套餐费率的累计用量缓存
- **配置缓存**：账户信息、订阅信息、费率配置缓存
- **计费锁缓存**：防止重复计费的分布式锁
### 4. 认证与安全

#### API 鉴权
- **签名验证**：基于 HMAC-SHA256 的请求签名
- **时间戳验证**：防止重放攻击
- **AppId 验证**：应用身份验证
- **参数完整性校验**：必要参数检查

## 📊 数据模型

### 核心实体

#### 账单实体
- **PostpaidProductIncomeBillDO**：后付费产品收入账单
- **PostpaidProductServiceIncomeBillDO**：后付费产品服务收入账单
- **PostpaidResourceServiceCostBillDO**：后付费资源服务成本账单

#### 配置实体
- **CustomerAccountVO**：客户账户信息
- **SupplierAccountVO**：供应商账户信息
- **AccountSubscriptionsVO**：账户订阅信息
- **ResourcePurchaseVO**：资源采购信息

### 数据关系
```
账户 (Account)
├── 订阅 (Subscription)
│   ├── 产品 (Product)
│   │   └── 服务 (Service)
│   └── 费率配置 (Rate Config)
└── 账单 (Bill)
    ├── 产品账单 (Product Bill)
    └── 服务账单 (Service Bill)
```

## 🔧 配置说明

### 应用配置
```yaml
spring:
  application:
    name: boss-platform-service-charge-billing
  cloud:
    nacos:
      server-addr: ${NACOS_SERVER}
      config:
        namespace: ${NACOS_NAMESPACE}
        group: ${NACOS_GROUP}
```

### 数据库配置
- **主数据库**：PostgreSQL
- **分库分表**：基于 Sharding-JDBC
- **连接池**：HikariCP

### 消息队列配置
- **RocketMQ**：异步计费处理
- **事务消息**：保证数据一致性
- **延迟消息**：支持延迟重试#
# 🚀 快速开始

### 环境要求
- Java 21+
- Maven 3.8+
- PostgreSQL 12+
- Redis 6+
- RocketMQ 4.9+
- Nacos 2.0+

### 启动步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd boss-platform-service-charge-billing
```

2. **配置环境变量**
```bash
export NACOS_SERVER=localhost:8848
export NACOS_USERNAME=nacos
export NACOS_PASSWORD=nacos
export NACOS_NAMESPACE=dev
export NACOS_GROUP=DEFAULT_GROUP
```

3. **编译项目**
```bash
mvn clean compile
```

4. **启动应用**
```bash
mvn spring-boot:run
```

### 健康检查
```bash
curl http://localhost:8080/actuator/health
```

## 📝 API 文档

### 计费 API

#### 创建账单明细
```http
POST /api/billing/detail
Content-Type: application/json
X-AppId: your-app-id
X-Sign: request-signature
X-Timestamp: *************

{
  "businessId": "business-123",
  "accountId": 1001,
  "serviceCode": "SMS",
  "usage": 100,
  "usageUnit": "条"
}
```

#### 产品出账
```http
POST /api/billing/product/{accountId}
```

#### 缓存管理
```http
POST /api/cache/refresh/subscription?accountId=1001
POST /api/cache/evict?cacheType=subscription&id=1001
```## 
🧪 测试

### 单元测试
```bash
mvn test
```

### 集成测试
```bash
mvn verify -P integration-test
```

### 测试覆盖率
```bash
mvn jacoco:report
```

## 📈 监控与运维

### 监控指标
- **计费成功率**：计费处理成功的比例
- **计费延迟**：计费处理的平均延迟
- **缓存命中率**：各级缓存的命中率
- **消息队列积压**：待处理消息数量

### 日志配置
- **日志级别**：支持动态调整
- **日志格式**：结构化 JSON 格式
- **日志文件**：按日期滚动

### 告警配置
- **计费失败告警**：计费失败率超过阈值
- **系统异常告警**：系统错误和异常
- **性能告警**：响应时间和吞吐量异常

## 🔄 部署

### Docker 部署
```dockerfile
FROM openjdk:21-jre-slim
COPY target/boss-platform-service-charge-billing.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### Kubernetes 部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: charge-billing-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: charge-billing-service
  template:
    metadata:
      labels:
        app: charge-billing-service
    spec:
      containers:
      - name: charge-billing
        image: charge-billing:latest
        ports:
        - containerPort: 8080
```## 🤝 
贡献指南

### 开发规范
- **代码风格**：遵循 Google Java Style Guide
- **提交规范**：使用 Conventional Commits
- **测试要求**：新功能必须包含单元测试
- **文档更新**：API 变更需要更新文档

### 提交流程
1. Fork 项目
2. 创建功能分支
3. 提交代码变更
4. 创建 Pull Request
5. 代码审查
6. 合并到主分支

## 📚 相关文档

- [成本费率计算策略实现总结](docs/成本费率计算策略实现总结.md)
- [阶梯付费状态设计方案](docs/tier-payment-status-design.md)
- [API 接口文档](docs/api-documentation.md)
- [部署运维手册](docs/deployment-guide.md)

## 📄 许可证

本项目采用 [MIT License](LICENSE) 许可证。

## 👥 维护团队

- **项目负责人**：linshiqiang
- **核心开发者**：BOSS Platform Team
- **技术支持**：<EMAIL>

---

**版本**：v1.0  
**最后更新**：2025-01-31  
**文档状态**：✅ 最新