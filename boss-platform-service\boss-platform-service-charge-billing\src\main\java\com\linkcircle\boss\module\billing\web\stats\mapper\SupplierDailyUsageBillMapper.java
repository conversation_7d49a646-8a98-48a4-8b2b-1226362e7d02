package com.linkcircle.boss.module.billing.web.stats.mapper;

import com.linkcircle.boss.framework.mybatis.core.mapper.BaseMapperX;
import com.linkcircle.boss.module.billing.api.stats.supplier.model.entity.SupplierDailyUsageBillDO;
import com.linkcircle.boss.module.billing.web.stats.model.vo.DailyUsageStatsSummaryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2025-06-18 08:58
 * @description 供应商每日使用量账单表 Mapper
 */
@Mapper
public interface SupplierDailyUsageBillMapper extends BaseMapperX<SupplierDailyUsageBillDO> {

    /**
     * 统计预付费成本账单使用量数据（SUM聚合）
     *
     * @param serviceCode 服务编码
     * @param serviceId   服务ID
     * @param productId   产品ID
     * @param accountId   账户ID
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 统计结果
     */
    DailyUsageStatsSummaryVO sumPrepaidCostUsage(@Param("serviceCode") String serviceCode,
                                                 @Param("serviceId") Long serviceId,
                                                 @Param("productId") Long productId,
                                                 @Param("accountId") Long accountId,
                                                 @Param("startTime") Long startTime,
                                                 @Param("endTime") Long endTime);

    /**
     * 统计后付费成本账单使用量数据（SUM聚合）
     *
     * @param serviceCode 服务编码
     * @param serviceId   服务ID
     * @param productId   产品ID
     * @param accountId   账户ID
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 统计结果
     */
    DailyUsageStatsSummaryVO sumPostpaidCostUsage(@Param("serviceCode") String serviceCode,
                                                  @Param("serviceId") Long serviceId,
                                                  @Param("productId") Long productId,
                                                  @Param("accountId") Long accountId,
                                                  @Param("startTime") Long startTime,
                                                  @Param("endTime") Long endTime);
}
