package com.linkcircle.boss.module.billing.web.stats.mapper;

import com.linkcircle.boss.framework.mybatis.core.mapper.BaseMapperX;
import com.linkcircle.boss.module.billing.api.stats.supplier.model.entity.SupplierDailyUsageBillDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-18 08:58
 * @description 供应商每日使用量账单表 Mapper
 */
@Mapper
public interface SupplierDailyUsageBillMapper extends BaseMapperX<SupplierDailyUsageBillDO> {

    /**
     * 查询预付费成本账单表中的所有serviceCode
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return serviceCode列表
     */
    List<String> queryServiceCodesFromPrepaidCost(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 查询后付费成本账单表中的所有serviceCode
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return serviceCode列表
     */
    List<String> queryServiceCodesFromPostpaidCost(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 统计预付费成本账单使用量数据
     *
     * @param serviceCode 服务编码
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 统计结果列表
     */
    List<SupplierDailyUsageBillDO> statsPrepaidCostUsage(@Param("serviceCode") String serviceCode,
                                                         @Param("startTime") Long startTime,
                                                         @Param("endTime") Long endTime);

    /**
     * 统计后付费成本账单使用量数据
     *
     * @param serviceCode 服务编码
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 统计结果列表
     */
    List<SupplierDailyUsageBillDO> statsPostpaidCostUsage(@Param("serviceCode") String serviceCode,
                                                          @Param("startTime") Long startTime,
                                                          @Param("endTime") Long endTime);
}
