package com.linkcircle.boss.module.billing.api.detail.cost.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.JdbcType;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-18 08:59
 * @description 预付费成本账单明细表
 */
@TableName("prepaid_cost_bill_detail")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrepaidCostBillDetailDO {

    /**
     * 成本账单明细id
     */
    @TableId(type = IdType.INPUT)
    private Long billDetailId;

    /**
     * 业务唯一id
     */
    private String businessId;

    /**
     * 业务产生的时间戳
     */
    private Long businessTime;

    /**
     * 资源服务id
     */
    private Long resourceServiceId;

    /**
     * 服务编码
     */
    private String serviceCode;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 账户id
     */
    private Long accountId;

    /**
     * 主体id
     */
    private Long entityId;

    /**
     * 采购id
     */
    private Long purchaseId;

    /**
     * 消耗量
     */
    @TableField("usage_count")
    private BigDecimal usageCount;

    /**
     * 消耗量单位
     */
    private String usageUnit;

    /**
     * 支付类型 0-预付费, 1-后付费
     */
    private Integer paymentType;

    /**
     * 支付方式 0-现金, 1-积分
     */
    private Integer paymentMethod;

    /**
     * 计费类型  0-固定费率, 1-阶梯费率, 2-套餐计费, 3-按量计费
     */
    private Integer billingType;

    /**
     * 账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清
     */
    private Integer billStatus;

    /**
     * 积分
     */
    private BigDecimal pointAmount;

    /**
     * 现金
     */
    private BigDecimal cashAmount;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 含税总金额
     */
    @TableField("amount_with_tax")
    @Schema(description = "含税总金额")
    private BigDecimal amountWithTax;

    /**
     * 不含税金额
     */
    @TableField("amount_without_tax")
    @Schema(description = "不含税金额")
    private BigDecimal amountWithoutTax;

    @Schema(description = "是否 试用期")
    private Boolean inTrial;

    /**
     * 目录单价
     */
    private BigDecimal originalUnitPrice;

    /**
     * 目录总价(单价原价)
     */
    private BigDecimal originalPrice;

    /**
     * 计费单位数
     */
    private BigDecimal chargeUnitCount;

    @Schema(description = "计费消耗量(measure*charge_unit_count)")
    private BigDecimal chargeUsageCount;

    @Schema(description = "计费单位数量")
    private BigDecimal chargeMeasure;

    @Schema(description = "计费计量单位")
    private String chargeMeasureUnit;

    @Schema(description = "计量单位是否向上取整 0-不向上取整, 1-向上取整")
    private Integer chargeMeasureCeil;

    /**
     * 货币单位 CNY USD
     */
    private String currency;

    /**
     * 实际支付时间戳
     */
    private Long paymentTime;

    /**
     * 出账时间戳
     */
    private Long billingTime;

    /**
     * 数据创建时间戳
     */
    private Long createTime;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 接口请求id
     */
    private String requestId;

    /**
     * 费用详情json 见 billing-api com.linkcircle.boss.module.billing.api.rate.model.dto.*
     */
    @TableField(value = "rate_details", jdbcType = JdbcType.OTHER)
    private String rateDetails;
}
