package com.linkcircle.boss.module.billing.web.bill.resource.scheduled.service.impl;

import cn.hutool.core.lang.Pair;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.module.billing.api.bill.resource.model.entity.PostpaidResourceServiceCostBillDO;
import com.linkcircle.boss.module.billing.api.detail.cost.model.entity.PostpaidCostBillDetailDO;
import com.linkcircle.boss.module.billing.api.rate.model.dto.PackageRateConfigDTO;
import com.linkcircle.boss.module.billing.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.billing.enums.BillingProcessStatusEnum;
import com.linkcircle.boss.module.billing.enums.OriginalPriceRateTypeEnum;
import com.linkcircle.boss.module.billing.web.bill.resource.scheduled.model.dto.CostPostpaidResourceBillingMessageDTO;
import com.linkcircle.boss.module.billing.web.bill.resource.scheduled.service.CostPostpaidResourceBillingCalculateService;
import com.linkcircle.boss.module.billing.web.bill.resource.scheduled.service.CostPostpaidResourceServiceBillService;
import com.linkcircle.boss.module.billing.web.data.service.CyclePeriodCalculateService;
import com.linkcircle.boss.module.billing.web.detail.cost.service.calculation.strategy.AbstractCostRateChargeStrategy;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateRequest;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateResponse;
import com.linkcircle.boss.module.crm.api.supplier.purchase.vo.ResourcePurchaseVO;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import io.github.kk01001.design.pattern.strategy.StrategyFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-07-17
 * @description 后付费资源服务出账计算服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CostPostpaidResourceBillingCalculateServiceImpl extends AbstractCostRateChargeStrategy implements CostPostpaidResourceBillingCalculateService {

    private final CostPostpaidResourceServiceBillService resourceServiceBillService;
    private final StrategyFactory strategyFactory;

    @Override
    protected CyclePeriodCalculateService getCyclePeriodCalculateService() {
        return null;
    }

    @Override
    public void processBillingCalculation(CostPostpaidResourceBillingMessageDTO billingMessage) {
        PostpaidResourceServiceCostBillDO resourceServiceCostBillDO = billingMessage.getResourceServiceCostBillDO();
        Long billingRecordId = resourceServiceCostBillDO.getResourceServiceBillId();

        log.info("开始处理资源服务出账计算, billingRecordId: {}, accountId: {}, serviceId: {}",
                billingRecordId, resourceServiceCostBillDO.getAccountId(), resourceServiceCostBillDO.getServiceId());

        try {
            boolean calculateSuccess = calculateUsageAndFee(billingMessage);
            log.info("资源服务出账计算完成, calculateSuccess: {}, billingRecordId: {}", calculateSuccess, billingRecordId);
        } catch (Exception e) {
            log.error("处理资源服务出账计算异常, billingRecordId: {}", billingRecordId, e);
            throw e;
        }
    }

    @Override
    public boolean calculateUsageAndFee(CostPostpaidResourceBillingMessageDTO billingMessage) {
        PostpaidResourceServiceCostBillDO resourceServiceCostBillDO = billingMessage.getResourceServiceCostBillDO();
        ResourcePurchaseVO purchase = billingMessage.getPurchase();

        Long serviceId = resourceServiceCostBillDO.getServiceId();
        String serviceCode = resourceServiceCostBillDO.getServiceCode();
        String billingCycle = resourceServiceCostBillDO.getBillingCycle();
        Long billingStartTime = resourceServiceCostBillDO.getBillingStartTime();
        Long billingEndTime = resourceServiceCostBillDO.getBillingEndTime();
        Long accountId = resourceServiceCostBillDO.getAccountId();
        BigDecimal taxRate = resourceServiceCostBillDO.getTaxRate();

        log.info("开始计算资源服务使用量和费用, serviceId: {}, serviceCode: {}, billingCycle: {}, accountId: {}",
                serviceId, serviceCode, billingCycle, accountId);

        // 1. 统计使用量（使用SQL统计sum(usage_count)）
        PostpaidCostBillDetailDO detailDO = resourceServiceBillService.getTotalUsageByServiceAndTime(
                purchase.getId(), serviceCode, billingStartTime, billingEndTime);

        if (Objects.isNull(detailDO) || detailDO.getChargeUsageCount().compareTo(BigDecimal.ZERO) == 0) {
            log.warn("使用量为0, serviceCode: {}, accountId: {}, 时间范围: {} - {}",
                    serviceCode, accountId, billingStartTime, billingEndTime);
            return true;
        }
        BigDecimal chargeUsageCount = detailDO.getChargeUsageCount();
        log.info("统计使用量完成, totalUsage: {}", chargeUsageCount);

        // 2. 根据消耗量计算原价
        OriginalPriceCalculateResponse priceResponse = calculateRateFee(billingMessage, detailDO, resourceServiceCostBillDO, taxRate);
        if (!Boolean.TRUE.equals(priceResponse.getSuccess())) {
            log.error("原价计算失败, accountId: {}, serviceId: {}, priceResponse: {}", accountId, serviceId, priceResponse);
            return false;
        }

        // 3. 更新服务账单记录的费用信息
        boolean updateSuccess = saveServiceBillFeeInfo(resourceServiceCostBillDO, priceResponse, detailDO);
        if (!updateSuccess) {
            log.error("更新资源服务账单费用信息失败, billId: {}", resourceServiceCostBillDO.getResourceServiceBillId());
            return false;
        }

        log.info("资源服务使用量和费用计算完成, serviceId: {}, serviceCode: {}, totalUsage: {}, originalPrice: {}",
                serviceId, serviceCode, chargeUsageCount, priceResponse.getOriginalPrice());
        return true;
    }

    @Override
    public boolean markBillingProcessing(Long billingRecordId) {
        try {
            boolean success = resourceServiceBillService.updateBillStatus(billingRecordId, BillingProcessStatusEnum.PROCESSING);
            log.info("更新资源服务账单状态为处理中, billingRecordId: {}, 结果: {}", billingRecordId, success);
            return success;
        } catch (Exception e) {
            log.error("更新资源服务账单状态为处理中异常, billingRecordId: {}", billingRecordId, e);
            return false;
        }
    }

    @Override
    public boolean markBillingCompleted(Long billingRecordId) {
        try {
            boolean success = resourceServiceBillService.updateBillStatus(billingRecordId, BillingProcessStatusEnum.COMPLETED);
            log.info("更新资源服务账单状态为已完成, billingRecordId: {}, 结果: {}", billingRecordId, success);
            return success;
        } catch (Exception e) {
            log.error("更新资源服务账单状态为已完成异常, billingRecordId: {}", billingRecordId, e);
            return false;
        }
    }

    @Override
    public boolean markBillingFailed(Long billingRecordId, String errorMessage, Integer retryCount) {
        try {
            boolean success = resourceServiceBillService.updateBillStatus(billingRecordId, BillingProcessStatusEnum.FAILED);
            log.info("更新资源服务账单状态为失败, billingRecordId: {}, errorMessage: {}, retryCount: {}, 结果: {}",
                    billingRecordId, errorMessage, retryCount, success);
            return success;
        } catch (Exception e) {
            log.error("更新资源服务账单状态为失败异常, billingRecordId: {}, errorMessage: {}, retryCount: {}",
                    billingRecordId, errorMessage, retryCount, e);
            return false;
        }
    }

    /**
     * 根据消耗量计算原价
     *
     * @param billingMessage            账单消息
     * @param detailDO                  统计数据
     * @param resourceServiceCostBillDO 资源服务账单DO
     * @param taxRate                   税率
     * @return 原价计算响应
     */
    private OriginalPriceCalculateResponse calculateRateFee(CostPostpaidResourceBillingMessageDTO billingMessage,
                                                            PostpaidCostBillDetailDO detailDO,
                                                            PostpaidResourceServiceCostBillDO resourceServiceCostBillDO,
                                                            BigDecimal taxRate) {
        ResourcePurchaseVO purchase = billingMessage.getPurchase();
        ResourcePurchaseVO.Detail detail = billingMessage.getDetail();

        try {
            Integer rateType = detail.getChargeType();
            ChargeRateTypeEnum rateTypeEnum = ChargeRateTypeEnum.getByType(rateType);
            String currency = resourceServiceCostBillDO.getCurrency();

            OriginalPriceCalculateRequest request = new OriginalPriceCalculateRequest();
            request.setTotalUsageWithCurrent(detailDO.getChargeUsageCount());
            request.setPreviousUsage(BigDecimal.ZERO);
            request.setCurrentUsage(detailDO.getChargeUsageCount());
            request.setCurrency(resourceServiceCostBillDO.getCurrency());
            request.setPaymentOptions(detail.getPaymentOptions());
            request.setTaxRate(taxRate);
            request.setCalculateTaxEnabled(true);
            request.setStartTime(resourceServiceCostBillDO.getBillingStartTime());
            request.setEndTime(resourceServiceCostBillDO.getBillingEndTime());
            request.setCycleStartTime(resourceServiceCostBillDO.getBillingStartTime());
            request.setCycleEndTime(resourceServiceCostBillDO.getBillingEndTime());
            convertRateConfig(request, rateTypeEnum, detail, currency);

            // 调用原价计算策略
            OriginalPriceCalculateResponse response = strategyFactory.execute(OriginalPriceRateTypeEnum.class,
                    rateTypeEnum.name(), request);

            if (response == null || !Boolean.TRUE.equals(response.getSuccess())) {
                log.error("原价计算失败, serviceId: {}, response: {}", resourceServiceCostBillDO.getServiceId(), response);
                return OriginalPriceCalculateResponse.fail(ErrorCodeConstants.COST_BILL_CALCULATION_FAILED);
            }

            return response;

        } catch (Exception e) {
            log.error("原价计算异常, purchaseId: {}, serviceId: {}",
                    purchase.getId(), resourceServiceCostBillDO.getServiceId(), e);
            return OriginalPriceCalculateResponse.fail(ErrorCodeConstants.COST_BILL_CALCULATION_FAILED);
        }
    }

    /**
     * 处理费率对象
     */
    private void convertRateConfig(OriginalPriceCalculateRequest request,
                                   ChargeRateTypeEnum rateTypeEnum,
                                   ResourcePurchaseVO.Detail service,
                                   String currency) {
        switch (rateTypeEnum) {
            case FIXED:
                request.setRateConfig(getFixedRateConfigDTO(service.getCurrencyPriceJson(), currency));
                break;
            case TIERED:
                request.setRateConfig(getTierRateConfigDTO(service.getCurrencyPriceJson(), currency));
                break;
            case PACKAGE:
                Pair<PackageRateConfigDTO, PackageRateConfigDTO.PackageConfigDTO> pair = getPackageRateConfigDTO(service.getCurrencyPriceJson(), currency);
                request.setRateConfig(pair.getKey());
                request.setPackageConfig(pair.getValue());
                break;
            case USAGE:
                request.setRateConfig(getUsageBasedRateConfigDTO(service.getCurrencyPriceJson()));
                break;
            default:
                break;
        }
    }

    /**
     * 保存资源服务账单费用信息
     *
     * @param resourceServiceCostBillDO 资源服务账单DO
     * @param priceResponse             原价计算响应
     * @param detailDO                  统计数据
     * @return 是否更新成功
     */
    private boolean saveServiceBillFeeInfo(PostpaidResourceServiceCostBillDO resourceServiceCostBillDO,
                                           OriginalPriceCalculateResponse priceResponse,
                                           PostpaidCostBillDetailDO detailDO) {
        resourceServiceCostBillDO.setResourceServiceBillId(resourceServiceCostBillDO.getResourceServiceBillId());
        resourceServiceCostBillDO.setUsageCount(detailDO.getUsageCount());
        resourceServiceCostBillDO.setUsageUnit(priceResponse.getUsageUnit());
        resourceServiceCostBillDO.setChargeUsageCount(priceResponse.getChargeUsageCount());
        resourceServiceCostBillDO.setChargeMeasure(priceResponse.getMeasure());
        resourceServiceCostBillDO.setChargeMeasureUnit(priceResponse.getMeasureUnit());
        resourceServiceCostBillDO.setChargeMeasureCeil(priceResponse.getMeasureCeil());

        resourceServiceCostBillDO.setOriginalPrice(priceResponse.getOriginalPrice());

        resourceServiceCostBillDO.setAmountWithTax(priceResponse.getAmountWithTax());
        resourceServiceCostBillDO.setAmountWithoutTax(priceResponse.getAmountWithoutTax());
        resourceServiceCostBillDO.setRateDetails(JsonUtils.toJsonString(priceResponse.getRateConfig()));
        resourceServiceCostBillDO.setBillingStatus(BillingProcessStatusEnum.COMPLETED.getStatus());
        return resourceServiceBillService.updateServiceBillFeeInfo(resourceServiceCostBillDO);
    }
}