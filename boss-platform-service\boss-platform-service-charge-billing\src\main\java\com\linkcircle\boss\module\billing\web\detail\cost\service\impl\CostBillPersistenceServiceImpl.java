package com.linkcircle.boss.module.billing.web.detail.cost.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.framework.sharding.algorithms.dto.HitBusinessTimeDTO;
import com.linkcircle.boss.framework.sharding.constants.LogicTableConstant;
import com.linkcircle.boss.module.billing.api.detail.cost.model.entity.PostpaidCostBillDetailDO;
import com.linkcircle.boss.module.billing.api.detail.cost.model.entity.PrepaidCostBillDetailDO;
import com.linkcircle.boss.module.billing.context.BusinessParamsContext;
import com.linkcircle.boss.module.billing.web.data.service.ScaleDataService;
import com.linkcircle.boss.module.billing.web.detail.cost.mapper.PostpaidCostBillDetailMapper;
import com.linkcircle.boss.module.billing.web.detail.cost.mapper.PrepaidCostBillDetailMapper;
import com.linkcircle.boss.module.billing.web.detail.cost.model.dto.CostBillDetailRequestDTO;
import com.linkcircle.boss.module.billing.web.detail.cost.model.dto.ResourceSubscriptionInfoDTO;
import com.linkcircle.boss.module.billing.web.detail.cost.service.CostBillPersistenceService;
import com.linkcircle.boss.module.billing.web.detail.cost.service.calculation.chain.context.CostDetailBillRequestContext;
import com.linkcircle.boss.module.billing.web.detail.cost.service.calculation.strategy.context.CostRateChargeResponse;
import com.linkcircle.boss.module.billing.web.stats.event.SupplierUsageStatsEvent;
import com.linkcircle.boss.module.crm.api.supplier.purchase.vo.ResourcePurchaseVO;
import com.linkcircle.boss.module.crm.enums.BillStatusEnum;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import com.linkcircle.boss.module.crm.enums.PaymentMethodEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.github.cloud.framework.common.constant.TimeConstants;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-06-25 17:40
 * @description 成本账单持久化服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CostBillPersistenceServiceImpl implements CostBillPersistenceService {

    private final PrepaidCostBillDetailMapper prepaidCostBillDetailMapper;
    private final PostpaidCostBillDetailMapper postpaidCostBillDetailMapper;
    private final ScaleDataService scaleDataService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCostDetailBill(String billId, CostDetailBillRequestContext requestContext) {
        log.info("保存成本账单明细, billId: {}", billId);

        try {
            // 1. 获取上下文数据
            CostBillDetailRequestDTO requestParams = requestContext.getCostBillMqDTO().getRequestParams();
            Map<String, Object> businessData = requestParams.getData();
            ResourceSubscriptionInfoDTO subscriptionInfo = requestContext.getResourceSubscriptionInfoDTO();
            Integer chargeType = subscriptionInfo.getDetail().getChargeType();
            if (ChargeRateTypeEnum.USAGE.getType().equals(chargeType) || ChargeRateTypeEnum.PACKAGE.getType().equals(chargeType)) {
                // 量表id
                Long scaleId = subscriptionInfo.getDetail().getScaleId();

                Optional<Map<String, Object>> businessParamsOpt = scaleDataService.buildMatchBusinessParams(businessData, scaleId);

                // 如果有匹配的业务参数，设置到ThreadLocal上下文
                if (businessParamsOpt.isPresent() && !businessParamsOpt.get().isEmpty()) {
                    BusinessParamsContext.setBusinessParams(businessParamsOpt.get());
                    log.info("设置业务参数到ThreadLocal，参数数量: {}", businessParamsOpt.get().size());
                }
            }

            // 2. 判断是预付费还是后付费
            boolean isPrepaid = requestContext.isPrepared();

            // 4. 获取服务编码用于分片
            String serviceCode = getServiceCode(requestContext);
            Long businessTime = requestParams.getBusinessTime();
            HitBusinessTimeDTO businessTimeDTO = HitBusinessTimeDTO.build(businessTime, List.of(serviceCode));

            try (HintManager hintManager = HintManager.getInstance()) {
                if (isPrepaid) {
                    // 保存预付费成本账单
                    hintManager.addTableShardingValue(LogicTableConstant.PREPAID_COST_BILL_DETAIL, businessTimeDTO);
                    PrepaidCostBillDetailDO detailDO = buildPrepaidCostBillDetailDO(billId, requestContext);
                    int insert = prepaidCostBillDetailMapper.insert(detailDO);
                    log.info("保存预付费成本账单成功, billId: {}, billDetailId: {}, insert: {}",
                            billId, detailDO.getBillDetailId(), insert);
                    publishUsageStatsEvent(detailDO, insert > 0);
                    return insert > 0;
                }
                // 保存后付费成本账单
                hintManager.addTableShardingValue(LogicTableConstant.POSTPAID_COST_BILL_DETAIL, businessTimeDTO);
                PostpaidCostBillDetailDO detailDO = buildPostpaidCostBillDetailDO(billId, requestContext);
                int insert = postpaidCostBillDetailMapper.insert(detailDO);
                log.info("保存后付费成本账单成功, billId: {}, billDetailId: {}, insert: {}",
                        billId, detailDO.getBillDetailId(), insert);
                publishUsageStatsEvent(detailDO, insert > 0);
                return insert > 0;
            } catch (Exception e) {
                log.error("保存成本账单失败, billId: {}", billId, e);
                throw e;
            }
        } finally {
            BusinessParamsContext.clear();
        }
    }

    private void publishUsageStatsEvent(PostpaidCostBillDetailDO detailDO, boolean success) {
        if (!success) {
            return;
        }
        String billDate = DateTimeFormatter.ofPattern(TimeConstants.DATE_FORMAT_YYYYMMDDHH)
                .format(LocalDateTime.ofInstant(Instant.ofEpochMilli(detailDO.getBusinessTime()), ZoneId.of(TimeConstants.TIMEZONE_UTC)));
        SupplierUsageStatsEvent supplierUsageStatsEvent = SupplierUsageStatsEvent.create(this,
                detailDO.getAccountId(),
                detailDO.getResourceServiceId(),
                billDate,
                detailDO.getServiceCode(),
                detailDO.getBillingType(),
                detailDO.getSupplierId(),
                detailDO.getChargeUsageCount(),
                detailDO.getUsageCount(),
                detailDO.getCashAmount(),
                detailDO.getPointAmount()
        );
        SpringUtil.publishEvent(supplierUsageStatsEvent);
    }

    private void publishUsageStatsEvent(PrepaidCostBillDetailDO detailDO, boolean success) {
        if (!success) {
            return;
        }
        String billDate = DateTimeFormatter.ofPattern(TimeConstants.DATE_FORMAT_YYYYMMDDHH)
                .format(LocalDateTime.ofInstant(Instant.ofEpochMilli(detailDO.getBusinessTime()), ZoneId.of(TimeConstants.TIMEZONE_UTC)));
        SupplierUsageStatsEvent supplierUsageStatsEvent = SupplierUsageStatsEvent.create(this,
                detailDO.getAccountId(),
                detailDO.getResourceServiceId(),
                billDate,
                detailDO.getServiceCode(),
                detailDO.getBillingType(),
                detailDO.getSupplierId(),
                detailDO.getChargeUsageCount(),
                detailDO.getUsageCount(),
                detailDO.getCashAmount(),
                detailDO.getPointAmount()
        );
        SpringUtil.publishEvent(supplierUsageStatsEvent);
    }

    private String getServiceCode(CostDetailBillRequestContext requestContext) {
        ResourcePurchaseVO.Detail detail = requestContext.getResourceSubscriptionInfoDTO().getDetail();
        Integer chargeType = detail.getChargeType();
        ChargeRateTypeEnum rateTypeEnum = ChargeRateTypeEnum.getByType(chargeType);
        switch (rateTypeEnum) {
            case FIXED, TIERED -> {
                return rateTypeEnum.name().toLowerCase();
            }
            case USAGE, PACKAGE -> {
                return detail.getResourceServiceCode();
            }
        }
        return "";
    }

    /**
     * 构建预付费成本账单明细DO
     */
    private PrepaidCostBillDetailDO buildPrepaidCostBillDetailDO(String billId, CostDetailBillRequestContext requestContext) {
        CostBillDetailRequestDTO requestParams = requestContext.getCostBillMqDTO().getRequestParams();
        ResourceSubscriptionInfoDTO subscriptionInfo = requestContext.getResourceSubscriptionInfoDTO();
        ResourcePurchaseVO purchaseVO = subscriptionInfo.getPurchaseVO();
        ResourcePurchaseVO.Detail detail = subscriptionInfo.getDetail();
        CostRateChargeResponse chargeResponse = requestContext.getCostRateChargeResponse();

        // 生成账单明细ID
        PrepaidCostBillDetailDO detailDO = PrepaidCostBillDetailDO.builder()
                .billDetailId(Long.valueOf(billId))
                .businessId(requestParams.getBusinessId())
                .requestId(requestParams.getRequestId())
                .businessTime(requestParams.getBusinessTime())
                .resourceServiceId(detail.getResourceServiceId())
                .serviceCode(detail.getResourceServiceCode())
                .supplierId(purchaseVO.getSuppliersId())
                .accountId(detail.getAccountId())
                .entityId(purchaseVO.getEntityId())
                .purchaseId(purchaseVO.getId())
                .usageCount(chargeResponse.getUsage())
                .usageUnit(chargeResponse.getUsageUnit())
                .chargeUsageCount(chargeResponse.getChargeUnitCount())
                .chargeMeasure(chargeResponse.getMeasure())
                .chargeMeasureUnit(chargeResponse.getMeasureUnit())
                .chargeMeasureCeil(chargeResponse.getMeasureCeil())
                .paymentType(requestContext.getPaymentType())
                .paymentMethod(detail.getPaymentOptions())
                .billingType(detail.getChargeType())
                .billStatus(BillStatusEnum.DRAFT.getStatus())
                .pointAmount(chargeResponse.getPointAmount())
                .cashAmount(chargeResponse.getCashAmount())
                .taxRate(chargeResponse.getTaxRate())
                .amountWithTax(chargeResponse.getAmountWithTax())
                .amountWithoutTax(chargeResponse.getAmountWithoutTax())
                .inTrial(chargeResponse.getInTrial())
                .chargeUnitCount(chargeResponse.getChargeUnitCount())
                .originalUnitPrice(chargeResponse.getOriginalUnitPrice())
                .originalPrice(chargeResponse.getOriginalPrice())
                .currency(chargeResponse.getCurrency())
                .createTime(System.currentTimeMillis())
                .billingTime(System.currentTimeMillis())
                .deleted(false)
                .rateDetails(JsonUtils.toJsonString(chargeResponse.getRateConfig()))
                .build();
        Integer paymentMethod = detailDO.getPaymentMethod();
        if (PaymentMethodEnum.CASH.getMethod().equals(paymentMethod)) {
            detailDO.setCashAmount(detailDO.getOriginalPrice());
        } else {
            detailDO.setPointAmount(detailDO.getOriginalPrice());
        }
        return detailDO;
    }

    /**
     * 构建后付费成本账单明细DO
     */
    private PostpaidCostBillDetailDO buildPostpaidCostBillDetailDO(String billId, CostDetailBillRequestContext requestContext) {
        CostBillDetailRequestDTO requestParams = requestContext.getCostBillMqDTO().getRequestParams();
        ResourceSubscriptionInfoDTO subscriptionInfo = requestContext.getResourceSubscriptionInfoDTO();
        ResourcePurchaseVO.Detail detail = subscriptionInfo.getDetail();
        ResourcePurchaseVO purchaseVO = subscriptionInfo.getPurchaseVO();
        CostRateChargeResponse chargeResponse = requestContext.getCostRateChargeResponse();

        PostpaidCostBillDetailDO detailDO = PostpaidCostBillDetailDO.builder()
                .billDetailId(Long.valueOf(billId))
                .businessId(requestParams.getBusinessId())
                .requestId(requestParams.getRequestId())
                .businessTime(requestParams.getBusinessTime())
                .resourceServiceId(detail.getResourceServiceId())
                .serviceCode(detail.getResourceServiceCode())
                .supplierId(purchaseVO.getSuppliersId())
                .accountId(detail.getAccountId())
                .entityId(purchaseVO.getEntityId())
                .purchaseId(purchaseVO.getId())
                .usageCount(chargeResponse.getUsage())
                .usageUnit(chargeResponse.getUsageUnit())
                .chargeUsageCount(chargeResponse.getChargeUnitCount())
                .chargeMeasure(chargeResponse.getMeasure())
                .chargeMeasureUnit(chargeResponse.getMeasureUnit())
                .chargeMeasureCeil(chargeResponse.getMeasureCeil())
                .paymentType(requestContext.getPaymentType())
                .paymentMethod(detail.getPaymentOptions())
                .billingType(detail.getChargeType())
                .billStatus(BillStatusEnum.DRAFT.getStatus())
                .taxRate(chargeResponse.getTaxRate())
                .amountWithTax(chargeResponse.getAmountWithTax())
                .amountWithoutTax(chargeResponse.getAmountWithoutTax())
                .inTrial(chargeResponse.getInTrial())
                .pointAmount(chargeResponse.getPointAmount())
                .cashAmount(chargeResponse.getCashAmount())
                .chargeUnitCount(chargeResponse.getChargeUnitCount())
                .originalUnitPrice(chargeResponse.getOriginalUnitPrice())
                .originalPrice(chargeResponse.getOriginalPrice())
                .originalPrice(chargeResponse.getOriginalPrice())
                .currency(chargeResponse.getCurrency())
                .createTime(System.currentTimeMillis())
                .billingTime(System.currentTimeMillis())
                .deleted(false)
                .rateDetails(JsonUtils.toJsonString(chargeResponse.getRateConfig()))
                .build();
        Integer paymentMethod = detailDO.getPaymentMethod();
        if (PaymentMethodEnum.CASH.getMethod().equals(paymentMethod)) {
            detailDO.setCashAmount(detailDO.getOriginalPrice());
        } else {
            detailDO.setPointAmount(detailDO.getOriginalPrice());
        }
        return detailDO;
    }
}
