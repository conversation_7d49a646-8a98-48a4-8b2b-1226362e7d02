package com.linkcircle.boss.module.report.web.bill.service.impl;

import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.framework.common.util.pdf.Maps;
import com.linkcircle.boss.framework.excel.core.util.ExcelUtils;
import com.linkcircle.boss.framework.tanant.TenantUtils;
import com.linkcircle.boss.module.crm.api.currency.CurrencyApi;
import com.linkcircle.boss.module.report.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.report.enums.DownLoadEnum;
import com.linkcircle.boss.module.report.enums.FinancialEnum;
import com.linkcircle.boss.module.report.utils.TimeUtils;
import com.linkcircle.boss.module.report.web.bill.convert.SupplierAccountBillConvert;
import com.linkcircle.boss.module.report.web.bill.mapper.SupplierAccountBillDorisMapper;
import com.linkcircle.boss.module.report.web.bill.model.dto.makeup.BillQueryPageReqDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.supplier.SupplerAccountBillDetailReqDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.supplier.SupplerAccountMonthBillDetailReqDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.supplier.SupplerAccountMonthBillReqDTO;
import com.linkcircle.boss.module.report.web.bill.model.export.bill.postpaid.PostpaidBillExportVO;
import com.linkcircle.boss.module.report.web.bill.model.export.bill.postpaid.PostpaidBillExportVOConvert;
import com.linkcircle.boss.module.report.web.bill.model.export.supplier.SupplierAccountBillDetailExportVO;
import com.linkcircle.boss.module.report.web.bill.model.export.supplier.SupplierAccountMonthBillDetailExportVO;
import com.linkcircle.boss.module.report.web.bill.model.export.supplier.SupplierAccountMonthBillExportVO;
import com.linkcircle.boss.module.report.web.bill.model.vo.makeup.MakeupIncomeBillPageVo;
import com.linkcircle.boss.module.report.web.bill.model.vo.postpaid.PostpaidIncomeProductBillDetailVO;
import com.linkcircle.boss.module.report.web.bill.model.vo.supplier.SupplerAccountBillGroupVO;
import com.linkcircle.boss.module.report.web.bill.model.vo.supplier.SupplierAccountBillDetailRespVO;
import com.linkcircle.boss.module.report.web.bill.model.vo.supplier.SupplierAccountMonthBillDetailRespVO;
import com.linkcircle.boss.module.report.web.bill.model.vo.supplier.SupplierAccountMonthBillRespVO;
import com.linkcircle.boss.module.report.web.bill.service.CurrencyService;
import com.linkcircle.boss.module.report.web.bill.service.SupplierAccountBillService;
import com.linkcircle.boss.module.report.web.download.model.dto.OnlineExportEnhanceDTO;
import com.linkcircle.boss.module.report.web.download.model.dto.OnlineExportListDTO;
import com.linkcircle.boss.module.report.web.download.service.DownLoadService;
import com.linkcircle.boss.module.report.web.financial.service.FunctionService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/12 17:47
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SupplierAccountBillServiceImpl implements SupplierAccountBillService {


    private final SupplierAccountBillDorisMapper dorisMapper;
    private final DownLoadService downLoadService;
    private final CurrencyService currencyService;


    @Override
    public List<SupplierAccountMonthBillRespVO> queryMonthBill(SupplerAccountMonthBillReqDTO monthBillReqDTO) {
        Map<String, Object> dateMap = new Maps<String, Object>()
                .with("billYear", monthBillReqDTO.getBillYear())
                .with("nextYear", TimeUtils.nextYearStr(monthBillReqDTO.getBillYear(), TimeUtils.FORMAT_YYYY))
                .with("createTimeMin", TimeUtils.transToUTC(TimeUtils.beginOfYear(monthBillReqDTO.getBillYear(), TimeUtils.FORMAT_YYYY)).getTime())
                .with("createTimeMax", TimeUtils.transToUTC(TimeUtils.endOfYear(monthBillReqDTO.getBillYear(), TimeUtils.FORMAT_YYYY)).getTime())
                .toMap();

        List<SupplierAccountMonthBillRespVO> billList = TenantUtils.executeIgnore(() -> dorisMapper.queryMonthBill(monthBillReqDTO, dateMap));
        if (CollectionUtils.isNotEmpty(billList)) {
            billList.forEach(bill -> {
                bill.setSupplierId(monthBillReqDTO.getSupplierId().toString());
                bill.setAccountId(monthBillReqDTO.getAccountId().toString());
            });
            billList.sort(Comparator.comparing(SupplierAccountMonthBillRespVO::getBillMonth));
        }
        return billList;
    }

    @Override
    public List<SupplerAccountBillGroupVO<SupplierAccountMonthBillDetailRespVO>> queryMonthBillDetail(SupplerAccountMonthBillDetailReqDTO monthBillReqDTO) {

        Map<String, Object> dateMap = new Maps<String, Object>()
                .with("billMonth", monthBillReqDTO.getBillMonth())
                .with("nextMonth", TimeUtils.nextMonth(monthBillReqDTO.getBillMonth(), TimeUtils.FORMAT_YYYYMM))
                .with("createTimeMin", TimeUtils.transToUTC(TimeUtils.beginOfMonth(monthBillReqDTO.getBillMonth(), TimeUtils.FORMAT_YYYYMM)).getTime())
                .with("createTimeMax", TimeUtils.transToUTC(TimeUtils.endOfMonth(monthBillReqDTO.getBillMonth(), TimeUtils.FORMAT_YYYYMM)).getTime())
                .toMap();
        List<SupplierAccountMonthBillDetailRespVO> detailList = TenantUtils.executeIgnore(() -> dorisMapper.queryMonthBillDetail(monthBillReqDTO, dateMap));
        FunctionService.of(FinancialEnum.BillColumn.RESOURCE).handle(detailList, SupplierAccountMonthBillDetailRespVO::getResourceId, SupplierAccountMonthBillDetailRespVO::setResourceName);
        FunctionService.of(FinancialEnum.BillColumn.RESOURCE_SERVICE).handle(detailList, SupplierAccountMonthBillDetailRespVO::getResourceServiceId, SupplierAccountMonthBillDetailRespVO::setResourceServiceName);
        // 关联资源表 得到 资源名称和资源服务名称
        if (CollectionUtils.isNotEmpty(detailList)) {
            return detailList.stream().collect(Collectors.groupingBy(SupplierAccountMonthBillDetailRespVO::getResourceId))
                    .values().stream().map(xs -> {
                        SupplerAccountBillGroupVO<SupplierAccountMonthBillDetailRespVO> groupVO = new SupplerAccountBillGroupVO<>();
                        groupVO.setResourceId(xs.getFirst().getResourceId());
                        groupVO.setResourceName(xs.getFirst().getResourceName());
                        xs.sort(Comparator.comparing(SupplierAccountMonthBillDetailRespVO::getResourceServiceId));
                        groupVO.setDetailList(xs);
                        return groupVO;
                    }).sorted(Comparator.comparing(SupplerAccountBillGroupVO::getResourceId)).toList();
        }
        return List.of();
    }

    @Override
    public List<SupplerAccountBillGroupVO<SupplierAccountBillDetailRespVO>> queryBillDetail(SupplerAccountBillDetailReqDTO reqDTO) {
        Map<String, Object> dateMap = new Maps<String, Object>()
                .with("billDate", reqDTO.getBillMonth())
                .with("nextDate", TimeUtils.nextDay(reqDTO.getBillMonth(), TimeUtils.FORMAT_YYYYMMDD))
                .with("createTimeMin", TimeUtils.transToUTC(TimeUtils.beginOfDay(reqDTO.getBillMonth(), TimeUtils.FORMAT_YYYYMMDD)).getTime())
                .with("createTimeMax", TimeUtils.transToUTC(TimeUtils.endOfDay(reqDTO.getBillMonth(), TimeUtils.FORMAT_YYYYMMDD)).getTime())
                .toMap();
        List<SupplierAccountBillDetailRespVO> details = TenantUtils.executeIgnore(() -> dorisMapper.listBillDetail(reqDTO, dateMap));
        FunctionService.of(FinancialEnum.BillColumn.RESOURCE).handle(details, SupplierAccountBillDetailRespVO::getResourceId, SupplierAccountBillDetailRespVO::setResourceName);
        FunctionService.of(FinancialEnum.BillColumn.RESOURCE_SERVICE).handle(details, SupplierAccountBillDetailRespVO::getResourceServiceId, SupplierAccountBillDetailRespVO::setResourceServiceName);
        // 关联资源表 得到 资源名称和资源服务名称
        if (CollectionUtils.isNotEmpty(details)) {
            return details.stream().collect(Collectors.groupingBy(SupplierAccountBillDetailRespVO::getResourceId))
                    .values().stream().map(xs -> {
                        SupplerAccountBillGroupVO<SupplierAccountBillDetailRespVO> groupVO = new SupplerAccountBillGroupVO<>();
                        groupVO.setResourceId(xs.getFirst().getResourceId());
                        groupVO.setResourceName(xs.getFirst().getResourceName());
                        xs.sort(Comparator.comparing(SupplierAccountBillDetailRespVO::getResourceServiceId));
                        groupVO.setDetailList(xs);
                        return groupVO;
                    }).sorted(Comparator.comparing(SupplerAccountBillGroupVO::getResourceId)).toList();
        }
        return List.of();
    }

    @Override
    public void exportMonthBill(SupplerAccountMonthBillReqDTO monthBillReqDTO, HttpServletResponse response) {
        try {
            OnlineExportListDTO<SupplerAccountMonthBillReqDTO, SupplierAccountMonthBillRespVO, SupplierAccountMonthBillExportVO> online = new OnlineExportListDTO<>();
            online.fileName("供应商-月度账单")
                    .sheetName("账单")
                    .exportClass(SupplierAccountMonthBillExportVO.class)
                    .response(response)
                    .req(monthBillReqDTO)
                    .queryFunc(this::queryMonthBill)
                    .convertFun(reps -> {
                        Map<String, String> currencyMap = currencyService.currencyMap();
                        return CollectionUtils.isNotEmpty(reps) ? reps.stream().map(t -> {
                            SupplierAccountMonthBillExportVO exportVO = SupplierAccountBillConvert.INSTANCE.convert(t);
                            exportVO.setCashAmountInfo(combineValueAndUnit(t.getCashAmount(), currencyMap.getOrDefault(t.getCurrencyCode(), t.getCurrencyCode())));
                            return exportVO;
                        }).toList() : List.of();
                    });
            downLoadService.exportOnline(online);
        } catch (Exception e) {
            log.error("导出供应商月度账单出错", e);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SUPPLIER__MONTH_BILL_EXPORT_FAILED);
        }
    }

    @Override
    public void exportMonthBillDetail(SupplerAccountMonthBillDetailReqDTO monthBillReqDTO, HttpServletResponse response) {
        List<SupplerAccountBillGroupVO<SupplierAccountMonthBillDetailRespVO>> monthBillList = queryMonthBillDetail(monthBillReqDTO);
        try {
            ExcelUtils.write(response, "供应商-月度账单-账单详情.xls", "账单详情", SupplierAccountMonthBillDetailExportVO.class,
                    CollectionUtils.isNotEmpty(monthBillList) ? monthBillList.stream().flatMap(t -> t.getDetailList().stream())
                            .map(t -> {
                                SupplierAccountMonthBillDetailExportVO exportVO = SupplierAccountBillConvert.INSTANCE.convert(t);
                                exportVO.setUsageInfo(combineValueAndUnit(t.getUsageCount(), t.getUsageUnit()));
                                exportVO.setCashAmountInfo(combineValueAndUnit(t.getCashAmount(), t.getCurrencyCode()));
                                return exportVO;
                            }).toList() : List.of()
            );
        } catch (Exception e) {
            log.error("导出供应商月度账单详情出错", e);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SUPPLIER__MONTH_BILL_DETAIL_EXPORT_FAILED);
        }
    }

    private <T extends Number> String combineValueAndUnit(T value, String unit) {
        String unitStr = StringUtils.isBlank(unit) ? "" : unit;
        if (value == null) {
            return 0 + unitStr;
        } else {
            if (value instanceof BigDecimal) {
                return ((BigDecimal) value).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + unitStr;
            } else {
                return value + unitStr;
            }
        }
    }


    @Override
    public void exportBillDetail(SupplerAccountBillDetailReqDTO reqDTO, HttpServletResponse response) {
        try {
            OnlineExportListDTO<SupplerAccountBillDetailReqDTO, SupplerAccountBillGroupVO<SupplierAccountBillDetailRespVO>, SupplierAccountBillDetailExportVO> online = new OnlineExportListDTO<>();
            online.fileName("供应商-费用明细")
                    .sheetName("费用明细")
                    .exportClass(SupplierAccountBillDetailExportVO.class)
                    .response(response)
                    .req(reqDTO)
                    .queryFunc(this::queryBillDetail)
                    .convertFun(reps -> {
                        Map<String, String> currencyMap = currencyService.currencyMap();
                        return CollectionUtils.isNotEmpty(reps) ? reps.stream().flatMap(t -> t.getDetailList().stream())
                                .map(t -> {
                                    SupplierAccountBillDetailExportVO exportVO = SupplierAccountBillConvert.INSTANCE.convert(t);
                                    exportVO.setConsumerPeriod(convertToConsumerPeriod(t.getBillDate()));
                                    exportVO.setUsageInfo(combineValueAndUnit(t.getUsageCount(), t.getUsageUnit()));
                                    exportVO.setCashAmountInfo(combineValueAndUnit(t.getCashAmount(), currencyMap.getOrDefault(t.getCurrencyCode(), t.getCurrencyCode())));
                                    return exportVO;
                                }).toList() : List.of();


                    });
            downLoadService.exportOnline(online);
        } catch (Exception e) {
            log.error("导出供应商账单明细出错", e);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SUPPLIER_BILL_DETAIL_EXPORT_FAILED);
        }
    }

    private String convertToConsumerPeriod(String billDate) {
        if (StringUtils.isBlank(billDate)) {
            return "00:00~00:59";
        } else {
            try {
                //yyyyMMddHH
                String hour = billDate.substring(8, 10);
                int hourInt = Integer.parseInt(hour);
                return String.format("%02d:00~%02d:59", hourInt, hourInt);
            } catch (Exception e) {
                log.error("转换账单时间出错:" + billDate, e);
                return "00:00~00:59";
            }
        }
    }
}
