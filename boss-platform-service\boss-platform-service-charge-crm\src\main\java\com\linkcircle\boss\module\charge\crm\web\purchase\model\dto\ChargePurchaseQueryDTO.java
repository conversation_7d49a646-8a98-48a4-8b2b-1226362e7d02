package com.linkcircle.boss.module.charge.crm.web.purchase.model.dto;

import com.linkcircle.boss.framework.common.model.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 资源服务基本信息分页查询参数
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "采购基本信息查询参数")
public class ChargePurchaseQueryDTO extends PageParam {


    @Schema(description = "资源服务id")
    private Long resourceServiceId;

    @Schema(description = "采购id")
    private Long purchaseId;


    @Schema(description = "状态，0：已结束，1：已生效，2：试用中，3：已取消")
    private Integer status;

    @Schema(description = "供应商id")
    private Long suppliersId;

    @Schema(description = "供应商账户id")
    private Long accountId;

    @Schema(description = "主体ID")
    private Long entityId;

    @Schema(description = "创建开始时间")
    private Long startTime;

    @Schema(description = "创建结束时间")
    private Long endTime;

    @Schema(description = "合同ids数组")
    private List<Long> contractIds;


}
