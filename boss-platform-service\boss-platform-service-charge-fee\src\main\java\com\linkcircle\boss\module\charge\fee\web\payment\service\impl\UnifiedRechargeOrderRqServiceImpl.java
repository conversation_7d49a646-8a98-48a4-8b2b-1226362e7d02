package com.linkcircle.boss.module.charge.fee.web.payment.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.charge.fee.config.UnifiedRechargeConfig;
import com.linkcircle.boss.module.charge.fee.web.payment.mapper.UnifiedRechargeOrderRqMapper;
import com.linkcircle.boss.module.charge.fee.web.payment.model.dto.UnifiedRechargeCallbackDTO;
import com.linkcircle.boss.module.charge.fee.web.payment.model.entity.UnifiedRechargeOrderRq;
import com.linkcircle.boss.module.charge.fee.web.payment.service.IUnifiedRechargeOrderRqService;
import com.linkcircle.boss.module.charge.fee.web.payment.util.PayKit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.InetAddress;
import java.util.Calendar;

/**
 * <p>
 * 统一充值下单请求 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Service
@Slf4j
public class UnifiedRechargeOrderRqServiceImpl extends ServiceImpl<UnifiedRechargeOrderRqMapper, UnifiedRechargeOrderRq> implements IUnifiedRechargeOrderRqService {

    @Autowired
    private UnifiedRechargeConfig unifiedRechargeConfig;

    @Override
    @Transactional
    public void testSave() {
        UnifiedRechargeOrderRq unifiedRechargeOrderRq = UnifiedRechargeOrderRq.builder()
                .clientIp("")
                .amount(1000L)
                .body(unifiedRechargeConfig.getBody())
                .currency(unifiedRechargeConfig.getCurrency())
                .isvId(unifiedRechargeConfig.getIsvId())
                .mchNo("")
                .mchOrderNo("UNIFIEDPAY-" + IdUtil.fastSimpleUUID())
                .reqTime(Calendar.getInstance().getTimeInMillis())
                .reqTimezone("8")
                .version(unifiedRechargeConfig.getVersion())
                .wayCode(unifiedRechargeConfig.getWayCode())
                .notifyUrl(unifiedRechargeConfig.getNotifyUrl())
                .expiredTime(3600)
                .signType("MD5")
                .subject("客户充值").build();
        this.save(unifiedRechargeOrderRq);
    }

    @Override
    @Transactional
    public CommonResult<JSONObject> unifiedOrder(Long customerId, Long amount,
                                                 String clientIp, Long walletId, String reqTimeZone) throws Exception {
        InetAddress inetAddress = InetAddress.getLocalHost();
        String hostAddress = inetAddress.getHostAddress();
        // 前端未提供客户端IP地址 使用本机IP
        if (StringUtils.isEmpty(clientIp)) {
            clientIp = hostAddress;
        }

        UnifiedRechargeOrderRq unifiedRechargeOrderRq = UnifiedRechargeOrderRq.builder()
                .clientIp(clientIp)
                .amount(amount)
                .body(unifiedRechargeConfig.getBody())
                .currency(unifiedRechargeConfig.getCurrency())
                .isvId(unifiedRechargeConfig.getIsvId())
                .mchNo(customerId + "")
                .mchOrderNo("UNIFIEDPAY-" + IdUtil.fastSimpleUUID())
                .reqTime(Calendar.getInstance().getTimeInMillis())
                .reqTimezone(reqTimeZone)
                .version(unifiedRechargeConfig.getVersion())
                .wayCode(unifiedRechargeConfig.getWayCode())
                .notifyUrl(unifiedRechargeConfig.getNotifyUrl())
                .expiredTime(3600)
                .signType("MD5")
                .subject("客户充值").build();

        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(unifiedRechargeOrderRq));
        //商户秘钥
//        String key = "s3r1z+jpi9NG06i7ppm+pw==";
        //（签名规则：去除请求体重的sign和requestId,后将请求字段循环MD5）
        String sign = PayKit.getSign(jsonObject, unifiedRechargeConfig.getSignKey());
        unifiedRechargeOrderRq.setSign(sign);

        String json = JSONUtil.toJsonPrettyStr(unifiedRechargeOrderRq);
        log.info("创建支付订单请求参数: " + json);
        String response = HttpUtil.post(unifiedRechargeConfig.getUrl(), json);
        log.info("创建支付订单响应结果: " + JSONUtil.toJsonPrettyStr(response));

        if (StringUtils.isEmpty(response)) {
            return CommonResult.error(500, "创建支付订单响应结果为空");
        }

        JSONObject responseJSON = JSONObject.parseObject(response);

        Integer code = responseJSON.getInteger("code");
        if (code != null && code == 0) {
            JSONObject data = responseJSON.getJSONObject("data");
            String payOrderId = data.getString("payOrderId");
            Integer orderState = data.getInteger("orderState");
            // 表示响应成功
            unifiedRechargeOrderRq.setWalletsId(walletId);
            unifiedRechargeOrderRq.setPayOrderId(payOrderId);
            unifiedRechargeOrderRq.setStatus(orderState);
            boolean save = this.save(unifiedRechargeOrderRq);
            if (!save) {
                throw new ServiceException(500, "统一充值下单保存失败");
            }
            return CommonResult.success(responseJSON);
        } else {
            return CommonResult.error(400, response);
        }
    }

    @Override
    public CommonResult<Long> unifiedPayCallback(UnifiedRechargeCallbackDTO unifiedRechargeCallbackDTO) {
        // 根据
        String mchOrderNo = unifiedRechargeCallbackDTO.getMchOrderNo();

        boolean update = lambdaUpdate().eq(UnifiedRechargeOrderRq::getMchOrderNo, mchOrderNo)
                .set(UnifiedRechargeOrderRq::getStatus, unifiedRechargeCallbackDTO.getState())
                .set(UnifiedRechargeOrderRq::getChannelOrderNo, unifiedRechargeCallbackDTO.getChannelOrderNo())
                .set(UnifiedRechargeOrderRq::getPayOrderId, unifiedRechargeCallbackDTO.getPayOrderId())
                .set(UnifiedRechargeOrderRq::getMchFeeAmount, unifiedRechargeCallbackDTO.getMchFeeAmount())
                .update();

        if (!update) {
            throw new ServiceException(500, "统一充值回调更新失败");
        }

        return CommonResult.success();
    }

    public static void main(String[] args) throws Exception {
        InetAddress inetAddress = InetAddress.getLocalHost();
        String hostAddress = inetAddress.getHostAddress();


        UnifiedRechargeOrderRq unifiedRechargeOrderRq = UnifiedRechargeOrderRq.builder().clientIp(hostAddress)
                .amount(1000L)
                .body("客户充值")
                .currency("SGD")
                .isvId("10000000000000001")
                .mchNo("9999999999")
                .mchOrderNo("UNIFIEDPAY" + IdUtil.fastSimpleUUID())
                .reqTime(Calendar.getInstance().getTimeInMillis())
                .reqTimezone("8")
                .version("1.0")
                .wayCode("ALLDEBIT_PN")
                .notifyUrl("")
                .expiredTime(3600)
                .signType("MD5")
                .subject("客户充值").build();

        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(unifiedRechargeOrderRq));
        //商户秘钥
        String key = "s3r1z+jpi9NG06i7ppm+pw==";
        //（签名规则：去除请求体重的sign和requestId,后将请求字段循环MD5）
        String sign = PayKit.getSign(jsonObject, key);
        unifiedRechargeOrderRq.setSign(sign);

        String json = JSONUtil.toJsonPrettyStr(unifiedRechargeOrderRq);
        System.out.println("创建支付订单请求参数: " + json);
        String response = HttpUtil.post("https://linkasia.linkcircle.com/payCallback/api/pay/unifiedOrder", json);
        System.out.println("创建支付订单响应结果: " + JSONUtil.toJsonPrettyStr(response));
    }

}
