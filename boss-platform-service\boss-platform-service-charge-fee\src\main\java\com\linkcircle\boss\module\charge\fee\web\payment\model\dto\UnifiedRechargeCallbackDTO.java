package com.linkcircle.boss.module.charge.fee.web.payment.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统一支付充值
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UnifiedRechargeCallbackDTO {

    /**
     * 商户手续费金额
     */
    @Schema(description = "商户手续费金额")
    private Integer mchFeeAmount;

    /**
     * 支付金额,单位分
     */
    @Schema(description = "支付金额,单位分")
    private Long amount;

    /**
     * 支付系统订单号
     */
    @Schema(description = "支付系统订单号")
    private String payOrderId;

    /**
     * 商户扩展参数,回调时会原样返回
     */
    @Schema(description = "商户扩展参数,回调时会原样返回")
    private String extParam;

    /**
     * 业务生成的订单号
     */
    @Schema(description = "业务生成的订单号")
    private String mchOrderNo;

    /**
     * 商品标题
     */
    @Schema(description = "商品标题")
    private String subject;

    /**
     * 汇率
     */
    @Schema(description = "汇率")
    private Integer exRate;

    /**
     * 支付方式代码
     */
    @Schema(description = "支付方式代码")
    private String wayCode;

    /**
     * 签名值
     */
    @Schema(description = "签名值")
    private String sign;

    /**
     * 上游渠道订单号
     */
    @Schema(description = "上游渠道订单号")
    private String channelOrderNo;

    /**
     * 请求时间戳
     */
    @Schema(description = "请求时间戳")
    private Long reqTime;

    /**
     * 商品描述信息
     */
    @Schema(description = "商品描述信息")
    private String body;

    /**
     * 客户端IP地址
     */
    @Schema(description = "客户端IP地址")
    private String clientIp;

    /**
     * 货币类型
     */
    @Schema(description = "货币类型")
    private String currency;

    /**
     * 服务商ID
     */
    @Schema(description = "服务商ID")
    private String isvId;

    /**
     * 支付状态: 0-订单生成, 1-支付中, 2-支付成功, 3-支付失败, 4-已撤销, 5-已退款, 6-订单关闭
     */
    @Schema(description = "支付状态: 0-订单生成, 1-支付中, 2-支付成功, 3-支付失败, 4-已撤销, 5-已退款, 6-订单关闭")
    private Integer state;

    /**
     * 接口代码
     */
    @Schema(description = "接口代码")
    private String intfCode;

}
